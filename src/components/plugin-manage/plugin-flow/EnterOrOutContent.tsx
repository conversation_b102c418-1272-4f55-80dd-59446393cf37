import React from 'react';
import { get, map, includes, find, concat, filter } from 'lodash';
import { formatPluginInfo } from './usePluginFlow';
import AiPluginInfoContent from './components/AiPluginInfoContent';
import AiAddPlugin from '~/components/api-manage/apiAi/policiesPlugins/plugin/AiAddPlugin';

const EnterOrOutContent = ({
  type = 'InBound',
  isAll,
  environmentId,
  pluginList,
  onRefresh,
  httpApiInfo,
  resourceInfo,
  sceneType,
  currentGatewayInfo,
  currentPublishEnv,
}) => {
  const apiEnvInfo = find(
    get(httpApiInfo, 'environments', []),
    (item) => item.environmentId === environmentId,
  );

  const attachResourceType =
    get(httpApiInfo, 'type') === 'LLM'
      ? 'LLMApi'
      : sceneType === 'McpApi'
        ? 'GatewayRoute'
        : 'HttpApi';

  const domains = sceneType === 'McpApi' ?
    concat(get(httpApiInfo, 'domainInfos', []), get(httpApiInfo, 'environmentInfo.subDomains', []))
    : concat(get(apiEnvInfo, 'customDomains', []), get(apiEnvInfo, 'subDomains', []));

  if (sceneType === 'McpApi') {
    pluginList = filter(pluginList, item => {
      return item.className !== "mcp-server";
    })
  }
  return (
    <div style={{ width: 345 }} className={type === 'InBound' ? 'enter-content' : 'out-content'}>
      {map(pluginList, (pluginInfo) => {
        const { enable, httpRewriteInitData } = formatPluginInfo({
          pluginData: pluginInfo,
          httpApiInfo,
          resourceInfo,
          domains,
          isAll
        });

        return (
          <React.Fragment>
            <div className="content-direction"></div>
            <div className="space-between pt-8 pb-8" style={{ borderBottom: '1px solid #DBDBDB' }}>
              {includes(['AiApi', 'LLMApi', 'McpApi'], sceneType) && (
                <AiPluginInfoContent
                  pluginInfo={{
                    ...pluginInfo,
                    isEnable: enable,
                    sceneType,
                    attachResourceName: get(resourceInfo, 'name'),
                  }}
                  currentPublishEnv={currentPublishEnv}
                  environmentId={environmentId}
                  currentGatewayInfo={currentGatewayInfo}
                  httpRewriteInitData={httpRewriteInitData}
                  onRefresh={onRefresh}
                  sceneType={sceneType}
                  isAll={isAll}
                />
              )}
            </div>
          </React.Fragment>
        );
      })}
      {includes(['AiApi', 'LLMApi', 'McpApi'], sceneType) && (
        <AiAddPlugin
          direction={type}
          environmentId={environmentId}
          onRefresh={onRefresh}
          currentGatewayInfo={currentGatewayInfo}
          resourceInfo={resourceInfo}
          concatDomains={domains}
          resourceId={
            sceneType === 'McpApi' ? get(resourceInfo, 'routeId') : get(resourceInfo, 'httpApiId')
          }
          sceneType={sceneType}
          attachResourceType={attachResourceType}
          isAll={isAll}
        />
      )}
    </div>
  );
};
export default EnterOrOutContent;
