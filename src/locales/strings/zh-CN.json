{"apigw.src.appConfig.Hangzhou": "杭州", "apigw.src.appConfig.Beijing": "北京", "apigw.components.addVersionDialog.AddVersion": "添加版本", "apigw.components.addVersionDialog.Usage": "使用方式", "apigw.components.createApiDialog.CreateHttpApi": "创建REST API", "apigw.components.createApiDialog.ApiName": "API名称", "apigw.components.createApiDialog.EnterAnApiName": "请输入API名称", "apigw.components.createApiDialog.BasePathCannotBeEmpty": "Base Path不能为空", "apigw.components.createApiDialog.VersionManagement": "版本管理", "apigw.components.createApiDialog.Usage": "使用方式", "apigw.components.createApiDialog.AddHeader": "添加Header", "apigw.components.createApiDialog.AddQuery": "添加Query", "apigw.components.createApiDialog.CannotBeEmpty": "不能为空", "apigw.components.createApiDialog.Description": "描述", "apigw.components.createApiDialog.EnterADescription": "请输入描述内容", "apigw.api-manage.createApi.Create": "创建", "apigw.shared.RegTest.Close": "关闭", "apigw.shared.RegionNav.NorthChinaZhangjiakou": "华北3（张家口）", "apigw.shared.RegionNav.ChinaHongKong": "中国（香港）", "apigw.shared.TagsResource.ResourceTag.Null": "空值", "apigw.config.constants.ServiceActivationFailedPleaseTry": "服务开通失败，请稍后再试", "apigw.config.constants.TheSubAccountDoesNot": "子账号没有对应权限", "apigw.config.constants.TheRequestFailedPleaseTry": "请求失败，请稍后再试", "apigw.config.constants.ApplicationHighAvailabilityService": "应用高可用服务", "apigw.config.constants.EastChinaFinancialCloud": "华东 2 金融云", "apigw.config.constants.NorthChinaAlibabaGovernmentCloud": "华北 2 阿里政务云1", "apigw.config.constants.SouthChinaShenzhen": "华南1（深圳）", "apigw.config.constants.NorthChinaBeijing": "华北2（北京）", "apigw.config.constants.PublicNetwork": "公网", "apigw.config.constants.EastChinaShanghai": "华东2（上海）", "apigw.config.constants.NorthChinaZhangjiakou": "华北3（张家口）", "apigw.config.constants.EastChinaHangzhou": "华东1（杭州）", "apigw..ApiDetails": "API详情", "apigw..Monitoring": "监控", "apigw..ReleaseVersion": "发布版本", "apigw..ReleaseHistory": "发布历史", "apigw.components.BasicInfo.StartingChanging": "启动/变更中（预计需要1～5分钟）", "apigw.components.BasicInfo.FailedToStartChange": "启动/变更失败", "apigw.components.BasicInfo.NormalOperation": "运行中", "apigw.components.BasicInfo.Console": "控制台", "apigw.components.BasicInfo.InstanceName": "实例名称", "apigw.components.BasicInfo.zoneList.FuzhouZoneA": "福州 可用区A", "apigw.components.BasicInfo.zoneList.ShanghaiZoneB": "上海 可用区B", "apigw.components.BasicInfo.zoneList.ShanghaiZoneM": "上海 可用区M", "apigw.components.BasicInfo.zoneList.ShanghaiZoneD": "上海 可用区D", "apigw.components.BasicInfo.zoneList.NanjingLocalRegionZoneA": "南京-本地地域 可用区A", "apigw.components.BasicInfo.zoneList.GuangzhouZoneA": "广州 可用区A", "apigw.components.BasicInfo.zoneList.GuangzhouZoneB": "广州 可用区B", "apigw.components.BasicInfo.zoneList.BeijingZoneK": "北京 可用区K", "apigw.components.BasicInfo.zoneList.HangzhouZoneK": "杭州 可用区K", "apigw.components.BasicInfo.zoneList.ShanghaiZoneL": "上海 可用区L", "apigw.components.BasicInfo.zoneList.HangzhouZoneJ": "杭州 可用区J", "apigw.components.BasicInfo.zoneList.EastChinaFinancialCloudZone": "华东1 金融云 可用区J", "apigw.components.BasicInfo.zoneList.EastChinaFinancialCloudZone.1": "华东1 金融云 可用区K", "apigw.components.BasicInfo.zoneList.EastChinaFinancialCloudZone.5": "华东2 金融云 可用区K", "apigw.components.BasicInfo.zoneList.EastChinaFinancialCloudZone.3": "华东2 金融云 可用区Z", "apigw.components.BasicInfo.zoneList.ZhangjiakouZoneA": "张家口 可用区A", "apigw.components.BasicInfo.zoneList.BeijingZoneI": "北京 可用区I", "apigw.components.BasicInfo.zoneList.BeijingZoneL": "北京 可用区L", "apigw.components.BasicInfo.zoneList.SiliconValleyZoneA": "硅谷 可用区A", "apigw.components.BasicInfo.zoneList.SiliconValleyZoneB": "硅谷 可用区B", "apigw.components.BasicInfo.zoneList.QingdaoZoneB": "青岛 可用区B", "apigw.components.BasicInfo.zoneList.QingdaoZoneC": "青岛 可用区C", "apigw.components.BasicInfo.zoneList.HangzhouZoneA": "杭州 可用区A", "apigw.components.BasicInfo.zoneList.HangzhouZoneB": "杭州 可用区B", "apigw.components.BasicInfo.zoneList.HangzhouZoneC": "杭州 可用区C", "apigw.components.BasicInfo.zoneList.HangzhouZoneD": "杭州 可用区D", "apigw.components.BasicInfo.zoneList.HangzhouZoneE": "杭州 可用区E", "apigw.components.BasicInfo.zoneList.HangzhouZoneF": "杭州 可用区F", "apigw.components.BasicInfo.zoneList.HangzhouZoneG": "杭州 可用区G", "apigw.components.BasicInfo.zoneList.HangzhouZoneH": "杭州 可用区H", "apigw.components.BasicInfo.zoneList.HangzhouZoneI": "杭州 可用区I", "apigw.components.BasicInfo.zoneList.SydneyZoneA": "悉尼 可用区A", "apigw.components.BasicInfo.zoneList.SydneyZoneB": "悉尼 可用区B", "apigw.components.BasicInfo.zoneList.ShanghaiZoneG": "上海 可用区G", "apigw.components.BasicInfo.zoneList.ShanghaiZoneE": "上海 可用区E", "apigw.components.BasicInfo.zoneList.ShanghaiZoneF": "上海 可用区F", "apigw.components.BasicInfo.zoneList.ShenzhenZoneD": "深圳 可用区D", "apigw.components.BasicInfo.zoneList.ShenzhenZoneF": "深圳 可用区F", "apigw.components.BasicInfo.zoneList.ShenzhenZoneE": "深圳 可用区E", "apigw.components.BasicInfo.zoneList.BeijingZoneJ": "北京 可用区J", "apigw.components.BasicInfo.zoneList.BeijingZoneH": "北京 可用区H", "apigw.components.BasicInfo.zoneList.BeijingZoneG": "北京 可用区G", "apigw.components.BasicInfo.zoneList.HongKongZoneB": "香港 可用区B", "apigw.components.BasicInfo.zoneList.HongKongZoneC": "香港 可用区C", "apigw.components.BasicInfo.zoneList.HongKongZoneD": "香港 可用区D", "apigw.components.BasicInfo.zoneList.SingaporeZoneA": "新加坡 可用区A", "apigw.components.BasicInfo.zoneList.SingaporeZoneC": "新加坡 可用区C", "apigw.components.BasicInfo.zoneList.SingaporeZoneB": "新加坡 可用区B", "apigw.components.BasicInfo.zoneList.JakartaZoneB": "雅加达 可用区B", "apigw.components.BasicInfo.zoneList.JakartaZoneA": "雅加达 可用区A", "apigw.components.BasicInfo.zoneList.FrankfurtZoneA": "法兰克福 可用区A", "apigw.components.BasicInfo.zoneList.FrankfurtZoneB": "法兰克福 可用区B", "apigw.components.BasicInfo.zoneList.VirginiaZoneB": "弗吉尼亚 可用区B", "apigw.components.BasicInfo.zoneList.VirginiaZoneA": "弗吉尼亚 可用区A", "apigw.components.BasicInfo.zoneList.MumbaiZoneB": "孟买 可用区B", "apigw.components.BasicInfo.zoneList.MumbaiZoneA": "孟买 可用区A", "apigw.components.BasicInfo.zoneList.ZhangjiakouZoneC": "张家口 可用区C", "apigw.components.BasicInfo.zoneList.ZhangjiakouZoneB": "张家口 可用区B", "apigw.components.BasicInfo.zoneList.ChengduZoneA": "成都 可用区A", "apigw.components.BasicInfo.zoneList.ChengduZoneB": "成都 可用区B", "apigw.components.BasicInfo.zoneList.UlanchabuZoneA": "乌兰察布 可用区A", "apigw.components.BasicInfo.zoneList.UlanchabuZoneB": "乌兰察布 可用区B", "apigw.components.BasicInfo.zoneList.TokyoZoneB": "东京 可用区B", "apigw.components.BasicInfo.zoneList.TokyoZoneA": "东京 可用区A", "apigw.components.BasicInfo.zoneList.SouthChinaFinancialCloudZone": "华南1 金融云 可用区E", "apigw.components.BasicInfo.zoneList.SouthChinaFinancialCloudZone.1": "华南1 金融云 可用区D", "apigw.components.BasicInfo.zoneList.KualaLumpurZoneA": "吉隆坡 可用区A", "apigw.components.BasicInfo.zoneList.KualaLumpurZoneB": "吉隆坡 可用区B", "apigw.components.BasicInfo.zoneList.EastChinaFinancialCloudZone.4": "华东2 金融云 可用区G", "apigw.components.BasicInfo.zoneList.NorthChinaFinancialCloudZone": "华北2 金融云 可用区L", "apigw.components.BasicInfo.zoneList.NorthChinaFinancialCloudZone.1": "华北2 金融云 可用区K", "apigw.components.BasicInfo.zoneList.NorthChinaAlibabaGovernmentCloud": "华北2 阿里政务云 可用区C", "apigw.components.BasicInfo.zoneList.NorthChinaAlibabaGovernmentCloud.1": "华北2 阿里政务云 可用区D", "apigw.components.BasicInfo.zoneList.ManilaZoneA": "马尼拉 可用区A", "apigw.components.BasicInfo.zoneList.EastChinaHangzhouZone": "华东1（杭州） 可用区", "apigw.components.BasicInfo.zoneList.EastChinaShanghaiZone": "华东2（上海） 可用区", "apigw.components.BasicInfo.zoneList.NorthChinaQingdaoZone": "华北1（青岛） 可用区", "apigw.components.BasicInfo.zoneList.NorthChinaBeijingZone": "华北2（北京） 可用区", "apigw.components.BasicInfo.zoneList.NorthChinaZhangjiakouZone": "华北3（张家口） 可用区", "apigw.components.BasicInfo.zoneList.NorthChinaHohhotZone": "华北5（呼和浩特） 可用区", "apigw.components.BasicInfo.zoneList.NorthChinaWulanchabuZone": "华北6（乌兰察布） 可用区", "apigw.components.BasicInfo.zoneList.SouthChinaShenzhenZone": "华南1（深圳） 可用区", "apigw.components.BasicInfo.zoneList.SouthChinaHeyuanZone": "华南2（河源） 可用区", "apigw.components.BasicInfo.zoneList.SouthChinaGuangzhouZone": "华南3（广州） 可用区", "apigw.components.BasicInfo.zoneList.SouthwestChengduZone": "西南1（成都） 可用区", "apigw.components.BasicInfo.zoneList.HongKongChinaZone": "中国香港 可用区", "apigw.components.BasicInfo.zoneList.SingaporeZone": "新加坡 可用区", "apigw.components.BasicInfo.zoneList.AustraliaSydneyZone": "澳大利亚（悉尼） 可用区", "apigw.components.BasicInfo.zoneList.MalaysiaKualaLumpurZone": "马来西亚（吉隆坡） 可用区", "apigw.components.BasicInfo.zoneList.IndonesiaJakartaZone": "印度尼西亚（雅加达） 可用区", "apigw.components.BasicInfo.zoneList.PhilippinesManilaZone": "菲律宾（马尼拉） 可用区", "apigw.components.BasicInfo.zoneList.ThailandBangkokZone": "泰国（曼谷） 可用区", "apigw.components.BasicInfo.zoneList.JapanTokyoZone": "日本（东京） 可用区", "apigw.components.BasicInfo.zoneList.SouthKoreaSeoulZone": "韩国（首尔） 可用区", "apigw.components.BasicInfo.zoneList.IndiaMumbaiZone": "印度（孟买） 可用区", "apigw.components.BasicInfo.zoneList.GermanyFrankfurtZone": "德国（法兰克福） 可用区", "apigw.components.BasicInfo.zoneList.UkLondonZone": "英国（伦敦） 可用区", "apigw.components.BasicInfo.zoneList.UsSiliconValleyZone": "美国（硅谷） 可用区", "apigw.components.BasicInfo.zoneList.UsVirginiaZone": "美国（弗吉尼亚） 可用区", "apigw.components.BasicInfo.zoneList.SaudiArabiaRiyadhZone": "沙特（利雅得） 可用区", "apigw.components.BasicInfo.zoneList.NorthChinaFinancialCloudZone.2": "华北2 金融云 可用区", "apigw.components.BasicInfo.zoneList.SouthChinaFinancialCloudZone.2": "华南1 金融云 可用区", "apigw.components.BasicInfo.zoneList.ChinaEastFinancialCloudZone": "华东1 金融云 可用区", "apigw.components.BasicInfo.zoneList.EastChinaFinancialCloudZone.6": "华东2 金融云 可用区", "apigw.components.BasicInfo.zoneList.NorthChinaAlibabaGovernmentCloud.2": "华北2 阿里政务云 可用区", "apigw.components.dubboGroup.dubboItem.ServiceGroupsAreApplicableTo": "服务分组适用于实例版本1.2.6及以上", "apigw.components.paramGroup.RequestHeader": "请求头", "apigw.router.components.LogsConfigDialog.EnableLogDelivery": "开启日志投递功能", "apigw.router.components.LogsConfigDialog.UseExistingLogServiceProject": "使用已有日志服务Project不能为空", "apigw.router.components.LogsConfigDialog.SelectAnExistingLogService": "请选择已有日志服务Project", "apigw.router.components.LogsConfigDialog.LogServiceSlsTermsOf": "日志服务(SLS)服务条款", "apigw.detail.components.LogsConfigDialog.EnableLogDelivery": "开启日志投递功能", "apigw.detail.components.LogsConfigDialog.UseExistingLogServiceProject": "使用已有日志服务Project不能为空", "apigw.detail.components.LogsConfigDialog.SelectAnExistingLogService": "请选择已有日志服务Project", "apigw.detail.components.LogsConfigDialog.LogServiceSlsTermsOf": "日志服务(SLS)服务条款", "apigw.components.VersionContent.EndpointnumEndpointnumpercent": "{EndpointNum}（{EndpointNumPercent}）", "apigw.components.ExpansionPanel.TimeConfig.TimePeriodConfiguration": "时间段配置", "apigw.components.ExpansionPanel.TimeConfig.TimePeriodCurrentRegionTime": "时间段（当前region时区）", "apigw.components.ExpansionPanel.TimeConfig.Note": "注：", "apigw.components.ExpansionPanel.TimeConfig.ForMoreThanOneHour": "1.超过一小时部分向上取整1小时", "apigw.components.ExpansionPanel.TimeConfig.YouCannotSetARepeat": "2.不可设置重复时间段", "apigw.components.ExpansionPanel.TimeConfig.StartTime": "起始时间", "apigw.components.ExpansionPanel.TimeConfig.EndTime": "结束时间", "apigw.components.ExpansionPanel.TimeConfig.TheSelectionRangeMustBe": "选取范围要≥2小时", "apigw.components.ExpansionPanel.TimeConfig.TheStartTimeOfThe": "当前任务起始时间早于当前时间，当前不会触发，将在下个周期触发", "apigw.components.ExpansionPanel.TimeConfig.NumberOfTargetNodesUnits": "目标节点数（单位：台）", "apigw.components.ExpansionPanel.EnableAutomaticExpansion": "打开自动扩容", "apigw.components.ExpansionPanel.AfterAutomaticScalingIsEnabled": "打开自动扩容后，实例将无法手动升配及降配。如需手动变配请关闭自动扩容。", "apigw.components.ExpansionPanel.DisableAutomaticScaling": "关闭自动扩容", "apigw.components.ExpansionPanel.TheExistingElasticNodeWill": "当前存在的弹性节点，将在关闭后停止，请在流量低峰期操作", "apigw.components.ExpansionPanel.SelectATimePeriod": "请选择时间段", "apigw.components.ExpansionPanel.PleaseCheckTheTimeRange": "请检查时间段范围", "apigw.components.ExpansionPanel.TheTimePeriodIsRepeated": "时间段重复", "apigw.components.ExpansionPanel.TheSelectedTimePeriodHas": "选取时间段有重复部分，请重新设置时间段", "apigw.components.ExpansionPanel.Reset": "重新设置", "apigw.components.ExpansionPanel.TheAutomaticExpansionOperationIs": "自动扩容操作成功", "apigw.components.ExpansionPanel.AutomaticExpansion": "自动扩容", "apigw.components.ExpansionPanel.Save": "保存", "apigw.components.ExpansionPanel.Cancel": "取消", "apigw.components.ExpansionPanel.EstimatedDailyExpansionFeeUnit": "预计“每日扩容费”=实例规格单价 x", "apigw.components.ExpansionPanel.HoursX": "小时 x", "apigw.components.ExpansionPanel.Node": "节点", "apigw.components.ExpansionPanel.DuringTheEffectivePeriodOf": "自动扩容生效期间,系统将对扩容节点数以及运行市场进行按量计费。设置及收费等信息详见设置", "apigw.components.ExpansionPanel.CurrentInstanceType": "当前实例规格", "apigw.components.ExpansionPanel.FixedNumberOfNodes": "固定节点数", "apigw.components.ExpansionPanel.CurrentinstancereplicaUnits": "{currentInstanceReplica}台", "apigw.components.ExpansionPanel.TotalCurrentNodes": "当前总节点数", "apigw.components.ExpansionPanel.Table": "台", "apigw.components.ExpansionPanel.EffectiveSwitch": "生效开关", "apigw.components.ExpansionPanel.ExpansionMethodSelection": "扩容方式选择", "apigw.components.ExpansionPanel.TheDailyCapacityExpansionFee": "每日扩容费根据实际扩容的节点和时长（按小时取整）计算", "apigw.components.ExpansionPanel.ScaleOutByTime": "按时间扩容", "apigw.src.sidebar.ApiManagement": "API", "apigw.api-manage.headBtn.MoreOperations": "更多操作", "apigw.headBtn.publish.BasicScenario": "基础场景", "apigw.headBtn.publish.ToUseSpecificContentAs": "使用特定内容作为请求返回结果，需在接口上定义Mock内容。", "apigw.headBtn.publish.SingleService": "单服务", "apigw.headBtn.publish.DistributingRequestsToTheOnly": "将请求分发到唯一一个后端服务，是最常使用的场景。", "apigw.headBtn.publish.GrayscaleScene": "灰度场景", "apigw.headBtn.publish.ProportionalMultiService": "按比例（多服务）", "apigw.headBtn.publish.TheDescriptionOfTheContent": "将请求按照比例分发到多个后端服务，常用于切流及灰度发布场景。", "apigw.headBtn.publish.RouteByContentLabel": "按内容（多服务）", "apigw.headBtn.publish.DistributeRequestsToMultipleBackend": "将请求按内容分发到多个后端服务，实现将特定特征的流量转发到对应服务。", "apigw.headBtn.publish.ByLabelLabelRouting": "按标签（标签路由）", "apigw.headBtn.publish.DomainName": "域名", "apigw.headBtn.publish.YouCanSelectMultipleDomain": "您可以选择多个域名", "apigw.headBtn.publish.UsageScenarios": "使用场景", "apigw.headBtn.publish.WhenTheBackendTypeIs": "后端类型为mock时，可在创建接口时，为不同接口配置不同的MOCK响应。", "apigw.headBtn.publish.ReleaseDescription": "发布描述", "apigw.headBtn.publish.EnterADescription": "请输入描述内容", "apigw.api-manage.interfaceList.ApiOperationsMenu.AddInterface": "添加接口", "apigw.api-manage.interfaceList.ApiOperationsSearch.SearchInterfaceName": "搜索接口名称", "apigw.api-manage.interfaceList.ApiOperationsSearch.SearchForInterfacePaths": "搜索接口路径", "apigw.api-manage.interfaceList.ApiOperationsSearch.Name": "名称", "apigw.api-manage.interfaceList.ApiOperationsSearch.Path": "路径", "apigw.api-manage.interfaceList.ApiOperationsSearch.Path.1": "路径 :", "apigw.api-manage.interfaceList.ApiOperationsSearch.Name.1": "名称 :", "apigw.api-manage.interfaceList.ApiOperationsSearch.Method": "方法 :", "apigw.api-manage.interfaceList.ApiOperationsSearch.ClearFilter": "清除筛选", "apigw.components.api-operations-action-button.CreateOperation.AddInterface": "添加接口", "apigw.components.api-operations-action-button.DeleteOperation.AreYouSureYouWant": "确定要删除该接口吗？", "apigw.components.api-operations-action-button.DeleteOperation.Delete": "删除", "apigw.components.api-operations-action-button.DeleteOperation.DeletedSuccessfully": "删除成功", "apigw.components.api-operations-action-button.EditOperation.Edit": "编辑", "apigw.components.api-operations-guide.CreateAnApi": "创建API", "apigw.components.api-operations-guide.AddInterface": "添加接口", "apigw.components.api-operations-slide.ApiBasicInfo.BasicInformation": "基本信息", "apigw.components.api-operations-slide.ApiBasicInfo.Method": "方法", "apigw.components.api-operations-slide.ApiBasicInfo.Path": "路径", "apigw.components.api-operations-slide.ApiBasicInfo.CreationTime": "创建时间", "apigw.components.api-operations-slide.ApiBasicInfo.Description": "描述", "apigw.components.api-operations-slide.ApiBasicInfo.InterfaceName": "接口名称", "apigw.components.api-operations-slide.ApiBasicInfo.EnterAnInterfaceName": "请输入接口名称", "apigw.components.api-operations-slide.ApiBasicInfo.InterfacePath": "接口Path", "apigw.components.api-operations-slide.ApiBasicInfo.TheRequestPathMustContain": "请求Path支持变量定义，格式如 /[id]、/&lcub;id&rcub; 或 /:id。变量值需在请求定义的Parameter Path中定义。", "apigw.components.api-operations-slide.ApiBasicInfo.EnterTheInterfacePath": "请输入接口Path", "apigw.components.api-operations-slide.ApiBasicInfo.SelectMethod": "请选择Method", "apigw.components.api-operations-slide.ApiBasicInfo.EnterADescription": "请输入描述内容", "apigw.components.api-operations-slide.CreateOrEditSlide.InterfaceName": "接口名称", "apigw.components.api-operations-slide.CreateOrEditSlide.InterfacePath": "接口Path", "apigw.components.api-operations-slide.CreateOrEditSlide.RequestDefinition": "请求定义", "apigw.components.api-operations-slide.CreateOrEditSlide.ResponseDefinition": "响应定义", "apigw.components.api-operations-slide.CreateOrEditSlide.TheFormInformationIsNot": "表单信息未填写完整", "apigw.components.api-operations-slide.CreateOrEditSlide.CheckTheFollowingFormInformation": "请检查以下表单信息：", "apigw.components.api-operations-slide.CreateOrEditSlide.BasicInformation": "基本信息", "apigw.components.api-operations-slide.CreateOrEditSlide.ApiDefinitionInformation": "参数定义", "apigw.components.api-operations-slide.CreateOrEditSlide.TheInformationInTheApi": "参数定义中的信息仅用作文档展示", "apigw.components.api-operations-slide.CreateOrEditSlide.CreateAnInterface": "创建接口", "apigw.components.api-operations-slide.CreateOrEditSlide.EditInterface": "编辑接口", "apigw.api-operations-slide.api-definition-info.BasicTableProps.ParameterName": "参数名称", "apigw.api-operations-slide.api-definition-info.BasicTableProps.EnterAParameterName": "请输入参数名称", "apigw.api-operations-slide.api-definition-info.BasicTableProps.Type": "类型", "apigw.api-operations-slide.api-definition-info.BasicTableProps.PleaseSelectAType": "请选择类型", "apigw.api-operations-slide.api-definition-info.BasicTableProps.DefaultValue": "默认值", "apigw.api-operations-slide.api-definition-info.BasicTableProps.EnterADefaultValue": "请输入默认值", "apigw.api-operations-slide.api-definition-info.BasicTableProps.Example": "示例", "apigw.api-operations-slide.api-definition-info.BasicTableProps.PleaseEnterAnExample": "请输入示例", "apigw.api-operations-slide.api-definition-info.BasicTableProps.Description": "描述", "apigw.api-operations-slide.api-definition-info.BasicTableProps.EnterADescription": "请输入描述", "apigw.components.apimanage.apiai.detail.modelName": "模型名称：", "apigw.api-operations-slide.api-definition-info.BasicTableProps.Required": "必填", "apigw.api-operations-slide.api-definition-info.BasicTableProps.Yes": "是", "apigw.api-operations-slide.api-definition-info.BasicTableProps.No": "否", "apigw.api-operations-slide.api-definition-info.BasicTableProps.ErrorCode": "错误码", "apigw.api-operations-slide.api-definition-info.BasicTableProps.ForExample": "如: 400", "apigw.api-operations-slide.api-definition-info.BasicTableProps.ErrorMessage": "错误信息", "apigw.api-operations-slide.api-definition-info.BasicTableProps.ForExampleMissingTheParameter": "如：Missing the parameter UserId", "apigw.api-operations-slide.api-definition-info.BasicTableProps.EnterADescription.1": "请输入描述内容", "apigw.api-operations-slide.api-definition-info.BasicTableProps.Value": "值", "apigw.api-operations-slide.api-definition-info.Mock.EnableMock": "开启<PERSON>ck", "apigw.api-operations-slide.api-definition-info.Mock.ResponseStatusCode": "状态码", "apigw.api-operations-slide.api-definition-info.Mock.ForExample": "如：200", "apigw.api-operations-slide.api-definition-info.Mock.ResponseContent": "响应内容", "apigw.api-operations-slide.api-definition-info.Request.BodyContentDescription": "Body 内容描述", "apigw.api-operations-slide.api-definition-info.Response.ReturnContenttype": "ContentType", "apigw.api-operations-slide.api-definition-info.Response.OnlyUsedToGenerateDocuments": "仅用于生成文档", "apigw.api-operations-slide.api-definition-info.Response.SelectReturnContenttype": "请选择ContentType", "apigw.api-operations-slide.api-definition-info.Response.TextPlainCharsetUtf": "文本 (text/plain;charset=utf-8)", "apigw.api-operations-slide.api-definition-info.Response.BinaryApplicationOctetStreamCharset": "二进制 (application/octet-stream;charset=utf-8)", "apigw.api-operations-slide.api-definition-info.RequestDefinition": "请求定义", "apigw.api-operations-slide.api-definition-info.ResponseDefinition": "响应定义", "apigw.api-operations-slide.api-definition-info.AI.GenerateSchema": "基于示例通过AI辅助生成Schema", "apigw.api-operations-slide.api-definition-info.AI.GenerateMockData": "基于Schema通过AI辅助生成Mock数据", "apigw.api-operations-slide.api-definition-info.AI.GeneratingTip": "AI生成中...", "apigw.api-operations-slide.api-definition-info.AI.MissingInput": "缺少输入", "apigw.api-operations-slide.api-definition-info.AI.PleaseInputBodyExampleFirst": "请先填写Body示例", "apigw.api-operations-slide.api-definition-info.AI.PleaseInputSchemaFirst": "请先在[响应定义]标签页中填写此状态码的Schema定义以使用此功能", "apigw.components.domain-certificate.DomainTableProps.Console": "控制台", "apigw.components.domain-certificate.DomainTableProps.SearchForDomainNames": "搜索域名", "apigw.shared.BasicFormTable.TableProps.Operation": "操作", "apigw.shared.BasicFormTable.TableProps.Delete": "删除", "apigw.shared.BasicFormTable.AddParameters": "添加参数", "apigw.components.EntranceSet.InstanceType": "实例类型", "apigw.components.TraceConfig.OpentelemetryWC": "OpenTelemetry（W3C）", "apigw.components.TraceConfig.RecommendUseThisProtocolFor": "推荐使用该协议，以获得更好的链路追踪体验", "apigw.components.TraceConfig.ZipkinB": "<PERSON><PERSON><PERSON>（B3）", "apigw.components.TraceConfig.TheOriginalXtraceOptionIs": "原 xTrace 选项，不推荐继续使用，建议切换为兼容性更好的 OpenTelemetry（W3C）协议。切换过程是无损的，可以放心执行", "apigw.components.TraceConfig.LinkTrackingConfiguration": "链路追踪配置", "apigw.components.TraceConfig.LinkTrackingStatus": "链路追踪状态", "apigw.components.TraceConfig.LinkTrackingService": "链路追踪服务", "apigw.components.TraceConfig.ObservableLinkVersionOpentelemetry": "可观测链路 OpenTelemetry 版", "apigw.components.TraceConfig.SelfBuiltSkywalking": "自建 SkyWalking", "apigw.components.TraceConfig.LinkTrackingProtocol": "链路追踪协议", "apigw.components.TraceConfig.ActivateService": "开通服务", "apigw.components.TraceConfig.ClickOkToAutomaticallyActivate": "点击确定按钮将会为您自动开通可观测链路 OpenTelemetry 版服务。该服务支持 OpenTelemetry（W3C）、<PERSON><PERSON><PERSON>（B3）和 SkyWalking 等多种协议。", "apigw.components.TraceConfig.LearnMore": "了解详情", "apigw.components.TraceConfig.ServiceAgreement": "服务协议", "apigw.components.TraceConfig.SkywalkingService": "SkyWalking 服务", "apigw.components.TraceConfig.SelectSkywalkingService": "请选择SkyWalking 服务", "apigw.components.TraceConfig.PortSkywalking": "SkyWalking 端口", "apigw.components.TraceConfig.SelectPortSkywalking": "请选择SkyWalking 端口", "apigw.observe.components.TracePermission.WarmTips": "温馨提示", "apigw.observe.components.TracePermission.YouAreUsingASelf": "您正在使用自建 SkyWalking 来提供链路追踪能力，请联系相关负责人员获取自建 SkyWalking 的访问地址。", "apigw.observe.trace.OpentelemetryWC": "OpenTelemetry（W3C）", "apigw.observe.trace.ZipkinB": "<PERSON><PERSON><PERSON>（B3）", "apigw.observe.trace.SelfBuiltSkywalking": "自建 SkyWalking", "apigw.observe.trace.TheLinkTrackingFeatureFully": "链路追踪功能已全面支持 OpenTelemetry（W3C）协议，强烈建议您切换至该协议，以获得更好的使用体验。该切换过程是无损的，您可以放心执行。", "apigw.observe.trace.NoLongerDisplayed": "不再显示", "apigw.observe.trace.Modify": "修改", "apigw.components.ObserveSide.OpentelemetryWC": "OpenTelemetry（W3C）", "apigw.components.ObserveSide.ZipkinB": "<PERSON><PERSON><PERSON>（B3）", "apigw.components.ObserveSide.LinkTrackingStatus": "链路追踪状态", "apigw.components.ObserveSide.Enabled": "已开启", "apigw.components.ObserveSide.NotEnabled": "未开启", "apigw.components.ObserveSide.LinkTrackingService": "链路追踪服务", "apigw.components.ObserveSide.LinkTrackingProtocol": "链路追踪协议", "apigw.confirm.offline.title": "确认下线 {name}", "apigw.publish.switch.current.version": "当前版本: {currentRevisionId} -> 切换到版本: {revisionId}", "apigw.export.api": "确定导出: {name}", "apigw.apiDetails.publishDetail.offline.Offline": "下线", "apigw.apiDetails.publishDetail.offline.OfflineSuccessfully": "下线成功", "apigw.apiDetails.publishDetail.publish.Publish": "发布", "apigw.apiDetails.publishDetail.tableProps.ServiceType": "服务类型", "apigw.apiDetails.publishDetail.tableProps.Service": "服务", "apigw.apiDetails.publishDetail.tableProps.Operation": "操作", "apigw.api-manage.components.constants.ExistingServices": "已有服务", "apigw.api-manage.components.constants.CloudProductResources": "云产品资源", "apigw.api-manage.components.constants.FixedAddress": "固定地址", "apigw.api-manage.components.constants.DnsDomainName": "DNS域名", "apigw.components.createApiDialog.CreateAFileBasedOn": "基于OpenAPI创建文件", "apigw.components.createApiDialog.OpenapiFiles": "OpenAPI文件", "apigw.components.createApiDialog.UploadTheOpenApiFile": "上传Open API文件，或将API定义粘贴到下面字段中。", "apigw.components.createApiDialog.UploadFiles": "上传文件", "apigw.components.createApiDialog.DragAndDropUploadedFiles": "拖拽上传文件", "apigw.components.createApiDialog.Or": "或", "apigw.components.createApiDialog.ViewLocalFiles": "查看本地文件", "apigw.components.createApiDialog.DeleteSampleApis": "删除示例API", "apigw.components.createApiDialog.UseTheSampleApi": "使用示例API", "apigw.api-manage.headBtn.Export": "导出", "apigw.api-manage.headBtn.ExportedSuccessfully": "导出成功", "apigw.publish.components.matchCondition.EditMatchingConditions": "编辑匹配条件", "apigw.publish.components.matchCondition.TheRelationshipBetweenMultipleMatching": "多个匹配条件之间是逻辑\"与\"的关系", "apigw.publish.components.serviceTableProps.TheFormatIsIpPort": "格式为IP:Port，支持填写多个，以英文\",\"分隔", "apigw.publish.components.serviceTableProps.MatchingCondition": "匹配条件", "apigw.publish.components.serviceTableProps.Default": "默认", "apigw.publish.components.serviceTableProps.ServiceName": "服务名称", "apigw.publish.components.serviceTableProps.ServicePort": "服务端口", "apigw.publish.components.serviceTableProps.ServiceAgreement": "服务协议", "apigw.publish.components.serviceTableProps.ServiceVersion": "服务版本", "apigw.publish.components.serviceTableProps.ParameterType": "参数类型", "apigw.publish.components.serviceTableProps.Parameter": "参数", "apigw.publish.components.serviceTableProps.MatchingMethod": "匹配方式", "apigw.publish.components.serviceTableProps.Equal": "等于", "apigw.publish.components.serviceTableProps.PrefixIs": "前缀是", "apigw.publish.components.serviceTableProps.RegularMatching": "正则匹配", "apigw.publish.components.serviceTableProps.Value": "值", "apigw.headBtn.publish.TheSumOfWeightsMust": "权重总和必须100", "apigw.headBtn.publish.UpToOneDefault": "最多只能有1个默认", "apigw.headBtn.publish.ThePublishingEnvironmentCannotBe": "发布环境不能为空", "apigw.headBtn.publish.BackendServices": "后端服务", "apigw.api-operations-slide.api-definition-info.BodyContent.BodyExample": "Body 示例", "apigw.api-operations-slide.api-definition-info.BodyContent.EnterABodyExample": "请输入Body示例", "apigw.api-operations-slide.api-definition-info.BodyContent.EnterADescription": "请输入描述内容", "apigw.api-operations-slide.api-definition-info.CodeTabContent.StatusCode": "状态码", "apigw.api-operations-slide.api-definition-info.CodeTabContent.Description": "描述", "apigw.api-operations-slide.api-definition-info.CodeTabContent.EnterADescription": "请输入描述", "apigw.api-operations-slide.api-definition-info.CodeTabs.Success": "成功", "apigw.api-operations-slide.api-definition-info.CodeTabs.DeletedSuccessfully": "删除成功", "apigw.api-operations-slide.api-definition-info.CodeTabs.TheRequestIsIncorrect": "请求有误", "apigw.api-operations-slide.api-definition-info.CodeTabs.NoPermission": "没有权限", "apigw.api-operations-slide.api-definition-info.CodeTabs.AccessProhibited": "禁止访问", "apigw.api-operations-slide.api-definition-info.CodeTabs.TheRecordDoesNotExist": "记录不存在", "apigw.api-operations-slide.api-definition-info.CodeTabs.ParameterError": "参数错误", "apigw.api-operations-slide.api-definition-info.CodeTabs.ServerError": "服务器错误", "apigw.api-operations-slide.api-definition-info.CodeTabs.GatewayError": "网关错误", "apigw.api-operations-slide.api-definition-info.CodeTabs.ServerFailure": "服务器故障", "apigw.api-operations-slide.api-definition-info.CodeTabs.GatewayTimeout": "网关超时", "apigw.api-operations-slide.api-definition-info.CodeTabs.Add": "添加", "apigw.api-definition-info.basicForm.ExampleSchema.ReturnExample": "返回示例", "apigw.api-definition-info.basicForm.ExampleSchema.PleaseEnterAReturnExample": "请输入返回示例", "apigw.api-definition-info.basicForm.ExampleSchema.JsonSchemaDefinition": "JSON Schema定义", "apigw.api-operations-slide.api-definition-info.Success": "成功", "apigw.components.api-operations-strategy.AddPolicyPlugin.EnablePoliciesPlugIns": "启用策略/插件", "apigw.components.api-operations-strategy.AddPolicyPlugin.AddPolicy": "添加策略", "apigw.components.api-operations-strategy.AddPolicyPlugin.EnablePlugIns": "启用插件", "apigw.components.api-operations-strategy.AddPolicyPlugin.Cancel": "取消", "apigw.components.api-operations-strategy.PolicyDetails.BasicInformation": "基本信息", "apigw.components.api-operations-strategy.PolicyDetails.PolicyName": "策略名称", "apigw.components.api-operations-strategy.PolicyDetails.PolicyType": "策略类型", "apigw.components.api-operations-strategy.PolicyDetails.ExecutionPhase": "执行阶段", "apigw.components.api-operations-strategy.PolicyDetails.ConfigurationInformation": "配置信息", "apigw.publishHistory.components.basic.ExampleOfVersionNumberUsage": "版本号使用示例", "apigw.publishHistory.components.basic.VersionDetailsCurrentrecordrevisionid": "版本详情 {currentRecordRevisionId}", "apigw.interface.detail.Close": "关闭", "apigw.components.interface.Name": "名称", "apigw.components.interface.Method": "方法", "apigw.components.interface.Path": "路径", "apigw.components.interface.Description": "描述", "apigw.components.interface.Operation": "操作", "apigw.components.interface.ViewMore": "查看更多", "apigw.components.interface.Interface": "接口", "apigw.api-manage.publishHistory.PleaseSelect": "请选择", "apigw.api-manage.publishHistory.Version": "版本", "apigw.api-manage.publishHistory.Environment": "环境", "apigw.api-manage.publishHistory.Description": "描述", "apigw.api-manage.publishHistory.ReleaseTime": "发布时间", "apigw.api-manage.publishHistory.WhetherItIsTheCurrent": "是否是当前版本", "apigw.api-manage.publishHistory.Yes": "是", "apigw.api-manage.publishHistory.No": "否", "apigw.api-manage.publishHistory.View": "查看", "apigw.api-manage.publishHistory.SwitchToThisVersion": "切换至此版本", "apigw.api-manage.publishHistory.switchVersion.ConfirmVersionSwitching": "确认切换版本", "apigw.api-manage.publishHistory.switchVersion.Switch": "切换", "apigw.api-manage.publishHistory.switchVersion.ReleaseDescription": "发布描述", "apigw.api-manage.publishHistory.switchVersion.EnterADescription": "请输入描述内容", "apigw.api-manage.publishHistory.switchVersion.CannotBeEmpty": "不能为空", "apigw.api-manage.publishHistory.versionDetails.VersionDetails": "版本详情", "apigw.api-manage.publishHistory.versionDetails.Close": "关闭", "apigw.components.overview.ActionsGrid.CreateAnInstance": "创建实例", "apigw.components.overview.ActionsGrid.QuickEntrance": "快速入口", "apigw.components.overview.HighlightPractices.BestPractices": "最佳实践", "apigw.components.overview.HighlightPractices.More": "更多", "apigw.components.overview.RecentNews.LatestDevelopments": "最新动态", "apigw.components.overview.RecentNews.More": "更多", "apigw.components.overview.ResourceList.ResourceStatistics": "资源统计", "apigw.components.overview.ResourceList.Region": "地域", "apigw.components.overview.ResourceList.NumberOfInstancesRunningNormally": "实例数量（运行正常/总数）", "apigw.components.overview.ResourceList.NumberOfApisPublishedTotal": "API数量（已发布/总数）", "apigw.components.overview.ResourceList.Operation": "操作", "apigw.components.overview.ResourceList.InstanceManagement": "实例", "apigw.components.overview.ResourceList.ApiManagement": "API管理", "apigw.components.overview.SceneList.RouteManagement": "路由：", "apigw.components.overview.SceneList.TrafficForwardingIsApplicableTo": "面向流量转发，适用于Kubernetes Ingress、微服务架构等使用场景。", "apigw.components.overview.SceneList.InstanceManagement": "实例", "apigw.components.overview.SceneList.InterSystemTrafficForwarding": "系统间流量转发", "apigw.components.overview.SceneList.CompatibleWithKSIngress": "K8s Ingress Controller", "apigw.components.overview.SceneList.NoInterfaceLevelControlIs": "无需接口级别的管控", "apigw.components.overview.SceneList.CreateAnInstance": "创建实例", "apigw.components.overview.SceneList.CreateARoute": "创建路由", "apigw.components.overview.SceneList.ApiManagement": "API 管理：", "apigw.components.overview.SceneList.FullLifecycleManagementForApi": "面向 API 设计、发布、授权及下线的全生命周期管理，适用于API协作、开放等场景。", "apigw.components.overview.SceneList.ApiManagement.1": "API 管理", "apigw.components.overview.SceneList.UnifiedInterfaceStandard": "统一接口标准", "apigw.components.overview.SceneList.ReusableAndScalable": "可复用、可扩展", "apigw.components.overview.SceneList.InterfaceExternalOrUpstreamAnd": "接口对外或上下游协作", "apigw.components.overview.SceneList.CreateAnApi": "创建API", "apigw.components.overview.SceneList.AddInterface": "添加接口", "apigw.components.overview.SceneList.PublishAnApiCreateAn": "发布API（如无实例，则创建实例）", "apigw.components.overview.SceneList.ForDifferentScenariosOpenThe": "应对不同场景，开启适合您的产品使用路径", "apigw.shared.BasicFormTable.TableProps.CannotBeEmpty": "不能为空", "apigw.shared.PluginCardList.HeaderActions.WeideWritingPlugIns": "WebIDE编写插件", "apigw.shared.PluginCardList.PluginCard.Custom": "自定义", "apigw.shared.PluginCardList.PluginList.SearchForPlugIns": "请搜索插件", "apigw.shared.PluginCardList.SideMenu.QuickNavigation": "快捷导航", "apigw.shared.PluginCardList.SideMenu.EnableStatus": "启用状态", "apigw.shared.PluginCardList.SideMenu.PlugInSource": "插件来源", "apigw.shared.PluginCardList.SideMenu.OfficialPlugIn": "Higress 官方插件", "apigw.shared.PluginCardList.SideMenu.HigressCommunityPlugIn": "Higress 社区插件", "apigw.shared.PluginCardList.SideMenu.CustomPlugIn": "自定义插件", "apigw.shared.PluginTableList.Columns.UploadDocuments": "上传文档", "apigw.shared.PluginTableList.Columns.PlugInNamePlugIn": "插件名称/插件描述", "apigw.shared.PluginTableList.Columns.RuleContent": "规则内容", "apigw.components.EngineParams.TheParameterValueFormatIs": "参数值格式有误", "apigw.components.EngineParams.ParameterName": "参数名称", "apigw.components.EngineParams.ParameterValue": "参数值", "apigw.components.EngineParams.ParameterType": "参数类型", "apigw.components.EngineParams.ValueRange": "取值范围", "apigw.components.EngineParams.ParameterDescription": "参数描述", "apigw.components.EngineParams.Operation": "操作", "apigw.components.EngineParams.Edit": "编辑", "apigw.components.EngineParams.ParameterModification": "参数修改", "apigw.components.ObserveSide.ObservableLinkVersionOpentelemetry": "可观测链路 OpenTelemetry 版", "apigw.components.ObserveSide.SelfBuiltSkywalking": "自建 SkyWalking", "apigw.components.ObserveSide.SwitchToProjectWhenLog": "当日志投递处于开启状态时，切换 Project\n                    无法生效。请先关闭日志投递再重新打开，此时便可切换到新的 Project。", "apigw.regionId.plugin-manage.PlugInMarket": "插件", "apigw.gateway.id.SecurityManagement": "安全管理", "apigw.gateway.id.BlackWhitelist": "黑/白名单", "apigw.gateway.id.GlobalAuthentication": "全局认证鉴权", "apigw.observe.trace.LinkTrackingProtocolTracetypemaptracetype": "链路追踪协议：{TraceTypeMapTraceType}", "apigw.id.parameter.ThisGatewayWasCreatedBy": "该网关是由 MSE Ingress Controller 创建的，建议通过", "apigw.id.parameter.ToModifyTheConfigurationThe": "来修改配置。此处的操作将会覆盖 MseIngressConfig 中的已有配置，请谨慎使用。", "apigw.AhasLimit.SystemGuardRulesManagement.Behavior.WebFallbackBehavior": "Web fallback 行为", "apigw.AhasLimit.SystemGuardRulesManagement.Behavior.ReturnsContentType": "返回 content-type", "apigw.security.authority.Configs.ComplexMode": "复杂模式", "apigw.components.EditForm.KeysETheIndexOf": "{\n  \"keys\":[\n    {\n      \"e\":\"公钥的指数，例如AQAB\",\n      \"kid\":\"Key ID\",\n      \"kty\": \"使用的加密算法的家族，例如RSA，必填，大小写敏感\",\n      \"alg\": \"使用的具体的加密算法，例如RS256，必填，大小写敏感\",\n      \"use\": \"密钥的用途，例如sig，用于签名\",\n      \"n\": \"公钥的模值\"\n    }\n  ]\n}\n", "apigw.components.EditForm.SimpleCondition": "简单条件", "apigw.components.EditForm.ComplexConditions": "复杂条件", "apigw.components.EditForm.EnterYamlConfigurationForComplex": "请填写复杂条件的YAML配置", "apigw.apiDetails.publishDetail.publish.Republish": "重新发布", "apigw.apiDetails.publishDetail.tableProps.UsageScenarios": "使用场景", "apigw.apiDetails.publishDetail.tableProps.MatchingCondition": "匹配条件", "apigw.apiDetails.publishDetail.tableProps.Default": "默认", "apigw.apiDetails.publishDetail.tableProps.Yes": "是", "apigw.apiDetails.publishDetail.tableProps.No": "否", "apigw.apiDetails.publishDetail.tableProps.ServiceName": "服务名称", "apigw.apiDetails.publishDetail.tableProps.ServicePort": "服务端口", "apigw.apiDetails.publishDetail.tableProps.ServiceAgreement": "服务协议", "apigw.apiDetails.publishDetail.tableProps.ServiceVersion": "服务版本", "apigw.apiDetails.publishDetail.tableProps.ServiceWeight": "服务权重", "apigw.apiDetails.publishDetail.tableProps.ServiceAddress": "服务地址", "apigw.apiDetails.publishDetail.tableProps.Namespace": "命名空间", "apigw.components.api-operations-debug.OperationDebug.DebuggingInterface": "调试接口:", "apigw.components.api-operations-debug.OperationDebug.InterfaceDomainName": "接口域名", "apigw.components.api-operations-debug.OperationDebug.RequestParameters": "请求参数", "apigw.components.api-operations-debug.OperationDebug.SendRequest": "发送请求", "apigw.components.api-operations-debug.RequestParamsTab.TheParameterNameCannotBe": "参数名不能为空", "apigw.components.api-operations-debug.RequestParamsTab.AddParameters": "添加参数", "apigw.components.api-operations-debug.RequestParamsTab.ReadOnly": "只看必填", "apigw.components.api-operations-debug.RequestParamsTab.SampleValuePadding": "示例值填充", "apigw.components.api-operations-policy.AddPolicyPlugin.AddPolicy": "添加策略", "apigw.components.api-operations-policy.DeletePolicy.AreYouSureYouWant": "确定删除当前策略吗?", "apigw.components.api-operations-policy.DeletePolicy.AfterDeletionAllAssociatedCurrent": "删除后，所有已关联当前策略的资源将不再生效", "apigw.components.api-operations-policy.DeletePolicy.Delete": "删除", "apigw.components.api-operations-policy.DeletePolicy.PolicyDeleted": "删除策略成功", "apigw.components.api-operations-policy.DeletePolicy.ApiLevelPoliciesCannotBe": "接口不允许删除APi级策略，请切换到全部接口后操作", "apigw.components.api-operations-policy.EnterOrOutContent.Strategy": "策略", "apigw.components.api-operations-policy.EnterOrOutContent.PlugIn": "插件", "apigw.components.api-operations-policy.EnterOrOutContent.ApiLevel": "API级", "apigw.components.api-operations-policy.EnterOrOutContent.Enabled": "已启用", "apigw.components.api-operations-policy.EnterOrOutContent.NotEnabled": "未开启", "apigw.components.api-operations-policy.PolicyEnvList.NotPublished": "未发布", "apigw.components.api-operations-policy.PolicyPanel.Add": "添加", "apigw.components.api-operations-policy.PolicyPanel.Cancel": "取消", "apigw.components.api-operations-policy.FrontendApi": "前端API", "apigw.components.api-operations-policy.AllInterfaces": "全部接口", "apigw.components.api-operations-policy.InboundProcessing": "入站处理", "apigw.components.api-operations-policy.OutboundProcessing": "出站处理", "apigw.components.api-operations-policy.Backend": "后端服务", "apigw.components.api-operations-policy.ServiceType": "服务类型：", "apigw.components.api-operations-policy.Backend.1": "后端：", "apigw.apiDetails.publishDetail.TheApiHasNotBeen": "API暂未发布,请先", "apigw.apiDetails.publishDetail.PublishApi": "发布API", "apigw.apiDetails.publishDetail.NoApiExistsUnderThe": "当前API下不存在接口，暂不支持发布", "apigw.components.addVersionDialog.SupportsLettersDigitsCaseInsensitive": "支持英文字母、数字、\"-\"、\".\"，大小写不敏感，以英文字母或数字开头及结尾，不超过32个字符", "apigw.api-manage.components.constants.Example": "使用示例：", "apigw.api-manage.components.constants.AccessUrlHttpDomainName": "访问URL http://&lcub;域名&rcub;/&lcub;BasePath&rcub;/&lcub;版本号&rcub;/&lcub;接口路径&rcub; 如：curl http://aliyun.com/basepath/v1/user", "apigw.api-manage.components.constants.AddAVersionHeaderTo": "在请求中增加版本Header，如：curl -H \"version:v1\" http://aliyun.com/basepath/user", "apigw.api-manage.components.constants.AccessUrlHttpDomainName.1": "访问URL http://&lcub;域名&rcub;/&lcub;BasePath&rcub;/&lcub;接口路径&rcub;?&lcub;Query&rcub;=&lcub;版本号&rcub; 如：curl http://aliyun.com/basepath/user?version=v1", "apigw.components.createApiDialog.EditHttpApi": "编辑REST API", "apigw.components.createApiDialog.SupportsLettersDigitsCaseInsensitive": "支持英文字母、数字、\"-\"、\".\"，大小写不敏感，以英文字母或数字开头及结尾，不超过64个字符", "apigw.components.createApiDialog.SupportsLettersDigitsCaseInsensitive.1": "支持英文字母、数字、\"-\"、\".\"，大小写不敏感，以英文字母或数字开头及结尾，不超过32个字符", "apigw.api-manage.headBtn.Delete": "删除", "apigw.api-manage.headBtn.NoApiExistsUnderThe": "当前API下不存在接口，暂不支持发布", "apigw.headBtn.publish.ItemnameItemprotocol": "{itemName}（{itemProtocol}）", "apigw.headBtn.publish.ApiPublishingAlreadyExistsIn": "当前环境已存在API发布，再次发布将覆盖原有发布信息，请谨慎操作。", "apigw.headBtn.publish.SelectThePublishingEnvironmentFirst": "需先选择发布环境", "apigw.components.api-operations-debug.RequestParamsTab.IncorrectParameters": "参数填写有误", "apigw.components.api-operations-guide.NoApiExistsUnderThe": "当前API下不存在接口，暂不支持发布", "apigw.components.api-operations-policy.PolicyEnvList.CreateAnEnvironment.2": "创建环境", "apigw.components.api-operations-policy.PolicyEnvList.Republish": "重新发布", "apigw.components.api-operations-slide.CreateOrEditSlide.Cancel": "取消", "apigw.components.env-manage.EnvManageSlide.SupportsLettersDigitsCaseInsensitive": "支持英文字母、数字、\"-\"、\".\"，大小写不敏感，以英文字母或数字开头及结尾，不超过64个字符", "apigw.components.env-manage.EnvManageSlide.EnvironmentId": "环境ID", "apigw.components.env-manage.EnvManageTableProps.EnvironmentId": "环境ID", "apigw.components.env-manage.EnvManageTableProps.SearchEnvironmentName": "搜索环境名称", "apigw.components.env-manage.EnvManageTableProps.SearchForInstanceId": "搜索实例ID", "apigw.components.env-manage.EnvManageTableProps.SearchForInstanceName": "搜索实例名称", "apigw.shared.Medusa.CopyDownload": "文案下载", "apigw.containers.AppLayout.HelpDocument": "帮助文档", "apigw..InitialVersion": "初始版本", "apigw..Published": "已发布", "apigw..Version": "版本", "apigw..AddVersion": "添加版本", "apigw.components.VersionContent.EndpointnumEndpointnumpercent.1": "{endpointNum}（{endpointNumPercent}）", "apigw.pages.auth.ServiceAssociatedRole": "服务关联角色", "apigw.pages.auth.TheUserDoesNotHave": "用户没有创建服务关联角色的权限，请联系主账号或权限管理员授权当前用户AliyunAPIGFullAccess或创建服务关联角色的自定义权限策略。自定义权限策略的相关信息如下：", "apigw.pages.auth.PermissionDescriptionCloudNativeApi": "权限说明：云原生API网关使用此角色来访问您在其他云产品中的资源。", "apigw.pages.auth.Authorized": "已授权", "apigw.pages.auth.AuthorizeNow": "立即授权", "apigw.pages.auth.Complete": "完成", "apigw.pages.auth.CongratulationsAllTheProcessesFor": "恭喜您，开通和授权的所有流程已成功完成。", "apigw.pages.auth.ExperienceCloudNativeApiGateway": "立即体验云原生API网关", "apigw.pages.auth.WelcomeToCloudNativeApi": "欢迎访问云原生API网关", "apigw.api-manage.apiDetails.VersionInformation": "版本信息", "apigw.apiDetails.publishDetail.tableProps.FunctionName": "函数名称", "apigw.apiDetails.publishDetail.tableProps.FunctionAliasOrVersion": "函数别名或版本", "apigw.components.createApiDialog.TheApiConfigurationIsIncorrect": "api配置错误，请重新修改", "apigw.components.createApiDialog.TheProtocolCannotBeEmpty": "协议不能为空", "apigw.api-manage.headBtn.Prompt": "提示", "apigw.headBtn.publish.FunctionComputeFc": "函数计算 FC", "apigw.headBtn.publish.TheCurrentDomainNameProtocol": "当前域名协议与API协议不匹配，请重新选择域名", "apigw.headBtn.publish.ApiProtocol": "API协议", "apigw.components.api-operations-policy.PolicyPanel.Ok": "确定", "apigw.components.api-operations-slide.ApiBasicInfo.EnterTheCorrectInterfacePath": "请输入正确的接口路径", "apigw.components.api-operations-slide.CreateOrEditSlide.Ok": "确定", "apigw.api-manage.monitoring.ApiMonitoringDashboard": "API 监控大盘", "apigw.api-manage.monitoring.TheCurrentFeatureIsNot": "当前功能暂不可用。您需要首先发布 API 才能查看监控大盘。", "apigw.src.constants.Releasing": "释放中", "apigw.src.constants.FailedToRelease": "释放失败", "apigw.src.constants.Stopped": "停止中", "apigw.src.constants.StopFailed": "停止失败", "apigw.src.constants.Stopped.1": "已停止", "apigw.router.components.GoToTheMonitoringDashboard": "前往监控大盘", "apigw.components.EditForm.PermissionsAndRulesIndicatesThat": "permissions:\n# and_rules 表示下面的所有 rules 条件同时成立时执行鉴权\n- and_rules:\n    rules:\n      - url_path:\n          # 前缀匹配路径\n          path:\n            prefix: /\n      - header:\n          # 正则匹配\n          safe_regex_match:\n            regex: \"(exampleA\\\\.com|exampleB\\\\.com)\"\n          # 支持HTTP Pseudo-Header规范，可通过\":authority\"这个Header来获取域名\n          name: \":authority\"", "apigw.id.service.GoToTheMonitoringDashboard": "前往监控大盘", "apigw.components.createApiDialog.TheInterfaceDescriptionCannotExceed": "接口描述不能超过255个字符", "apigw.components.api-operations-slide.ApiBasicInfo.TheInterfaceDescriptionCannotExceed": "接口描述不能超过255个字符", "apigw.components.AuthContent.Yes": "是", "apigw.components.AuthContent.No": "否", "apigw.pages.auth.AServiceAssociationRoleIs": "将会自动创建一个服务关联角色（如果已经创建，则不会重复创建），便于您使用网关管理、API管理等功能。", "apigw.components.api-operations-policy.PolicyEnvList.View": "查看", "apigw.components.domain-certificate.DomainSlide.Ok": "确定", "apigw.components.selectScroll.PleaseSelect": "请选择", "apigw.components.selectScroll.popupContent.NoOption": "无选项", "apigw.components.selectScroll.popupContent.View": "查看", "apigw.components.selectScroll.popupContent.CreateAFunction": "创建函数", "apigw.shared.ExtendSelect.Loading": "加载中", "apigw.regionId.alarm.Alert": "告警", "apigw.pages.notAllowed.CloudNativeApiGatewayDoes": "云原生API网关暂未开启公测，敬请期待！", "apigw.src.sidebar.Alert": "告警", "apigw.api-operations-slide.api-definition-info.Mock.PleaseEnterTheResponseContent": "请输入响应内容", "apigw.components.domain-certificate.DomainTableProps.DomainNameId": "域名/ID", "apigw.shared.hooks.useSetTitle.CloudNativeApiGateway": "云原生API网关", "apigw.shared.hooks.useSetTitle.PublicBetaVersion": "公测版", "apigw.api.create.successfully": "API：{name}创建成功", "apigw.components.addVersionDialog.AddPath": "添加Path", "apigw.components.createApiDialog.BasePathMustStartWith": "Base Path 必须以 / 开头, 不超过64个字符", "apigw.components.createApiDialog.EnterBasepathForExampleBase": "请输入BasePath， 如：/base", "apigw.components.createApiDialog.AddPath": "添加Path", "apigw.api-manage.headBtn.PublishApi": "发布API", "apigw.api-manage.headBtn.EditApi": "编辑API", "apigw.api-manage.headBtn.SdkDocumentGeneration": "SDK&文档生成", "apigw.api-manage.headBtn.TheSdkDocumentHasBeen": "SDK&文档生成成功, 已自动开始下载", "apigw.publish.components.serviceTableProps.TheFormatIsDomainName.1": "格式为域名:Port（如aliyun.com:80），支持填写多个，以英文\",\"分隔，不能以http(s)://协议名开头", "apigw.headBtn.publish.ApiPublishedSuccessfully": "API发布成功", "apigw.headBtn.publish.AfterPublishingTheApiYou": "发布API后您可以：", "apigw.headBtn.publish.DebugInterfaceTestTheConnectivity": "1．调试接口：在线测试接口的连通性与功能", "apigw.headBtn.publish.DomainNameAccessBusinessAccess": "2．域名访问：通过绑定的域名进行业务访问", "apigw.headBtn.publish.IfNoDomainNameIs": "未选择域名时，将仅支持通过环境二级域名访问（用于测试场景）", "apigw.headBtn.publish.CurrentlyOnlyFunctionsWithHttp": "当前仅支持具有HTTP触发器的函数", "apigw.headBtn.sdk.TheSdkDocumentHasBeen": "SDK&文档生成成功, 已自动开始下载", "apigw.headBtn.sdk.DuringGenerationYouCanClose": "生成中，您可以关闭页面，生成后将自动下载。", "apigw.headBtn.sdk.GeneratedSuccessfully": "生成成功", "apigw.headBtn.sdk.Generating": "生成中", "apigw.headBtn.sdk.GenerateAndDownload": "生成并下载", "apigw.headBtn.sdk.Close": "关闭", "apigw.headBtn.sdk.CurrentApi": "当前API", "apigw.headBtn.sdk.ApiVersion": "API版本", "apigw.headBtn.sdk.SdkLanguage": "SDK语言", "apigw.api-manage.interfaceList.ApiOperationsContent.AllInterfacesApiLevelPolicies": "全部接口 (API级策略&插件配置)", "apigw.api-manage.interfaceList.ApiOperationsContent.Interface": "接口：", "apigw.api-manage.interfaceList.ApiOperationsContent.Multiple": "多条", "apigw.api-manage.interfaceList.ApiOperationsContent.DebuggingInterface": "调试接口", "apigw.api-manage.interfaceList.ApiOperationsMenu.AllInterfacesApiLevelPolicies": "全部接口 (API级策略&插件配置)", "apigw.api-manage.interfaceList.ApiOperationsMenu.DebuggingInterface": "调试接口", "apigw.api-manage.interfaceList.ApiOperationsMenu.ApiAndInterfaceRelationships": "API和接口的关系", "apigw.api-manage.interfaceList.ApiOperationsMenu.AnApiRepresentsASet": "API 代表具有相同 Base Path 前缀的 接口集合；接口代表特定的执行动作和路径。", "apigw.components.api-operations-action-button.DeleteOperation.DeleteAnInterface": "删除接口", "apigw.components.api-operations-action-button.EditOperation.EditInterface": "编辑接口", "apigw.components.api-operations-debug.OperationDebug.PrivateNetworkDebuggingIsNot": "(暂不支持私网调试)", "apigw.components.api-operations-guide.TheApiCorrespondsToA": "API对应一个Swagger文件，代表了具有相同Base Path前缀的接口集合。", "apigw.components.api-operations-guide.DefineAnExecutionActionAnd": "定义一个执行动作和路径，从而实现特定的业务逻辑和数据交换。一个API下可以添加多个接口。", "apigw.components.api-operations-guide.AddServicesAndPublishApis": "添加服务并发布API", "apigw.components.api-operations-guide.AddAServiceToPublish": "在「发布API」中添加服务，完成发布后，该API即可被调用。", "apigw.components.api-operations-guide.Steps": "步骤指引:", "apigw.components.api-operations-guide.StepGuide": "步骤指引", "apigw.components.api-operations-policy.AddServicesToPublishApis": "在「发布API」中添加服务", "apigw.components.api-operations-policy.PublishApi": "发布API", "apigw.components.api-operations-slide.CreateOrEditSlide.Add": "添加", "apigw.components.api-operations-slide.CreateOrEditSlide.AddAndContinue": "添加并继续", "apigw.components.env-manage.EnvManageTableProps.More": "更多", "apigw.components.plugin-manage.GatewayPluginConfig.RuleConfigurationGatewayid": "规则配置: {gatewayId}", "apigw.components.plugin-manage.GatewayPluginConfig.RuleActionLevel": "规则作用层级", "apigw.components.plugin-manage.GatewayPluginConfig.Version": "版本", "apigw.components.plugin-manage.GatewayPluginConfig.RoutingPlugInRules": "路由级插件规则", "apigw.components.plugin-manage.GatewayPluginConfig.TheRuleTakesEffectWhen": "作用在接口/路由上；当请求匹配到指定接口/路由时，规则生效。", "apigw.components.plugin-manage.GatewayPluginConfig.DomainLevelPlugInRules": "域名级插件规则", "apigw.components.plugin-manage.GatewayPluginConfig.ItWorksOnDomainNames": "作用在域名上；特定域名的匹配优先级高于泛域名。", "apigw.components.plugin-manage.GatewayPluginConfig.InstanceLevelPlugInRules": "实例级插件规则", "apigw.components.plugin-manage.GatewayPluginConfig.TheJobIsGloballyLocated": "作用在网关全局；当接口/路由和REST API以及域名级规则都没有匹配到的时候则匹配该规则。", "apigw.components.plugin-manage.GatewayPluginConfig.TheJobIsGloballyLocated.ai": "作用在网关全局；当路由和LLM API以及域名级规则都没有匹配到的时候则匹配该规则。", "apigw.components.plugin-manage.GatewayPluginConfig.AddRule": "添加规则", "apigw.components.plugin-manage.GatewayWithPlugin.GatewayIdGatewayName": "网关ID/网关名称", "apigw.components.plugin-manage.GatewayWithPlugin.Operation": "操作", "apigw.components.plugin-manage.GatewayWithPlugin.ViewConfiguration": "查看配置", "apigw.components.plugin-manage.PluginAttachTable.RuleContent": "规则内容", "apigw.components.plugin-manage.PluginAttachTable.EffectiveStatus": "生效状态", "apigw.components.plugin-manage.PluginAttachTable.Effective": "已生效", "apigw.components.plugin-manage.PluginAttachTable.Operation": "操作", "apigw.components.plugin-manage.PluginAttachTable.Edit": "编辑", "apigw.components.plugin-manage.PluginAttachTable.Disable": "禁用", "apigw.components.plugin-manage.PluginAttachTable.Delete": "删除", "apigw.components.plugin-manage.UploadCustomPlugin.Upload": "上传", "apigw.components.plugin-manage.UploadCustomPlugin.SelectLanguage": "选择语言", "apigw.components.plugin-manage.UploadCustomPlugin.PlugInInformation": "插件信息", "apigw.components.plugin-manage.UploadCustomPlugin.PlugInDocumentation": "插件文档", "apigw.components.plugin-manage.UploadCustomPlugin.VersionInformation": "版本信息", "apigw.components.plugin-manage.UploadCustomPlugin.WasmFile": "WASM文件", "apigw.components.plugin-manage.UploadCustomPlugin.VersionDescription": "版本描述", "apigw.policy.header-content.AtLeastOneHeaderRule": "必须配置至少一个header规则", "apigw.shared.ApiAndOperationRelation.TheRelationshipBetweenApiAnd": "API和接口的关系（说明仅适用于本产品）", "apigw.shared.ApiAndOperationRelation.Api": "API：", "apigw.shared.ApiAndOperationRelation.ApiDefinesTheInteractionBetween": "API定义了系统之间的交互方式.在本产品中，API对应一个Swagger文件，代表了具有相同BasePath前缀的接口集合。", "apigw.shared.ApiAndOperationRelation.Interface": "接口：", "apigw.shared.ApiAndOperationRelation.TheInterfaceRepresentsASpecific": "接口代表了特定的执行动作和路径，如GET/get，对应Swagger文件中具体的path。", "apigw.shared.ApiAndOperationRelation.AccessUrl": "访问URL：", "apigw.shared.ApiAndOperationRelation.HttpSExampleComBasepath": "http(s)://example.com/[BasePath]/[接口Path]", "apigw.shared.ApiAndOperationRelation.Close": "关闭", "apigw.shared.ApiAndOperationRelation.ApiAndInterfaceRelationships": "API和接口的关系", "apigw.shared.MockUser.SimulatingUser": "正在模拟用户", "apigw.shared.MockUser.PerspectiveProceedWithCautionDo": "视角。谨慎操作！禁止截图或录屏！", "apigw.shared.MockUser.ExitUserPerspective": "退出用户视角", "apigw.shared.NotFound.SorryTheAccessedPageDoes": "抱歉，访问的页面不存在", "apigw.shared.NotFound.ReturnToTheInstanceList": "返回实例列表页", "apigw.shared.PluginCardList.PluginConfig.EffectiveScope": "生效范围", "apigw.shared.PluginCardList.PluginConfig.Optional": "可选", "apigw.shared.PluginCardList.PluginConfig.Selected": "已选", "apigw.shared.PluginCardList.PluginConfig.PlugInRules": "插件规则", "apigw.shared.PluginCardList.Test": "测试1", "apigw.shared.PluginCardList.ThrottlingPlugInForApi": "限流插件，用于API流量控制", "apigw.shared.PluginCardList.Test.1": "测试2", "apigw.shared.PluginCardList.JwtVerificationPlugInTo": "JWT 校验插件，用于验证API调用的身份", "apigw.shared.ProductNotOpen.ProductNotActivated": "产品未开通", "apigw.shared.ProductNotOpen.Product": "产品", "apigw.shared.ProductNotOpen.NotActivatedPleaseGo": "未开通，请前往", "apigw.shared.ProductNotOpen.Activate": "开通", "apigw.apiName.apiId.ApiCreatedSuccessfully": "API创建成功", "apigw.apiName.apiId.AfterCreatingAnApiYou": "创建API后您可以：", "apigw.apiName.apiId.AddInterfacesYouCanAdd": "1．添加接口：您可以添加多个接口", "apigw.apiName.apiId.AddInterface": "添加接口", "apigw.apiName.apiId.PublishAnApiAfterYou": "2．发布API：添加接口后，即可添加服务并发布API", "apigw.apiName.apiId.PublishApi": "发布API", "apigw.components.EditForm.CurrentlyOnlyFunctionsWithHttp": "当前仅支持具有HTTP触发器的函数", "apigw.addOrEdit.components.K8SSource.WhetherToListenToK": "是否监听 K8s Ingress 资源变动，并同步到路由列表。", "apigw.addOrEdit.components.K8SSource.Close": "关闭", "apigw.addOrEdit.components.K8SSource.AfterClosingTheGatewayWill": "关闭后，网关将不再监听 K8s Ingress 的资源变化，并删除之前已经同步过的路由记录。", "apigw.addOrEdit.components.K8SSource.ThisOperationWillCauseProblems": "此操作将导致经由网关访问这些路由的请求出现问题，但原 K8s Ingress 配置不受影响，请谨慎执行。", "apigw.addOrEdit.components.K8SSource.Enable": "开启", "apigw.addOrEdit.components.K8SSource.WhenEnabledTheGatewayListens": "开启后，网关会监听 K8s Ingress 的资源变化，并同步到路由列表。当路由匹配规则一致时，在控制台创建的路由，其优先级高于从 Ingress 同步的路由。", "apigw.addOrEdit.components.K8SSource.ByDefaultTheSystemListens": "系统会默认监听集群中全部命名空间下的所有 Ingress 资源。如需指定监听范围，您可以", "apigw.addOrEdit.components.K8SSource.IfTheFollowingInformationIs": "如果配置了以下信息，请确保在 K8s 集群中这些资源不会被误删除，否则数据将会出错。", "apigw.addOrEdit.components.K8SSource.UpdateIngressStatus": "更新 Ingress Status", "apigw.addOrEdit.components.K8SSource.WhenEnabledIngressOfThe": "开启后，被监听的 Ingress 中的 Status Endpoint 将会被替换成网关入口的访问地址。", "apigw.components.addOrEdit.DisablingKSIngressResource": "正在关闭 K8s Ingress 资源监听。", "apigw.components.addOrEdit.AfterClosingTheGatewayWill": "关闭后，网关将不再监听 K8s Ingress 的资源变化，并删除之前已经同步过的路由记录。", "apigw.components.addOrEdit.ThisOperationWillCauseProblems": "此操作将导致经由网关访问这些路由的请求出现问题，但原 K8s Ingress 配置不受影响，请谨慎执行。", "apigw.plugin-manage.id.Enabled": "已启用", "apigw.plugin-manage.id.CloudNativeApiGateway": "云原生API网关", "apigw.plugin-manage.id.PlugInMarket": "插件", "apigw.plugin-manage.id.Document": "文档", "apigw.plugin-manage.id.Version": "版本", "apigw.apiName.publish.domain.has.already.been.published.in.the.environment": "域名 {domainName} 在当前API中已发布在环境 {envName} 中，当前API不可再使用该域名发布到其他环境。", "apigw.src.appConfig.Shenzhen": "深圳", "apigw.src.appConfig.Shanghai": "上海", "apigw.apiDetails.publishDetail.tableProps.PublicPrivateSecondaryDomainNames": "公网/私网二级域名", "apigw.apiDetails.publishDetail.tableProps.PublicNetwork": "公网", "apigw.apiDetails.publishDetail.tableProps.PrivateNetwork": "私网", "apigw.api-manage.apiList.ApiListTable.CreateAnApi": "创建API", "apigw.api-manage.apiList.ApiListTable.SelectEnvironment": "选择环境", "apigw.api-manage.apiList.ApiListTableProps.ApiName": "API名称", "apigw.api-manage.apiList.ApiListTableProps.ApiType": "API类型", "apigw.api-manage.apiList.ApiListTableProps.ApiDescription": "API描述", "apigw.api-manage.apiList.ApiListTableProps.Edit": "编辑", "apigw.api-manage.apiList.ApiListTableProps.AfterEnablingMultipleVersionsYou": "开启多版本后需要进入API详情页编辑", "apigw.api-manage.apiList.ApiListTableProps.Delete": "删除", "apigw.api-manage.apiList.ApiListTableProps.AfterYouEnableMultipleVersions": "开启多版本后需要进入API详情页删除", "apigw.api-manage.apiList.ApiListTableProps.EnterTheApiNameTo": "请输入要检索的API名称", "apigw.api-manage.apiList.ApiListTableProps.SelectTheApiTypeTo": "请选择要检索的API类型", "apigw.api-manage.apiMenu.CreateBasedOnAiA": "基于AI大模型创建", "apigw.api-manage.apiMenu.ItIsCreatedBasedOn": "通过自然语言描述，基于AI大模型创建。辅助您设计出更标准、更规范的API。", "apigw.components.addVersionDialog.TheHeaderCannotStartWith": "Header不允许以x-fc开头，该Header与调用FC函数的系统Header冲突, 请您调整", "apigw.components.conflictDetection.Precision": "精准", "apigw.components.conflictDetection.Prefix": "前缀", "apigw.components.conflictDetection.Regular": "正则", "apigw.components.conflictDetection.Serious": "严重", "apigw.components.conflictDetection.Warning": "警告", "apigw.components.conflictDetection.Information": "信息", "apigw.components.conflictDetection.CurrentResource": "当前资源", "apigw.components.conflictDetection.ConflictResources": "冲突资源", "apigw.components.conflictDetection.Routing": "路由", "apigw.components.conflictDetection.GatewayInstance": "网关实例", "apigw.components.conflictDetection.ConflictLevel": "冲突等级", "apigw.components.conflictDetection.Cause": "原因", "apigw.components.conflictDetection.PathAndParameterConflictPlease": "路径及参数冲突，请调整相关配置", "apigw.components.conflictDetection.PathConflictPleaseAdjustThe": "路径冲突，请调整相关配置", "apigw.components.conflictDetection.ExceptionPrompt": "异常提示", "apigw.components.conflictDetection.ContinuePublishing": "继续发布", "apigw.components.conflictDetection.TheConflictLevelIsSerious": "冲突等级存在“严重”, 暂无法发布", "apigw.components.conflictDetection.ResourceConflictsPleaseAdjustThe": "资源存在冲突，请您调整，冲突如下：", "apigw.createApiDialog.ai.PleaseCheckTheFormInformation": "请检查表单信息", "apigw.createApiDialog.ai.Inspiration": "灵感", "apigw.createApiDialog.ai.AiGeneratesASampleApi": "由AI为您生成一个示例API", "apigw.createApiDialog.ai.EnterTheApiName": "请填写API名称", "apigw.createApiDialog.ai.TheApiNameCannotBe": "API名称不能为空", "apigw.createApiDialog.ai.TheApiNameCannotExceed": "API名称不能超过64个字符", "apigw.createApiDialog.ai.Scenario": "场景", "apigw.createApiDialog.ai.PleaseEnterWhatServiceYour": "请填写您的API是关于什么服务", "apigw.createApiDialog.ai.TheSceneCannotBeEmpty": "场景不能为空", "apigw.createApiDialog.ai.TheSceneCannotExceedCharacters": "场景不能超过1000个字符", "apigw.createApiDialog.ai.Other": "其他", "apigw.createApiDialog.ai.YouCanFillInOther": "可填写其他补充内容", "apigw.createApiDialog.ai.OtherSupplementaryContentCannotExceed": "其他补充内容不能超过1000个字符", "apigw.createApiDialog.ai.Stop": "停止", "apigw.createApiDialog.ai.ExampleOfGeneratingSwagger": "生成 Swagger 示例", "apigw.createApiDialog.ai.tabContent.Name": "名称", "apigw.createApiDialog.ai.tabContent.EnterTheNameOfThe": "请填写数据对象的名称（如：图书）", "apigw.createApiDialog.ai.tabContent.TheNameCannotBeEmpty": "名称不能为空", "apigw.createApiDialog.ai.tabContent.TheNameCannotExceedCharacters": "名称不能超过64个字符", "apigw.createApiDialog.ai.tabContent.Properties": "属性", "apigw.createApiDialog.ai.tabContent.EnterTheAttributesSuchAs": "请填写数据对象包含的属性（如：书名、作者）,可直接粘贴数据模型相关的定义文本或代码", "apigw.createApiDialog.ai.tabContent.TheAttributeDescriptionCannotExceed": "属性描述不能超过1000个字符", "apigw.createApiDialog.ai.tabContent.Increase": "增", "apigw.createApiDialog.ai.tabContent.AddADescriptionOfThe": "新增资源接口描述，如根据ID、名称新增", "apigw.createApiDialog.ai.tabContent.TheInterfaceDescriptionCannotExceed": "接口描述不能超过1000个字符", "apigw.createApiDialog.ai.tabContent.Delete": "删", "apigw.createApiDialog.ai.tabContent.TheDescriptionOfTheDelete": "删除资源接口描述，如根据ID删除资源", "apigw.createApiDialog.ai.tabContent.Change": "改", "apigw.createApiDialog.ai.tabContent.ModifyTheResourceInterfaceDescription": "修改资源接口描述，如根据ID，修改资源名称", "apigw.createApiDialog.ai.tabContent.Check": "查", "apigw.createApiDialog.ai.tabContent.ViewTheDescriptionOfThe": "查看资源接口描述，如根据ID查询资源信息", "apigw.createApiDialog.ai.tabContent.SelectWhatYouWantTo": "勾选您想要对资源做哪些操作", "apigw.createApiDialog.apiPreCheck.cradTable.AllOperationTypes": "全部操作类型", "apigw.createApiDialog.apiPreCheck.cradTable.Add": "新增", "apigw.createApiDialog.apiPreCheck.cradTable.Update": "更新", "apigw.createApiDialog.apiPreCheck.cradTable.PleaseEnterTheRequestPath": "请输入请求Path搜索", "apigw.createApiDialog.apiPreCheck.cradTable.EnterAModelNameTo": "请输入模型名称搜索", "apigw.createApiDialog.apiPreCheck.SmartMerge": "智能合并", "apigw.createApiDialog.apiPreCheck.BasedOnExistingApisNew": "将在已存在的API基础之上，创建新增的接口，更新重复的接口，但不删除仅在原API中存在的接口。", "apigw.createApiDialog.apiPreCheck.ImportOnlyNewInterfaces": "仅导入新增接口", "apigw.createApiDialog.apiPreCheck.OnlyNewInterfacesAreCreated": "将在已存在的API基础之上，仅创建新增的接口，对重复或原有的接口不做处理。", "apigw.createApiDialog.apiPreCheck.OverwriteTheCurrentApi": "覆盖当前API", "apigw.createApiDialog.apiPreCheck.RecreateTheApiBasedOn": "基于当前导入的文件重新创建API，完全覆盖已有API。", "apigw.createApiDialog.apiPreCheck.OperationType": "操作类型", "apigw.createApiDialog.apiPreCheck.Update": "更新", "apigw.createApiDialog.apiPreCheck.Add": "新增", "apigw.createApiDialog.apiPreCheck.InterfaceName": "接口名称", "apigw.createApiDialog.apiPreCheck.RequestPath": "请求Path", "apigw.createApiDialog.apiPreCheck.Method": "方法", "apigw.createApiDialog.apiPreCheck.ErrorMessage": "错误信息", "apigw.createApiDialog.apiPreCheck.ModelName": "模型名称", "apigw.createApiDialog.apiPreCheck.OpenapiFilePrecheck": "OpenAPI文件预检", "apigw.createApiDialog.apiPreCheck.Creating": "创建中", "apigw.createApiDialog.apiPreCheck.CreateAnApi": "创建API", "apigw.createApiDialog.apiPreCheck.Cancel": "取消", "apigw.createApiDialog.apiPreCheck.PreCheckFailedPlease": "预检失败，请", "apigw.createApiDialog.apiPreCheck.Adjustment": "调整", "apigw.createApiDialog.apiPreCheck.ImportOnlyPrecheckSuccessfulData": "仅导入预检成功数据", "apigw.createApiDialog.apiPreCheck.TheWarningMessageHasBeen": "已阅读警告信息。", "apigw.createApiDialog.apiPreCheck.NoteThatOnlyPrecheckSuccessful": "注意，仅导入预检成功数据。", "apigw.createApiDialog.apiPreCheck.TheCurrentApiAlreadyExists": "当前API已存在", "apigw.createApiDialog.apiPreCheck.According": "根据", "apigw.createApiDialog.apiPreCheck.ApiName": "API名称", "apigw.createApiDialog.apiPreCheck.And": "和", "apigw.createApiDialog.apiPreCheck.Version": "版本", "apigw.createApiDialog.apiPreCheck.VerifyThatTheCurrentApi": "校验，当前API已存在，请选择合并逻辑。", "apigw.createApiDialog.apiPreCheck.ApiNameVersionAlreadyExists": "已存在API名称/版本", "apigw.createApiDialog.apiPreCheck.MergeLogic": "合并逻辑", "apigw.createApiDialog.apiPreCheck.ApiPrecheckFailed": "API预检失败", "apigw.createApiDialog.apiPreCheck.DataStructurePrecheckFailed": "数据结构预检失败", "apigw.createApiDialog.apiPreCheck.WarningMessage": "警告信息", "apigw.createApiDialog.apiPreCheck.ApiPrecheckSucceeded": "API预检成功", "apigw.createApiDialog.apiPreCheck.DataStructurePreCheckSucceeded": "数据结构预检成功", "apigw.components.createApiDialog.constants.ApiForManagingUserAccounts": "用于管理用户账户和个人资料的API。", "apigw.components.createApiDialog.constants.BasicUserInformation": "用户基础信息", "apigw.components.createApiDialog.constants.TheBasicUserInformationObject": "用户基础信息对象包含姓名、邮箱、电话号码、性别、出生日期等信息。", "apigw.components.createApiDialog.constants.RetrieveBasicUserInformationIncluding": "检索用户基础信息，包括获取所有用户的列表、通过用户ID查找特定用户、根据条件过滤用户信息。", "apigw.components.createApiDialog.constants.CreateANewUserRegister": "创建新用户、进行用户注册并建立初始账户。", "apigw.components.createApiDialog.constants.UpdateBasicUserInformationSuch": "更新用户基础信息，如修改用户的姓名、邮箱等。", "apigw.components.createApiDialog.constants.DeleteAUserFromThe": "删除用户自账户，并可选择是否删除与用户相关的所有数据。", "apigw.components.createApiDialog.constants.UserPreferences": "用户偏好设置", "apigw.components.createApiDialog.constants.UserPreferencesAreUsedTo": "用户偏好设置对象用于存储用户的个性化设置，如语言、主题、通知设置等。", "apigw.components.createApiDialog.constants.ObtainsTheUserSCurrent": "获取用户当前的偏好设置，包括语言、主题和通知选项。", "apigw.components.createApiDialog.constants.UpdateTheUserSPreferences": "更新用户的偏好设置，如改变语言或主题，调整通知接收方式。", "apigw.components.createApiDialog.constants.UserSecuritySettings": "用户安全设置", "apigw.components.createApiDialog.constants.TheUserSecuritySettingsObject": "用户安全设置对象用于管理与账户安全相关的信息，如密码、两步验证、密码恢复设置等。", "apigw.components.createApiDialog.constants.TheUserResetsThePassword": "用户重置密码、启用或禁用两步验证、发送密码恢复邮件。", "apigw.components.createApiDialog.constants.UpdateTheUserSSecurity": "更新用户的安全设置，例如更改密码和调整两步验证选项。", "apigw.components.createApiDialog.constants.UserActivityLog": "用户活动日志", "apigw.components.createApiDialog.constants.UserActivityLogObjectsStore": "用户活动日志对象存储用户的操作记录，帮助监控用户行为与安全审计。", "apigw.components.createApiDialog.constants.ObtainUserActivityLogsIncluding": "获取用户的活动日志，包括登录历史、操作记录等，以供审核和分析。", "apigw.components.createApiDialog.constants.DeletesAUserSSpecific": "根据条件删除用户的特定活动记录。", "apigw.components.createApiDialog.constants.ThisApiDesignSupportsOauth": "此API设计支持OAuth 2.0认证机制，确保用户数据的安全性，并需要实现数据加密和访问控制功能，以防止未经授权的访问。同时，API应支持日志记录和监控，便于跟踪用户活动和审计。", "apigw.components.createApiDialog.constants.ApiForManagingUserOrders": "用于管理用户订单和交易的API。", "apigw.components.createApiDialog.constants.Order": "订单", "apigw.components.createApiDialog.constants.TheOrderObjectStoresThe": "订单对象用于存储订单的基本信息，如订单ID、用户ID、商品列表、订单状态、总金额等。", "apigw.components.createApiDialog.constants.RetrieveTheOrderListRetrieve": "检索订单列表、根据ID检索特定订单、根据用户ID过滤用户的订单信息。", "apigw.components.createApiDialog.constants.CreateANewOrderIncluding": "创建新订单，包括订单的详细信息与商品的添加。", "apigw.components.createApiDialog.constants.UpdateOrderInformationSuchAs": "更新订单信息，例如修改订单状态或支付信息。", "apigw.components.createApiDialog.constants.DeleteTheSpecifiedOrderAnd": "删除指定订单，并可选择取消相关交易。", "apigw.components.createApiDialog.constants.OrderStatus": "订单状态", "apigw.components.createApiDialog.constants.ManageTheDifferentStatusOf": "管理订单的不同状态，以便跟踪订单的处理进度。", "apigw.components.createApiDialog.constants.ObtainAListOfAll": "获取所有可能的订单状态列表以及当前订单的状态信息。", "apigw.components.createApiDialog.constants.AddACustomOrderStatus": "新增自定义订单状态或更新现有状态的描述和分类。", "apigw.components.createApiDialog.constants.DeleteTheOrderStatusThat": "删除不再需要的订单状态。", "apigw.components.createApiDialog.constants.PaymentInformation": "支付信息", "apigw.components.createApiDialog.constants.StorePaymentInformationRelatedTo": "存储与订单相关的支付信息，包括支付方式、支付状态、支付时间等。", "apigw.components.createApiDialog.constants.RetrieveThePaymentInformationOf": "检索订单的支付信息，包括最近的支付记录和相关状态。", "apigw.components.createApiDialog.constants.SubmitNewPaymentInformationTo": "提交新支付信息，以记录新的交易细节。", "apigw.components.createApiDialog.constants.TheApiMustHaveWebhook": "该API需具备Webhook功能，以便在订单状态变更时实时通知相关系统，并需记录系统操作日志以支持安全审计与分析。", "apigw.components.createApiDialog.constants.ApiForManagingLibraryStorage": "用于管理图书库存和借阅信息的API。", "apigw.components.createApiDialog.constants.Book": "图书", "apigw.components.createApiDialog.constants.TheBookObjectIsUsed": "图书对象用于存储图书的基本信息，如书名、作者、ISBN、出版日期、库存数量等。", "apigw.components.createApiDialog.constants.RetrieveAListOfBooks": "检索图书列表、根据ID检索特定图书以及根据条件过滤图书信息。", "apigw.components.createApiDialog.constants.AddNewBooksAndImport": "添加新图书、批量导入图书信息。", "apigw.components.createApiDialog.constants.UpdateTheBookInformationIncluding": "更新图书信息，包括修改书名、作者、库存数量等。", "apigw.components.createApiDialog.constants.DeleteABookRecordAnd": "删除图书记录，并可选择删除图书相关信息。", "apigw.components.createApiDialog.constants.Borrowing": "借阅", "apigw.components.createApiDialog.constants.TheBorrowingObjectIsUsed": "借阅对象用于管理用户借阅图书的信息，包括借阅日期、归还日期、用户ID等。", "apigw.components.createApiDialog.constants.QueryBorrowingRecordsRetrieveThe": "查询借阅记录、根据用户ID检索其借阅的图书信息，以及根据图书ID查询借阅状态。", "apigw.components.createApiDialog.constants.UsersBorrowBooksAndRecord": "用户借阅图书，并记录借阅信息。", "apigw.components.createApiDialog.constants.UpdateBorrowingInformationIncludingModifying": "更新借阅信息，包括修改归还日期、用户信息等。", "apigw.components.createApiDialog.constants.CancelBorrowingRecordsAndSelect": "取消借阅记录，并可选择更新图书库存状态。", "apigw.components.createApiDialog.constants.Classification": "分类", "apigw.components.createApiDialog.constants.BookClassificationObjectsAreUsed": "图书分类对象用于管理图书的类别信息，如分类名称、描述等。", "apigw.components.createApiDialog.constants.QueryTheBookClassificationList": "查询图书分类列表、根据ID获取特定分类信息。", "apigw.components.createApiDialog.constants.AddNewCategoriesAndImport": "添加新分类、批量导入分类信息。", "apigw.components.createApiDialog.constants.UpdateTheClassificationInformationIncluding": "更新分类信息，包括修改分类名称和描述。", "apigw.components.createApiDialog.constants.DeleteACategoryRecordAnd": "删除分类记录，并可选择删除分类下的图书信息。", "apigw.components.createApiDialog.constants.SupportOverdueRemindersForBook": "需支持图书借阅的逾期提醒、用户借阅限额管理功能，并保持操作日志以便进行审计和分析。", "apigw.components.createApiDialog.constants.ApiForManagingAccountingBooks": "用于管理会计账本的API。", "apigw.components.createApiDialog.constants.AccountBook": "账本", "apigw.components.createApiDialog.constants.TheLedgerObjectIsUsed": "账本对象用于存储会计记录，包括日期、交易类型、金额、账户等基本信息。", "apigw.components.createApiDialog.constants.RetrieveTheLedgerListObtain": "检索账本列表、根据ID获取特定账本信息、根据日期范围或其他条件过滤账本记录。", "apigw.components.createApiDialog.constants.CreateANewLedgerRecord": "创建新的账本记录，支持添加交易信息和初始余额。", "apigw.components.createApiDialog.constants.UpdateExistingLedgerRecordsIncluding": "更新现有账本记录，包括修改交易信息和金额。", "apigw.components.createApiDialog.constants.DeleteSpecificLedgerRecordsAnd": "删除特定账本记录，并可选择同时删除相关的交易信息。", "apigw.components.createApiDialog.constants.Account": "账户", "apigw.components.createApiDialog.constants.AccountObjectsAreUsedTo": "账户对象用于管理不同类型的账户信息，包括账户名称、账户类型、余额等。", "apigw.components.createApiDialog.constants.ObtainAListOfAccounts": "获取账户列表、根据ID检索特定账户信息以及根据账户类型过滤账户。", "apigw.components.createApiDialog.constants.ToCreateANewAccount": "创建新账户，支持设置账户类别和初始余额。", "apigw.components.createApiDialog.constants.UpdateAccountInformationIncludingAccount": "更新账户信息，包括修改账户名称、账户类型等。", "apigw.components.createApiDialog.constants.ToDeleteASpecificAccount": "删除特定账户，需确认是否删除与该账户相关的所有交易记录。", "apigw.components.createApiDialog.constants.Transaction": "交易", "apigw.components.createApiDialog.constants.TheTransactionObjectIsUsed": "交易对象用于记录每一笔账务交易，包括交易日期、金额、相关账户和备注等信息。", "apigw.components.createApiDialog.constants.RetrieveTransactionListsObtainSpecific": "检索交易列表、根据ID获取特定交易详情、根据条件过滤交易记录（如日期范围、金额区间等）。", "apigw.components.createApiDialog.constants.CreateANewTransactionRecord": "创建新交易记录，支持添加交易备注、选择涉及账户。", "apigw.components.createApiDialog.constants.UpdateExistingTransactionsIncludingModifying": "更新现有交易记录，包括修改交易信息和备注。", "apigw.components.createApiDialog.constants.ToDeleteASpecificTransaction": "删除特定交易记录，需确认是否删除与其相关的账务影响。", "apigw.components.createApiDialog.constants.DataRollbackIsRequiredTo": "需支持数据的回滚功能，保证会计记录的可靠性和一致性，同时记录每次操作的审计日志，以便审计和追踪。", "apigw.components.createApiDialog.constants.ApiForManagingAttendanceRecords": "用于管理企业员工的考勤记录及相关操作的API。", "apigw.components.createApiDialog.constants.Employee": "员工", "apigw.components.createApiDialog.constants.EmployeeObjectsAreUsedTo": "员工对象用于存储员工的基本信息，如姓名、工号、部门、职位等。", "apigw.components.createApiDialog.constants.RetrieveEmployeeListsRetrieveSpecific": "检索员工列表、根据ID检索特定员工信息、根据部门或职位过滤员工信息。", "apigw.components.createApiDialog.constants.AddNewEmployeesAndUpdate": "添加新员工、更新员工基本信息，包括调动部门或职位。", "apigw.components.createApiDialog.constants.DeleteEmployeeRecordsAndSelect": "删除员工记录，并可选择是否删除与员工相关的考勤数据。", "apigw.components.createApiDialog.constants.AttendanceRecord": "考勤记录", "apigw.components.createApiDialog.constants.TheAttendanceRecordObjectIs": "考勤记录对象用于存储员工的考勤数据，包括打卡时间、请假记录、缺勤原因等。", "apigw.components.createApiDialog.constants.ObtainEmployeeAttendanceRecordsFilter": "获取员工的考勤记录、根据日期范围筛选考勤数据、获取总的考勤统计信息。", "apigw.components.createApiDialog.constants.RecordTheAttendanceClockIn": "记录员工的考勤打卡信息，包括上班打卡和下班打卡。", "apigw.components.createApiDialog.constants.UpdateAttendanceRecordsSuchAs": "更新考勤记录，如修改打卡时间、添加请假或缺勤记录。", "apigw.components.createApiDialog.constants.YouCanDeleteSpecificAttendance": "删除特定的考勤记录，支持批量删除操作。", "apigw.components.createApiDialog.constants.LeaveManagement": "请假管理", "apigw.components.createApiDialog.constants.LeaveManagementObjectsAreUsed": "请假管理对象用于处理员工的请假申请，包括请假类型、请假时间、审核状态等。", "apigw.components.createApiDialog.constants.ObtainEmployeeLeaveApplicationRecords": "获取员工的请假申请记录、根据请假状态过滤结果、获取请假类型列表。", "apigw.components.createApiDialog.constants.CreateANewLeaveApplication": "创建新的请假申请、提交请假申请审核。", "apigw.components.createApiDialog.constants.UpdateTheStatusOfThe": "更新请假申请状态，例如审核通过或驳回申请。", "apigw.components.createApiDialog.constants.CancelTheSubmittedLeaveApplication": "撤销已提交的请假申请。", "apigw.components.createApiDialog.constants.TheSystemNeedsToIntegrate": "系统需要集成考勤异常提醒功能，并记录员工考勤操作日志，以便进行数据审计和行为分析。", "apigw.components.createApiDialog.constants.TheApiUsedToManage": "用于管理企业报销流程的API。", "apigw.components.createApiDialog.constants.ReimbursementApplication": "报销申请", "apigw.components.createApiDialog.constants.TheObjectOfTheReimbursement": "报销申请对象用于存储报销的基本信息，如申请人、报销金额、报销类型等。", "apigw.components.createApiDialog.constants.SearchTheReimbursementApplicationList": "检索报销申请列表、根据ID检索特定申请、根据条件过滤申请信息。", "apigw.components.createApiDialog.constants.CreateANewReimbursementRequest": "创建新报销申请，提交报销请求。", "apigw.components.createApiDialog.constants.UpdateTheReimbursementInformationIncluding": "更新报销申请信息，包括修改申请金额、变更报销类型等。", "apigw.components.createApiDialog.constants.DeletesASpecificReimbursementApplication": "删除特定报销申请，允许撤销已提交的申请。", "apigw.components.createApiDialog.constants.ReimbursementReview": "报销审核", "apigw.components.createApiDialog.constants.TheReimbursementAuditObjectIs": "报销审核对象用于管理各个审核阶段的信息，如审核人、审核意见、审核状态等。", "apigw.components.createApiDialog.constants.ObtainTheReimbursementApplicationReview": "获取待审核的报销申请、审核历史记录和审核动态信息。", "apigw.components.createApiDialog.constants.SubmitReviewResultsIncludingConsent": "提交审核结果，包括同意、拒绝及相关备注。", "apigw.components.createApiDialog.constants.UpdateReviewInformationModifyReview": "更新审核信息，修改审核意见或重新分配审核人。", "apigw.components.createApiDialog.constants.ReimbursementRecord": "报销记录", "apigw.components.createApiDialog.constants.TheReimbursementRecordObjectIs": "报销记录对象用于追踪初始化的报销申请，包含申请状态、结算时间、支付方式等。", "apigw.components.createApiDialog.constants.RetrieveTheReimbursementRecordList": "检索报销记录列表，获取已处理的报销申请信息。", "apigw.components.createApiDialog.constants.MultiLevelAuditAndPermission": "需具备多级审核和权限控制功能，以确保报销流程的安全性和效率。同时，API需支持日志记录，以便于对报销操作进行追踪和审计。", "apigw.components.createApiDialog.constants.TheApiUsedToManage.1": "用于管理电子商务商品库存的API。", "apigw.components.createApiDialog.constants.Commodity": "商品", "apigw.components.createApiDialog.constants.ProductObjectsAreUsedTo": "商品对象用于存储商品的基本信息，如名称、描述、价格、库存数量、分类等。", "apigw.components.createApiDialog.constants.RetrieveAListOfProducts": "检索商品列表、根据ID检索特定商品、根据条件过滤商品信息。", "apigw.components.createApiDialog.constants.AddNewProductsAndPut": "添加新商品、商品上架。", "apigw.components.createApiDialog.constants.UpdateProductInformationIncludingModifying": "更新商品信息，包括修改价格、库存数量、商品描述等。", "apigw.components.createApiDialog.constants.ToDeleteAProductYou": "删除商品，能够选择删除相关的图片和描述等数据。", "apigw.components.createApiDialog.constants.Inventory": "库存", "apigw.components.createApiDialog.constants.TheInventoryObjectIsUsed": "库存对象用于管理每个商品的存货数量及入库、出库记录。", "apigw.components.createApiDialog.constants.ObtainTheInventoryInformationAnd": "获取特定商品的库存信息、历史库存变动记录。", "apigw.components.createApiDialog.constants.RecordTheProductInStockroom": "记录商品入库操作，更新库存数量。", "apigw.components.createApiDialog.constants.RevokeSpecificInventoryChangeRecords": "撤销特定库存变动记录，避免错误库存操作的影响。", "apigw.components.createApiDialog.constants.TheClassificationObjectIsUsed": "分类对象用于组织和管理商品的分类信息，包含分类名称及描述。", "apigw.components.createApiDialog.constants.RetrieveTheClassificationListAnd": "检索分类列表、获取特定分类信息。", "apigw.components.createApiDialog.constants.CreateNewProductCategoriesAnd": "创建新的商品分类、更新分类信息。", "apigw.components.createApiDialog.constants.DeleteASpecificProductCategory": "删除特定商品分类，并可选择将商品迁移至默认分类。", "apigw.components.createApiDialog.constants.TheBatchOperationFunctionIs": "需支持批量操作功能，方便同时管理多个商品的入库、出库及更新操作。同时，必须记录库存变动日志，以便进行审计和问题追踪。", "apigw.components.createApiDialog.constants.TheApiUsedToManage.2": "用于管理商品进出口报关流程的API。", "apigw.components.createApiDialog.constants.TheProductObjectStoresBasic": "商品对象用于存储商品的基本信息，如名称、规格、数量、价格、原产地等。", "apigw.components.createApiDialog.constants.SearchTheListOfProducts": "检索商品列表、根据ID检查特定商品的详细信息，以及根据条件进行商品信息过滤。", "apigw.components.createApiDialog.constants.CreateNewProductRecordsFor": "创建新商品记录，用于进出口申报。", "apigw.components.createApiDialog.constants.UpdateProductInformationIncludingPrice": "更新商品信息，包括修改价格、数量、规格及其它相关信息。", "apigw.components.createApiDialog.constants.DeleteProductRecordsThatAre": "删除不再需要的商品记录。", "apigw.components.createApiDialog.constants.CustomsDeclarationForm": "报关单", "apigw.components.createApiDialog.constants.CustomsDeclarationObjectsAreUsed": "报关单对象用于管理和存储与商品申报相关的文档信息，如报关单编号、申报日期、状态等。", "apigw.components.createApiDialog.constants.ObtainAListOfCustoms": "获取报关单列表，查看某个报关单的详细信息，以及根据条件筛选报关单信息。", "apigw.components.createApiDialog.constants.CreateANewCustomsDeclaration": "创建新的报关单，提交商品信息用于海关审批。", "apigw.components.createApiDialog.constants.UpdateTheStatusOfThe.1": "更新报关单状态或相关信息，例如补充文档或修改申报内容。", "apigw.components.createApiDialog.constants.WithdrawOrDeleteCustomsDeclarations": "撤回或删除不再需要的报关单。", "apigw.components.createApiDialog.constants.CustomsDeclarationHistory": "报关历史", "apigw.components.createApiDialog.constants.TheCustomsDeclarationHistoryObject": "报关历史对象用于记录已完成的报关操作的信息，包括时间、状态、处理人员等。", "apigw.components.createApiDialog.constants.ObtainTheCustomsDeclarationHistory": "获取商品的报关历史记录，查看特定报关单的处理过程及状态。", "apigw.components.createApiDialog.constants.CustomsStatus": "海关状态", "apigw.components.createApiDialog.constants.TheCustomsStatusObjectIs": "海关状态对象用于跟踪每个报关单的实时状态，包括待审核、已审核、放行等信息。", "apigw.components.createApiDialog.constants.ObtainTheCustomsStatusInformation": "获取报关单的海关状态信息，以便提供实时更新给用户。", "apigw.components.createApiDialog.constants.UpdateTheCustomsStatusInformation": "更新海关状态信息，以反映处理过程中的状态更改。", "apigw.components.createApiDialog.constants.TheApiMustHaveMulti": "该API需具备多语言支持，并能够记录报关操作日志，以便进行审计和安全分析，同时应允许数据接口集成海关系统，实现自动化处理。", "apigw.components.createApiDialog.constants.ApiForManagingTravelPlans": "用于管理旅游出行计划的API。", "apigw.components.createApiDialog.constants.TravelPlan": "旅游计划", "apigw.components.createApiDialog.constants.TheTravelPlanObjectIs": "旅游计划对象用于存储用户的出行信息，包括目的地、出发日期、返回日期、交通方式、住宿信息等。", "apigw.components.createApiDialog.constants.RetrieveAllTravelPlansObtain": "检索所有旅游计划、根据ID获取特定旅游计划、根据条件筛选旅游计划。", "apigw.components.createApiDialog.constants.CreateANewTravelPlan": "创建新的旅游计划，包括添加目的地、日期、交通及住宿等信息。", "apigw.components.createApiDialog.constants.UpdateTheInformationOfThe": "更新旅游计划的信息，如修改出发或返回日期、更新目的地等。", "apigw.components.createApiDialog.constants.IfYouDeleteASpecific": "删除特定的旅游计划，用户可选择是否删除与之关联的所有信息。", "apigw.components.createApiDialog.constants.TrafficInformation": "交通信息", "apigw.components.createApiDialog.constants.ItIsUsedToManage": "用于管理用户的交通信息，包括航班、火车、汽车租赁等相关详情。", "apigw.components.createApiDialog.constants.ObtainAllTrafficInformationOf": "获取用户的所有交通信息，支持根据ID或条件检索特定交通安排。", "apigw.components.createApiDialog.constants.AddNewTrafficInformationSuch": "添加新的交通信息，如预订航班或火车票等。", "apigw.components.createApiDialog.constants.DeleteSpecificTrafficInformationIncluding": "删除特定的交通信息，包括已预订或计划的交通安排。", "apigw.components.createApiDialog.constants.AccommodationInformation": "住宿信息", "apigw.components.createApiDialog.constants.ManageUsersAccommodationInformationIncluding": "管理用户的住宿信息，包括酒店、民宿、旅馆等的预订和取消情况。", "apigw.components.createApiDialog.constants.YouCanObtainAllAccommodation": "获取用户的所有住宿信息，支持按条件筛选住宿记录。", "apigw.components.createApiDialog.constants.AddANewAccommodationReservation": "添加新的住宿预订，包括酒店名称、入住日期、退房日期等信息。", "apigw.components.createApiDialog.constants.IfYouCancelASpecific": "取消特定的住宿预订，用户可选择是否保留相关信息。", "apigw.components.createApiDialog.constants.TourismActivities": "旅游活动", "apigw.components.createApiDialog.constants.ManageUsersTravelActivityInformation": "管理用户的旅游活动信息，包括景点门票、导游服务、活动预约等。", "apigw.components.createApiDialog.constants.ObtainAllTravelActivityRecords": "获取用户的所有旅游活动记录，可根据条件进行筛选。", "apigw.components.createApiDialog.constants.AddTravelActivityInformationTo": "新增旅游活动信息，支持预订门票、导游等服务。", "apigw.components.createApiDialog.constants.YouCanSelectWhetherTo": "删除已预订的旅游活动信息，用户可选择是否删除相关记录。", "apigw.components.createApiDialog.constants.UserAuthenticationAndPermissionManagement": "需要具备用户身份验证和权限管理功能，以确保敏感信息的安全性。此外，应记录用户的所有操作日志，以便进行安全审计和行为分析。", "apigw.components.createApiDialog.constants.ApiForManagingProductDiscounts": "用于管理商品折扣和促销活动的API。", "apigw.components.createApiDialog.constants.TheProductObjectStoresThe": "商品对象用于存储商品的基本信息，如名称、价格、库存、描述等。", "apigw.components.createApiDialog.constants.RetrieveProductListsRetrieveSpecific": "检索商品列表、根据ID检索特定商品以及根据条件过滤商品信息。", "apigw.components.createApiDialog.constants.CreateANewProductAdd": "创建新商品，添加商品信息并设置默认状态。", "apigw.components.createApiDialog.constants.UpdateProductInformationIncludingModifying.1": "更新商品信息，包括修改价格、库存、描述等。", "apigw.components.createApiDialog.constants.DeleteTheProductRecordAnd": "删除商品记录，清除商品的所有相关信息。", "apigw.components.createApiDialog.constants.DiscountActivity": "折扣活动", "apigw.components.createApiDialog.constants.TheDiscountActivityObjectStores": "折扣活动对象用于存储促销活动的相关信息，如活动名称、开始时间、结束时间、适用商品等。", "apigw.components.createApiDialog.constants.ObtainsAListOfActivities": "获取活动列表、根据ID检索特定活动，以及根据条件过滤活动信息。", "apigw.components.createApiDialog.constants.CreateANewDiscountActivity": "创建新的折扣活动，包括设置折扣率、适用商品和活动时间等。", "apigw.components.createApiDialog.constants.UpdateTheDetailsOfExisting": "更新现有折扣活动的详细信息，包括调整折扣率、修改活动时间等。", "apigw.components.createApiDialog.constants.DeleteADiscountActivityAnd": "删除折扣活动，撤销该活动及其所有应用。", "apigw.components.createApiDialog.constants.ApplicableProductsForActivities": "活动适用商品", "apigw.components.createApiDialog.constants.ItIsUsedToRecord": "用于记录哪些商品可以参与特定折扣活动，包括商品ID和活动ID等信息。", "apigw.components.createApiDialog.constants.AddApplicableProductsForA": "为指定活动添加适用商品，建立商品与活动间的关系。", "apigw.components.createApiDialog.constants.RemoveSpecificProductsFromThe": "从活动中撤除特定商品，解除商品与活动的关联。", "apigw.components.createApiDialog.constants.ItIsNecessaryToRecord": "需支持记录活动的参与情况和用户反馈，以便进行效果评估和后续活动优化。同时，确保仅授权用户可以创建、编辑或删除折扣活动。", "apigw.components.createApiDialog.constants.ApisForManagingTaxiServices": "用于管理打车服务的API，包括用户、司机和行程等功能。", "apigw.components.createApiDialog.constants.User": "用户", "apigw.components.createApiDialog.constants.TheUserObjectContainsBasic": "用户对象包含用户的基本信息，如姓名、电话号码、邮箱、位置等。", "apigw.components.createApiDialog.constants.RetrieveTheListOfUsers": "检索用户列表、根据ID检索特定用户、获取用户的行程历史。", "apigw.components.createApiDialog.constants.UserRegistrationUserLoginAnd": "用户注册、用户登录以及更新用户位置信息。", "apigw.components.createApiDialog.constants.UpdateUserInformationSuchAs": "更新用户信息，例如修改密码、更新联系方式等。", "apigw.components.createApiDialog.constants.DeleteAUserAccountAnd": "删除用户账户，并可选择删除用户的相关数据。", "apigw.components.createApiDialog.constants.Driver": "司机", "apigw.components.createApiDialog.constants.TheDriverObjectContainsThe": "司机对象包含司机的基本信息，如姓名、车牌、车型、评级等。", "apigw.components.createApiDialog.constants.RetrieveTheListOfDrivers": "检索司机列表、根据ID检索特定司机、获取司机的行程历史。", "apigw.components.createApiDialog.constants.DriverRegistrationDriverLoginAnd": "司机注册、司机登录，以及更新司机的状态（在线/离线）。", "apigw.components.createApiDialog.constants.UpdateDriverInformationSuchAs": "更新司机信息，如修改车辆信息、更新联系方式等。", "apigw.components.createApiDialog.constants.DeleteTheDriverAccountAnd": "删除司机账户，并可选择删除相关的行程记录。", "apigw.components.createApiDialog.constants.Travel": "行程", "apigw.components.createApiDialog.constants.TheTravelObjectIsUsed": "行程对象用于存储用户的打车信息，包括起点、终点、费用、司机信息等。", "apigw.components.createApiDialog.constants.ObtainTheItineraryListOf": "获取当前用户的行程列表、根据ID检索特定行程信息。", "apigw.components.createApiDialog.constants.CreateANewTripRequest": "创建新的行程请求，用户选择起点和终点，并匹配合适司机。", "apigw.components.createApiDialog.constants.UpdateTheTravelStatusSuch": "更新行程状态，例如取消行程、确认行程等。", "apigw.components.createApiDialog.constants.DeleteASpecificTripAnd": "删除特定行程，并可选择清除相关数据。", "apigw.components.createApiDialog.constants.Payment": "支付", "apigw.components.createApiDialog.constants.ThePaymentObjectIsUsed": "支付对象用于管理用户的支付信息和交易记录。", "apigw.components.createApiDialog.constants.ObtainTheUserSPayment": "获取用户的支付记录和账户余额。", "apigw.components.createApiDialog.constants.CreateAPaymentRequestTo": "创建支付请求以完成行程费用的支付。", "apigw.components.createApiDialog.constants.UpdateThePaymentMethodSuch": "更新支付方式，如绑定新的信用卡或借记卡。", "apigw.components.createApiDialog.constants.DeleteASpecificPaymentRecord": "删除特定的支付记录。", "apigw.components.createApiDialog.constants.RealTimeLocationTrackingIs": "需要具备实时位置跟踪功能，以便在行程中为用户和司机提供定位服务，并记录所有操作以便进行安全审计和数据分析。", "apigw.components.createApiDialog.constants.ApiForManagingSalesInventory": "用于管理水果门店的销售、库存和订单的API。", "apigw.components.createApiDialog.constants.Fruit": "水果", "apigw.components.createApiDialog.constants.FruitObjectsAreUsedTo": "水果对象用于存储水果的基本信息，如名称、种类、价格、库存量等。", "apigw.components.createApiDialog.constants.RetrieveAllFruitListsRetrieve": "检索所有水果列表、根据ID检索特定水果、根据条件过滤水果信息。", "apigw.components.createApiDialog.constants.AddNewFruitsAndUpdate": "添加新水果、更新水果信息，包括价格和库存量。", "apigw.components.createApiDialog.constants.DeleteSpecificFruitInformationAnd": "删除特定水果信息，清理不再销售的水果。", "apigw.components.createApiDialog.constants.TheOrderObjectStoresThe.1": "订单对象用于存储客户的订单信息，包括订单ID、客户信息、购买的水果及数量、订单状态等。", "apigw.components.createApiDialog.constants.ObtainAListOfAll.1": "获取所有订单列表、根据ID检索特定订单、根据状态过滤订单。", "apigw.components.createApiDialog.constants.CreateNewOrdersSubmitOrder": "创建新订单、提交订单信息，进行订单结算。", "apigw.components.createApiDialog.constants.UpdateTheOrderStatusSuch": "更新订单状态，如确认订单、发货、取消订单等。", "apigw.components.createApiDialog.constants.CancelASpecificOrderAnd": "取消特定订单，清除订单记录。", "apigw.components.createApiDialog.constants.Customer": "客户", "apigw.components.createApiDialog.constants.CustomerObjectsAreUsedTo": "客户对象用于存储客户的基本信息，如姓名、联系方式、购买历史等。", "apigw.components.createApiDialog.constants.ObtainAListOfCustomers": "获取客户列表、根据ID检索特定客户信息、根据条件过滤客户。", "apigw.components.createApiDialog.constants.RegisterNewCustomersAndUpdate": "注册新客户、更新客户信息，包括联系方式和购买历史。", "apigw.components.createApiDialog.constants.DeleteSpecificCustomerInformation": "删除特定客户信息。", "apigw.components.createApiDialog.constants.TheApiMustHaveThe": "API需具备记录交易历史和用户操作日志的功能，以便进行安全审计和销售分析。建议对敏感操作进行权限验证。", "apigw.components.createApiDialog.constants.TheApiUsedToManage.3": "用于管理海鲜销售市场的API。", "apigw.components.createApiDialog.constants.SeafoodProducts": "海鲜产品", "apigw.components.createApiDialog.constants.SeafoodProductObjectsAreUsed": "海鲜产品对象用于存储产品的基本信息，如名称、类型、价格、库存量、来源等。", "apigw.components.createApiDialog.constants.SearchTheListOfSeafood": "检索海鲜产品列表、根据ID检索特定海鲜产品，以及根据类型或价格范围过滤产品信息。", "apigw.components.createApiDialog.constants.AddNewSeafoodProductsUpdate": "添加新海鲜产品、更新产品信息，以及批量导入海鲜产品数据。", "apigw.components.createApiDialog.constants.DeleteASpecificSeafoodProduct": "删除特定海鲜产品，并可选择删除与该产品相关的所有数据。", "apigw.components.createApiDialog.constants.TheOrderObjectRecordsThe": "订单对象用于记录客户的购买信息，包括客户ID、产品ID、数量、总价、订单状态等。", "apigw.components.createApiDialog.constants.ObtainAListOfAll.2": "获取所有订单列表、根据ID检索特定订单、以及根据用户ID或状态过滤订单信息。", "apigw.components.createApiDialog.constants.CreateNewOrdersUpdateOrder": "创建新订单、更新订单状态，以及处理订单支付。", "apigw.components.createApiDialog.constants.ModifyOrderInformationIncludingModifying": "修改订单信息，包括修改产品数量、更新订单状态等。", "apigw.components.createApiDialog.constants.CancelTheCreatedOrderAnd": "取消已创建的订单，并可选删除相关支付记录。", "apigw.components.createApiDialog.constants.UserObjectsAreUsedTo": "用户对象用于管理客户的基本信息，如姓名、电话、地址、注册时间等。", "apigw.components.createApiDialog.constants.RetrieveAListOfUsers": "检索用户列表、根据ID检索特定用户，以及根据注册时间或地区过滤用户信息。", "apigw.components.createApiDialog.constants.RegisterNewUsersLogOn": "注册新用户、用户登录，以及更新用户个人资料。", "apigw.components.createApiDialog.constants.DeleteAUserAccountAnd.1": "删除用户账户，并可选择删除与用户相关的订单历史记录。", "apigw.components.createApiDialog.constants.PaymentMethodManagementAndOrder": "需具备支付方式管理和订单处理状态跟踪的功能，以提供订单查询和支付审计的服务。", "apigw.components.createApiDialog.constants.ApisForManagingColdChain": "用于管理冷链运输系统的API，包括货物追踪、温度监控等。", "apigw.components.createApiDialog.constants.Goods": "货物", "apigw.components.createApiDialog.constants.TheCargoObjectIsUsed": "货物对象用于存储运输中的货物信息，如名称、数量、温度要求、状态等。", "apigw.components.createApiDialog.constants.RetrieveAListOfGoods": "检索货物列表、根据ID检索特定货物，以及根据状态过滤货物信息。", "apigw.components.createApiDialog.constants.CreateNewItemsIncludingThe": "创建新货物项，包括货物入库和运输任务的创建。", "apigw.components.createApiDialog.constants.UpdateCargoInformationIncludingModification": "更新货物信息，包括修改状态、数量及温度要求等。", "apigw.components.createApiDialog.constants.DeleteGoodsRecordsThatAre": "删除不再需要的货物记录，并可选择进行数据清理。", "apigw.components.createApiDialog.constants.TransportVehicle": "运输车辆", "apigw.components.createApiDialog.constants.TheTransportVehicleObjectContains": "运输车辆对象包含车辆的信息，如车辆编号、温度监控、当前位置等。", "apigw.components.createApiDialog.constants.ObtainAListOfVehicles": "获取车辆列表、根据车辆编号检索特定车辆信息，以及获取车辆的当前位置。", "apigw.components.createApiDialog.constants.RegisterNewVehiclesUpdateVehicle": "注册新车辆、更新车辆信息及配置车辆的温度监控系统。", "apigw.components.createApiDialog.constants.UpdateTheStatusOfTransportation": "更新运输车辆的状态，例如开始运输、完成运输等。", "apigw.components.createApiDialog.constants.DeleteRetiredTransportVehicleRecords": "删除已退役的运输车辆记录。", "apigw.components.createApiDialog.constants.TransportationTask": "运输任务", "apigw.components.createApiDialog.constants.TheTransportationTaskObjectIs": "运输任务对象用于存储运输的具体信息，如起止地点、运输时间、货物列表等。", "apigw.components.createApiDialog.constants.ObtainTheListOfTransportation": "获取运输任务列表、根据ID检索特定任务，以及获取任务的进展状态。", "apigw.components.createApiDialog.constants.CreateANewTransportationTask": "创建新的运输任务，关联货物和车辆进行调度。", "apigw.components.createApiDialog.constants.UpdateTheInformationOfTransportation": "更新运输任务的信息，包括修改运输路线、时间安排等。", "apigw.components.createApiDialog.constants.CancelTheTransportationTaskAnd": "取消运输任务，并移除关联的货物和车辆记录。", "apigw.components.createApiDialog.constants.TemperatureMonitoring": "温度监控", "apigw.components.createApiDialog.constants.TheTemperatureMonitoringObjectIs": "温度监控对象用于存储实时温度数据，确保货物保持在适宜的温度范围内。", "apigw.components.createApiDialog.constants.ObtainCurrentTemperatureMonitoringData": "获取当前温度监控数据，历史温度记录及警报信息。", "apigw.components.createApiDialog.constants.UploadRealTimeTemperatureData": "上传实时温度数据，配置温度监控的阈值。", "apigw.components.createApiDialog.constants.ClearExpiredTemperatureMonitoringRecords": "清除过期的温度监控记录。", "apigw.components.createApiDialog.constants.TheApiMustHaveA": "API需具备实时数据推送和警报系统，确保对异常情况的及时响应，并需记录所有操作日志以便进行安全审计和系统性能评估。", "apigw.components.createApiDialog.constants.TheApiUsedToManage.4": "用于管理设备折旧的API。", "apigw.components.createApiDialog.constants.Equipment": "设备", "apigw.components.createApiDialog.constants.TheDeviceObjectIsUsed": "设备对象用于存储设备的基本信息，如名称、型号、购买日期、原值等。", "apigw.components.createApiDialog.constants.RetrieveTheListOfDevices": "检索设备列表、根据ID检索特定设备，以及根据条件过滤设备信息。", "apigw.components.createApiDialog.constants.CreateANewDeviceRecord": "创建新设备记录，包括设备的基本信息与折旧规则。", "apigw.components.createApiDialog.constants.UpdateDeviceInformationIncludingModifying": "更新设备信息，包括修改设备属性、更新折旧策略等。", "apigw.components.createApiDialog.constants.DeleteADeviceRecordAnd": "删除设备记录，并可选择删除与之相关的折旧历史数据。", "apigw.components.createApiDialog.constants.DepreciationRecord": "折旧记录", "apigw.components.createApiDialog.constants.TheDepreciationRecordObjectIs": "折旧记录对象用于记录每个设备的折旧计算方式和当前折旧情况。", "apigw.components.createApiDialog.constants.ObtainTheDepreciationHistoryCurrent": "获取设备的折旧历史记录、当前折旧值以及相关统计信息。", "apigw.components.createApiDialog.constants.ManuallyCreateADepreciationRecord": "手动创建折旧记录，记录特定时间点的设备折旧情况。", "apigw.components.createApiDialog.constants.DeleteDepreciationRecordsOfSpecific": "删除特定设备的折旧记录，清理历史数据。", "apigw.components.createApiDialog.constants.DepreciationStrategy": "折旧策略", "apigw.components.createApiDialog.constants.DepreciationPolicyObjectsAreUsed": "折旧策略对象用于定义各种折旧计算方法，如直线法、双倍余额递减法等。", "apigw.components.createApiDialog.constants.ObtainAListOfAll.3": "获取所有可用折旧策略的列表及其详细信息。", "apigw.components.createApiDialog.constants.CreateANewDepreciationPolicy": "创建新的折旧策略，定义折旧计算规则。", "apigw.components.createApiDialog.constants.UpdateExistingDepreciationPoliciesIncluding": "更新现有折旧策略，包括调整计算参数或描述信息。", "apigw.components.createApiDialog.constants.DeleteASpecificDepreciationPolicy": "删除特定的折旧策略，不再使用。", "apigw.components.createApiDialog.constants.ItIsNecessaryToSupport": "需支持将设备折旧和财务系统集成，方便自动对接和数据报表生成，确保数据的一致性和准确性。", "apigw.components.createApiDialog.constants.ApiForStatisticsAndManagement": "用于统计和管理股票收益的API。", "apigw.components.createApiDialog.constants.Stock": "股票", "apigw.components.createApiDialog.constants.TheStockObjectIsUsed": "股票对象用于存储股票的基本信息，如股票代码、名称、当前价格、持有数量等。", "apigw.components.createApiDialog.constants.SearchTheListOfStocks": "检索股票列表，获取特定股票信息，根据条件过滤股票。", "apigw.components.createApiDialog.constants.AddNewStockInformationTo": "添加新股票信息，更新持有股票的数量及成本。", "apigw.components.createApiDialog.constants.DeleteRecordedStockInformation": "删除已记录的股票信息。", "apigw.components.createApiDialog.constants.RevenueStatistics": "收益统计", "apigw.components.createApiDialog.constants.ItIsUsedToCalculate": "用于计算和存储股票的收益数据，如买入价格、当前价格、收益率等。", "apigw.components.createApiDialog.constants.ObtainTheReturnStatisticsOf": "获取特定股票的收益统计信息，计算用户的总收益和收益率。", "apigw.components.createApiDialog.constants.AddRevenueRecordsAndUpdate": "新增收益记录，更新收益统计，包括买入、卖出和持有状态等。", "apigw.components.createApiDialog.constants.DeleteASpecificRevenueStatistics": "删除特定收益统计记录。", "apigw.components.createApiDialog.constants.UserConfiguration": "用户配置", "apigw.components.createApiDialog.constants.StoresUserPersonalizedSettingsSuch": "存储用户的个性化设置信息，如投资偏好、警报设置等。", "apigw.components.createApiDialog.constants.ObtainTheUserSInvestment": "获取用户的投资属性设置和历史记录。", "apigw.components.createApiDialog.constants.CreateOrUpdateYourInvestment": "创建或更新用户的投资偏好设置。", "apigw.components.createApiDialog.constants.DeletesAConfigurationSettingFor": "删除用户的某项配置设置。", "apigw.components.createApiDialog.constants.TheUserSOperationHistory": "需跟踪用户的操作历史，以便进行数据分析和为用户提供个性化建议。API应具备数据加密及权限验证功能，确保用户数据安全。", "apigw.components.createApiDialog.constants.ApiForManagingRegularFeeding": "用于管理宠物的定期喂食计划的API。", "apigw.components.createApiDialog.constants.FeedingPlan": "喂食计划", "apigw.components.createApiDialog.constants.TheFeedingPlanObjectIs": "喂食计划对象用于存储每只宠物的喂食时间、喂食量和喂食类型等信息。", "apigw.components.createApiDialog.constants.RetrieveAllFeedingPlansRetrieve": "检索所有喂食计划、根据ID检索特定喂食计划以及根据宠物ID过滤喂食计划。", "apigw.components.createApiDialog.constants.CreateANewFeedingPlan": "创建新喂食计划，包括设置具体的喂食时间、量和类型。", "apigw.components.createApiDialog.constants.UpdateTheExistingFeedingPlan": "更新现有的喂食计划，包括修改喂食时间、量和类型。", "apigw.components.createApiDialog.constants.YouCanDeleteSpecificFeeding": "删除特定的喂食计划，支持按宠物ID批量删除相关的喂食计划。", "apigw.components.createApiDialog.constants.Pet": "宠物", "apigw.components.createApiDialog.constants.PetObjectsAreUsedTo": "宠物对象用于存储宠物的基本信息，如姓名、品种、年龄、体重等。", "apigw.components.createApiDialog.constants.RetrieveAllPetInformationRetrieve": "检索所有宠物信息、根据ID检索特定宠物以及根据条件过滤宠物信息。", "apigw.components.createApiDialog.constants.AddTheInformationOfThe": "添加新宠物的信息，包括上传宠物的基本信息。", "apigw.components.createApiDialog.constants.UpdateTheInformationOfExisting": "更新现有宠物的信息，如更新宠物的健康记录、体重等。", "apigw.components.createApiDialog.constants.DeletePetInformationAndChoose": "删除宠物的信息，并可选择删除与宠物相关的喂食计划。", "apigw.components.createApiDialog.constants.YouMustHaveTheFunction": "需具备提醒用户喂食时间的功能，并生成喂食记录以便进行营养分析和宠物健康跟踪。", "apigw.components.createApiDialog.constants.ApiForManagingCommercialInsurance": "用于管理商业保险计划的API。", "apigw.components.createApiDialog.constants.InsurancePlan": "保险计划", "apigw.components.createApiDialog.constants.TheInsurancePlanObjectIs": "保险计划对象用于存储保险的基本信息，如计划名称、保险类型、保额、保费及适用范围等。", "apigw.components.createApiDialog.constants.RetrieveTheListOfInsurance": "检索保险计划列表、根据ID检索特定保险计划，以及根据条件筛选保险计划信息。", "apigw.components.createApiDialog.constants.CreateANewInsurancePlan": "创建新保险计划，提交保险计划的详细信息。", "apigw.components.createApiDialog.constants.UpdateTheInformationOfAn": "更新现有保险计划的信息，包括修改保额、保费或适用范围。", "apigw.components.createApiDialog.constants.DeleteInsurancePlansThatAre": "删除不再使用的保险计划，并可选择清理相关的数据记录。", "apigw.components.createApiDialog.constants.Policyholder": "投保人", "apigw.components.createApiDialog.constants.TheApplicantObjectIsUsed": "投保人对象用于存储投保人的个人信息，如姓名、联系方式、身份证号码及投保历史等。", "apigw.components.createApiDialog.constants.ObtainAListOfPolicyholders": "获取投保人列表、根据ID检索特定投保人，以及根据条件筛选投保人信息。", "apigw.components.createApiDialog.constants.CreateRecordsForNewPolicyholders": "为新投保人创建记录，提交个人信息及投保相关内容。", "apigw.components.createApiDialog.constants.UpdateTheInformationOfThe.1": "更新投保人的信息，包括修改联系方式或添加投保历史。", "apigw.components.createApiDialog.constants.RemovePolicyholderRecordsThatAre": "移除不再使用的投保人记录，并可以选择删除相关的投保数据。", "apigw.components.createApiDialog.constants.ClaimApplication": "索赔申请", "apigw.components.createApiDialog.constants.TheClaimObjectIsUsed": "索赔申请对象用于记录投保人提出的索赔请求，包括索赔金额、索赔理由和申请状态等。", "apigw.components.createApiDialog.constants.SearchTheClaimListSearch": "检索索赔申请列表、根据ID检索特定索赔申请，以及根据条件筛选索赔请求。", "apigw.components.createApiDialog.constants.SubmitANewClaimApplication": "提交新的索赔申请，上传相关文件及支持材料。", "apigw.components.createApiDialog.constants.UpdateTheClaimStatusOr": "更新索赔申请状态或补充材料信息。", "apigw.components.createApiDialog.constants.RevokeUnprocessedClaimsOrDelete": "撤销未处理的索赔申请，或删除已取消的申请记录。", "apigw.components.createApiDialog.constants.MultiLanguageSupportIsRequired": "需具备多语言支持功能，以适应不同地区用户的需求，并需实现保险计划与投保人之间的关联查询功能。", "apigw.components.createApiDialog.constants.ApiForManagingHealthCheck": "用于管理健康体检信息和流程的API。", "apigw.components.createApiDialog.constants.PhysicalExaminationUser": "体检用户", "apigw.components.createApiDialog.constants.ThePhysicalExaminationUserObject": "体检用户对象用于存储用户的基本信息，如姓名、身份证号、联系方式、健康状态等。", "apigw.components.createApiDialog.constants.RetrieveTheListOfPhysical": "检索体检用户列表、根据ID检索特定用户的体检信息、根据条件过滤用户信息。", "apigw.components.createApiDialog.constants.CreateANewMedicalExamination": "创建新体检用户、用户注册以参与体检，提交体检申请。", "apigw.components.createApiDialog.constants.UpdateTheInformationOfThe.2": "更新体检用户的信息，包括联系方式、健康状态等。", "apigw.components.createApiDialog.constants.DeleteThePhysicalExaminationUser": "删除体检用户记录，并可选择删除用户的相关体检数据。", "apigw.components.createApiDialog.constants.PhysicalExaminationItems": "体检项目", "apigw.components.createApiDialog.constants.TheObjectOfThePhysical": "体检项目对象包含体检的具体内容，如检查项目、检查标准、费用等。", "apigw.components.createApiDialog.constants.ObtainTheListOfPhysical": "获取体检项目列表、根据ID获取特定体检项目的信息。", "apigw.components.createApiDialog.constants.CreateANewPhysicalExamination": "创建新体检项目、更新体检项目的信息。", "apigw.components.createApiDialog.constants.DeleteThePhysicalExaminationItem": "删除体检项目记录。", "apigw.components.createApiDialog.constants.PhysicalExaminationResults": "体检结果", "apigw.components.createApiDialog.constants.ThePhysicalExaminationResultObject": "体检结果对象用于存储每个用户的体检数据和结果，包括测量指标、体检报告等。", "apigw.components.createApiDialog.constants.ObtainTheListOfPhysical.1": "获取体检结果列表、根据用户ID获取特定用户的体检结果。", "apigw.components.createApiDialog.constants.EnterTheUserSPhysical": "录入用户的体检结果、生成体检报告。", "apigw.components.createApiDialog.constants.DeleteThePhysicalExaminationResults": "删除特定用户的体检结果记录。", "apigw.components.createApiDialog.constants.MedicalExaminationAppointment": "体检预约", "apigw.components.createApiDialog.constants.ThePhysicalExaminationAppointmentObject": "体检预约对象用于管理用户的体检预约信息，如预约时间、预约状态等。", "apigw.components.createApiDialog.constants.ObtainTheListOfPhysical.2": "获取体检预约列表、根据用户ID获取特定用户的预约信息。", "apigw.components.createApiDialog.constants.CreateANewMedicalExamination.1": "创建新的体检预约、更新预约信息，如重约或取消预约。", "apigw.components.createApiDialog.constants.CancelThePhysicalExaminationAppointment": "取消特定用户的体检预约。", "apigw.components.createApiDialog.constants.TheSystemMustHaveThe": "系统需具备用户健康数据的保密性和安全性，加入数据加密及访问权限控制，以确保用户隐私。", "apigw.components.createApiDialog.constants.ApiForManagingVodServices": "用于管理电影点播服务的API。", "apigw.components.createApiDialog.constants.Film": "电影", "apigw.components.createApiDialog.constants.TheMovieObjectIsUsed": "电影对象用于存储电影的基本信息，如标题、导演、演员、类型、发布日期等。", "apigw.components.createApiDialog.constants.RetrieveMovieListsRetrieveSpecific": "检索电影列表、根据ID检索特定电影、根据条件过滤电影信息（如类型、导演等）。", "apigw.components.createApiDialog.constants.AddNewMovieInformationIncluding": "添加新电影信息，包括上传电影海报和预告片，支持电影的详情描述。", "apigw.components.createApiDialog.constants.UpdateInformationAboutExistingMovies": "更新现有电影的信息，如修改标题、演员、类型及其他相关信息。", "apigw.components.createApiDialog.constants.DeleteASpecificMovieAnd": "删除特定电影，及可选择删除与该电影相关的所有数据。", "apigw.components.createApiDialog.constants.UserObjectsAreUsedTo.1": "用户对象用于存储用户的基本信息，如用户名、邮箱、订阅状态等。", "apigw.components.createApiDialog.constants.ObtainUserAccountInformationUser": "获取用户账户信息、用户观看历史和订阅计划。", "apigw.components.createApiDialog.constants.UserRegistrationUserLoginAnd.1": "用户注册、用户登录，并可选择创建个人观看列表。", "apigw.components.createApiDialog.constants.UpdateUserInformationSuchAs.1": "更新用户信息，如修改密码、更新邮箱和订阅计划。", "apigw.components.createApiDialog.constants.DeleteAUserAccountAnd.2": "删除用户账户，及可选择删除与账户相关的观看记录。", "apigw.components.createApiDialog.constants.Subscription": "订阅", "apigw.components.createApiDialog.constants.TheSubscriptionObjectIsUsed": "订阅对象用于管理用户的订阅计划，包括订阅类型、续订、取消等信息。", "apigw.components.createApiDialog.constants.ObtainsTheSubscriptionStatusAnd": "获取当前用户的订阅状态和可用的订阅计划列表。", "apigw.components.createApiDialog.constants.TheUserSelectsASubscription": "用户选择订阅计划并进行支付操作，支持不同的付费模式。", "apigw.components.createApiDialog.constants.CancelUserSubscriptionsAndProcess": "取消用户的订阅，及处理相关的续订操作。", "apigw.components.createApiDialog.constants.YouMustHaveTheFunction.1": "需具备统计用户观看行为及偏好的功能，以提供个性化推荐服务。同时，需要确保用户数据的安全存储和隐私保护。", "apigw.components.createApiDialog.constants.TheApiUsedToManage.5": "用于管理云游戏服务的API。", "apigw.components.createApiDialog.constants.Game": "游戏", "apigw.components.createApiDialog.constants.GameObjectsAreUsedTo": "游戏对象用于存储游戏的基本信息，如游戏ID、名称、类型、开发者、封面图等。", "apigw.components.createApiDialog.constants.SearchTheGameListSearch": "检索游戏列表、根据ID检索特定游戏、根据条件过滤游戏信息。", "apigw.components.createApiDialog.constants.CreateANewGameAnd": "创建新游戏，上传游戏信息，包括名称、封面图等。", "apigw.components.createApiDialog.constants.UpdateGameInformationIncludingModifying": "更新游戏信息，包括修改游戏描述、更新游戏图片等。", "apigw.components.createApiDialog.constants.ToDeleteAGameRecord": "删除游戏记录，可选择是否删除相关数据。", "apigw.components.createApiDialog.constants.UserAccount": "用户账户", "apigw.components.createApiDialog.constants.UserAccountObjectsAreUsed": "用户账户对象用于管理用户在云游戏服务中的账户信息，包括用户ID、昵称、注册时间等。", "apigw.components.createApiDialog.constants.ObtainUserAccountInformationAnd": "获取用户账户信息和账户列表，支持根据条件过滤。", "apigw.components.createApiDialog.constants.CreateNewUserAccountsRegister": "创建新用户账户，用户注册，以及绑定社交平台账户。", "apigw.components.createApiDialog.constants.UpdateUserAccountInformationIncluding": "更新用户账户信息，包括修改昵称、邮箱等。", "apigw.components.createApiDialog.constants.DeleteTheUserAccountAnd": "删除用户账户，并可选择删除用户的相关游戏记录。", "apigw.components.createApiDialog.constants.GameRecord": "游戏记录", "apigw.components.createApiDialog.constants.TheGameRecordObjectIs": "游戏记录对象用于存储用户在云游戏中玩的记录，包括用户ID、游戏ID、游玩时间和成绩等。", "apigw.components.createApiDialog.constants.ObtainTheUserSGame": "获取用户的游戏记录，支持按照时间、游戏类型等条件过滤。", "apigw.components.createApiDialog.constants.RecordTheUserSPlay": "记录用户的游玩数据，包括开始、结束时间及成绩上传。", "apigw.components.createApiDialog.constants.YouCanDeleteGameRecords": "删除特定用户的游戏记录，支持批量删除。", "apigw.components.createApiDialog.constants.ThePaymentObjectIsUsed.1": "支付对象用于管理用户在云游戏中的支付信息，包括支付ID、金额、支付时间等。", "apigw.components.createApiDialog.constants.ObtainTheUserSPayment.1": "获取用户的支付记录，支持根据时间、金额等条件过滤。", "apigw.components.createApiDialog.constants.InitiateANewPaymentRequest": "发起新的支付请求，记录支付信息，支持不同支付方式。", "apigw.components.createApiDialog.constants.CancelTheUserSPayment": "取消用户的支付请求，删除相关支付记录。", "apigw.components.createApiDialog.constants.Feedback": "反馈", "apigw.components.createApiDialog.constants.TheFeedbackObjectIsUsed": "反馈对象用于收集用户对云游戏服务的反馈，包括用户ID、反馈内容、反馈时间等。", "apigw.components.createApiDialog.constants.ObtainUserFeedbackRecordsFor": "获取用户反馈记录，供管理员查看和分析。", "apigw.components.createApiDialog.constants.UsersSubmitFeedbackIncludingProblem": "用户提交反馈，包括问题描述和建议等。", "apigw.components.createApiDialog.constants.DeleteFeedbackRecordsForSpecific": "删除特定用户的反馈记录。", "apigw.components.createApiDialog.constants.ApiDesignNeedsToSupport": "API设计需支持多语言和跨平台访问，同时需要具备监控系统的功能，以跟踪游戏性能和用户行为，从而提供优化建议。", "apigw.components.createApiDialog.constants.ApisForManagingChildrenS": "用于管理儿童游园会的API，包括活动、报名、奖项等信息。", "apigw.components.createApiDialog.constants.Activity": "活动", "apigw.components.createApiDialog.constants.TheActivityObjectContainsBasic": "活动对象包含活动的基本信息，如名称、时间、地点、参与限制等。", "apigw.components.createApiDialog.constants.RetrieveAllActivitiesRetrieveSpecific": "检索所有活动、根据ID检索特定活动，以及按条件过滤活动信息。", "apigw.components.createApiDialog.constants.CreateANewActivityIncluding": "创建新活动，包括活动的详细信息如名称、时间、地点等。", "apigw.components.createApiDialog.constants.UpdateActivityInformationIncludingModification": "更新活动信息，包括修改时间、地点、参与规则等。", "apigw.components.createApiDialog.constants.DeleteSpecifiedActivitiesAndSelect": "删除指定活动，及选择删除相关信息及报名记录。", "apigw.components.createApiDialog.constants.SignUp": "报名", "apigw.components.createApiDialog.constants.TheRegistrationObjectIsUsed": "报名对象用于记录儿童及其监护人报名参与活动的信息，包括儿童姓名、年龄、监护人联系方式等。", "apigw.components.createApiDialog.constants.ObtainAllRegistrationInformationAnd": "获取所有报名信息、根据活动ID检索特定报名信息等。", "apigw.components.createApiDialog.constants.ChildrenSignUpForActivities": "儿童报名参加活动，记录相关信息。", "apigw.components.createApiDialog.constants.CancelTheRegistrationAndDelete": "撤销报名，删除相关儿童的报名信息。", "apigw.components.createApiDialog.constants.Awards": "奖项", "apigw.components.createApiDialog.constants.TheAwardObjectRecordsThe": "奖项对象记录游园会中各活动的奖品设置，包括奖品名称、数量、规则等。", "apigw.components.createApiDialog.constants.ObtainTheAwardSettingsOf": "获取各活动的奖项设置，按活动ID过滤奖项信息。", "apigw.components.createApiDialog.constants.SetUpNewAwardsFor": "为特定活动设置新的奖项，包括奖品名称、数量等详细信息。", "apigw.components.createApiDialog.constants.UpdateTheAwardInformationOf": "更新活动的奖项信息，修改奖品数量、规则等。", "apigw.components.createApiDialog.constants.DeleteTheAwardSettingsFor": "删除特定活动的奖项设置。", "apigw.components.createApiDialog.constants.YouMustHaveTheFunction.2": "需具备记录用户操作日志和活动参与情况的功能，以便进行安全审计和数据分析。", "apigw.components.createApiDialog.constants.TheApiUsedToManage.6": "用于管理客服服务记录的API。", "apigw.components.createApiDialog.constants.CustomerServiceRecords": "客服记录", "apigw.components.createApiDialog.constants.CustomerServiceRecordsAreUsed": "客服记录对象用于存储服务请求、客户反馈、客服响应和解决方案等信息，包括记录ID、客户ID、客服ID、创建时间、更新时间、服务状态等属性。", "apigw.components.createApiDialog.constants.RetrieveCustomerServiceRecordsRetrieve": "检索客服记录列表、根据ID检索特定客服记录、按照客户ID或时间范围过滤记录。", "apigw.components.createApiDialog.constants.CreateANewCustomerService": "创建新的客服记录，包括记录服务请求和客户反馈等信息。", "apigw.components.createApiDialog.constants.UpdateExistingCustomerServiceRecords": "更新现有的客服记录，包括修改服务状态、添加解决方案或备注等。", "apigw.components.createApiDialog.constants.ToDeleteACustomerService": "删除客服记录，可选择删除某一特定记录或批量删除满足条件的记录。", "apigw.components.createApiDialog.constants.CustomerInformation": "客户信息", "apigw.components.createApiDialog.constants.StoreBasicCustomerInformationSuch": "存储客户的基本信息，如客户ID、姓名、联系方式、反馈历史等，用于关联客服记录。", "apigw.components.createApiDialog.constants.ObtainCustomerInformationAndFeedback": "获取客户信息和反馈历史记录，支持根据客户ID检索。", "apigw.components.createApiDialog.constants.CreateNewCustomerInformationRecord": "创建新的客户信息，记录客户的基本资料以及反馈历史。", "apigw.components.createApiDialog.constants.UpdateCustomerInformationSuchAs": "更新客户信息，例如修改联系方式或添加更多的反馈记录。", "apigw.components.createApiDialog.constants.DeleteCustomerInformationOrRelevant": "删除客户信息或相关反馈记录，需确保客户在系统中的完整性。", "apigw.components.createApiDialog.constants.CustomerServiceStaff": "客服人员", "apigw.components.createApiDialog.constants.StoreBasicInformationAboutCustomer": "存储客服人员的基本信息，如人员ID、姓名、联系方式、所属部门及其服务历史记录等。", "apigw.components.createApiDialog.constants.ObtainAListOfCustomer": "获取客服人员列表、根据ID检索特定客服人员的信息。", "apigw.components.createApiDialog.constants.CreateNewCustomerServicePersonnel": "创建新的客服人员信息，包括其基本资料和服务角色。", "apigw.components.createApiDialog.constants.UpdateCustomerServicePersonnelInformation": "更新客服人员信息，例如修改联系方式或调整部门。", "apigw.components.createApiDialog.constants.ToDeleteCustomerServicePersonnel": "删除客服人员记录，需注意是否影响相关服务记录。", "apigw.components.createApiDialog.constants.ItIsNecessaryToDesign": "需设计记录客服与客户间的所有交互，并提供搜索和过滤功能，以便进行数据分析和提升服务质量。同时，每条记录应具备审核机制，确保数据的真实性和安全性。", "apigw.components.createApiDialog.constants.TheApiUsedToManage.7": "用于管理工程维修记录的API。", "apigw.components.createApiDialog.constants.MaintenanceRecord": "维修记录", "apigw.components.createApiDialog.constants.TheMaintenanceRecordObjectIs": "维修记录对象用于存储维修的基本信息，如维修日期、项目名称、维修描述、费用等。", "apigw.components.createApiDialog.constants.RetrieveTheListOfMaintenance": "检索维修记录列表、根据ID检索特定维修记录以及根据条件过滤维修记录信息。", "apigw.components.createApiDialog.constants.CreateANewRepairRecord": "创建新维修记录，包括添加维修项目的详细信息和费用。", "apigw.components.createApiDialog.constants.UpdateExistingMaintenanceRecordsIncluding": "更新已存在的维修记录，包括修改维修描述、费用和其他相关信息。", "apigw.components.createApiDialog.constants.DeleteSpecificServiceRecordsAnd": "删除特定维修记录，并可选择删除相关的附件或日志。", "apigw.components.createApiDialog.constants.MaintenancePersonnel": "维修人员", "apigw.components.createApiDialog.constants.TheMaintenancePersonnelObjectIs": "维修人员对象用于记录参与维修的工作人员信息，包括姓名、联系方式、角色等。", "apigw.components.createApiDialog.constants.ObtainTheListOfMaintenance": "获取维修人员列表、根据ID检索特定维修人员信息，及按照条件过滤维修人员信息。", "apigw.components.createApiDialog.constants.AddNewMaintenancePersonnelInformation": "添加新的维修人员信息，创建工作人员的档案。", "apigw.components.createApiDialog.constants.UpdateMaintenancePersonnelInformationIncluding": "更新维修人员的信息，包括修改联系方式和角色。", "apigw.components.createApiDialog.constants.DeleteTheInformationOfA": "删除特定维修人员的信息。", "apigw.components.createApiDialog.constants.MaintenanceClassification": "维修分类", "apigw.components.createApiDialog.constants.MaintenanceClassificationObjectsAreUsed": "维修分类对象用于对维修项目进行分类，如电气维修、管道维修等。", "apigw.components.createApiDialog.constants.ObtainAListOfMaintenance": "获取维修分类列表、根据ID检索特定维修分类。", "apigw.components.createApiDialog.constants.AddANewMaintenanceClassification": "添加新的维修分类，便于对维修项目进行归类管理。", "apigw.components.createApiDialog.constants.ToDeleteASpecificMaintenance": "删除特定维修分类，需保证没有相关维修记录引用。", "apigw.components.createApiDialog.constants.TheApiMustSupportFile": "API需支持文件上传以便记录维修相关的照片和文档，同时应具备记录维修日志的功能，以供日后审计与分析。", "apigw.components.createApiDialog.OpenapiContentIsInvalidPlease": "OpenAPI内容不合法，请重新修改", "apigw.components.createApiDialog.GenerateASwaggerSampleBefore": "请生成swagger示例后再创建", "apigw.components.createApiDialog.SwaggerExampleIsBeingGenerated": "swagger示例正在生成中，请稍等", "apigw.components.createApiDialog.YamlIsInvalid": "YAML不合法", "apigw.components.createApiDialog.CurrentApiVersionManagementConflicts": "当前API版本管理冲突", "apigw.components.createApiDialog.AfterTestingItAlreadyExists": "经检测，已存在", "apigw.components.createApiDialog.TheCurrentApiAndThe": "。当前API与已存在API版本管理开启状态不同，若为同一API，版本管理开启状态需保持一致，否则需调整API名称。", "apigw.components.createApiDialog.Cancel": "取消", "apigw.components.createApiDialog.FileUploadFailed": "文件上传失败", "apigw.components.createApiDialog.FileUploadedSuccessfully": "文件上传成功", "apigw.components.createApiDialog.TheApiAlreadyHasThe": "该API已存在相同版本号, 请调整API名称或版本号", "apigw.components.createApiDialog.VersionManagementAlreadyExistsFor": "该API已存在版本管理,请开启下面的版本管理", "apigw.components.createApiDialog.TheApiNameAlreadyExists": "该API名称已存在,请调整", "apigw.components.createApiDialog.CreateBasedOnAiA": "基于AI大模型创建", "apigw.components.createApiDialog.PreCheck": "预检中", "apigw.components.createApiDialog.PrecheckAndCreate": "预检并创建", "apigw.components.createApiDialog.Confirm": "确认", "apigw.components.createApiDialog.EnterAnApiNameIf": "请输入API名称，不填写则默认从API定义文件中提取", "apigw.components.createApiDialog.TheApiNameCannotBe": "API名称不能为空", "apigw.components.createApiDialog.TheCharacterIsNotAllowed": "不允许有@字符", "apigw.components.createApiDialog.CharactersAreNotAllowed": "不允许有 / 字符", "apigw.components.createApiDialog.TheApiNameCannotExceed": "API名称不能超过64个字符", "apigw.components.createApiDialog.TheFileSizeIsLimited": "(限制文件大小在30M内)", "apigw.components.createApiDialog.TheContentExceedsMAnd": "内容超过2M，需要以文件方式上传", "apigw.components.createApiDialog.InvalidJson": "JSON不合法", "apigw.components.createApiDialog.TheHeaderCannotStartWith": "Header不允许以x-fc开头，该Header与调用FC函数的系统Header冲突, 请您调整", "apigw.api-manage.createApi.CreateApiTypes.Create": "创建", "apigw.api-manage.createApi.CreateApiTypes.AiALargeModelCreation": "AI大模型创建", "apigw.api-manage.createApi.CreateApiTypes.Import": "导入", "apigw.api-manage.createApi.CreateApiTypes.ImportIngress": "导入Ingress", "apigw.api-manage.createApi.CreateApiTypes.AnHttpBasedInterfaceIs": "RESTful风格的HTTP接口，所有接口遵循统一的OpenAPI规范，适用于前后端及系统间协作、API精细化管理等场景。如果您需要为协作方提供标准访问接口，包括文档、SDK，建议选择当前API类型", "apigw.api-manage.createApi.CreateApiTypes.HttpBasedInterfacesAreRouting": "以路由为核心的HTTP接口，适用于对接口没有统一规范约束的场景，如果您需要流量转发（包括SSE）、K8s Ingress、微服务请求处理等，建议选择当前API类型。", "apigw.api-manage.createApi.CreateApiTypes.ItProvidesALongConnection": "WebSocket协议接口，适用于双向实时通讯，如AI、IoT、即时通讯等。相比HTTP API，WebSocket提供了实时数据传输能力，内置长连接相关默认配置。如果您需要应用间即时交互，建议选择当前API类型。", "apigw.api-manage.createApi.CreateApiTypes.YouCanSelectTheApi": "您可以在这里选择API类型并创建或导入。", "apigw.api-manage.createApi.CreateApiTypes.LearnAboutDifferentTypesOf": "了解不同类型的API", "apigw.api-manage.createApi.CreateApiTypes.NoInstanceIsCurrentlyAvailable": "当前暂无实例，为了更好的配置体验，建议您先", "apigw.api-manage.createApi.CreateApiTypes.CreateAnInstance": "创建实例", "apigw.api-manage.createApi.CreateApiTypesDialog.CreateAnApi": "创建API", "apigw.createApi.create-actions.CreateApiBasicForm.TheApiNameAlreadyExists": "该API名称已存在,请调整", "apigw.createApi.create-actions.CreateApiBasicForm.TheNameIsUniqueAnd": "名称唯一，支持英文、数字、下划线“_”，不超过64个字符", "apigw.createApi.create-actions.CreateApiBasicForm.EnterAnApiName": "请输入API名称", "apigw.createApi.create-actions.CreateApiBasicForm.TheApiNameCannotBe": "API名称不能为空", "apigw.createApi.create-actions.CreateApiBasicForm.TheCharacterIsNotAllowed": "不允许有@字符", "apigw.createApi.create-actions.CreateApiBasicForm.TheApiNameCannotExceed": "API名称不能超过64个字符", "apigw.createApi.create-actions.CreateIngressApiSidePanel.ImportIngress": "导入Ingress", "apigw.createApi.create-actions.CreateIngressApiSidePanel.EnvironmentGatewayVpc": "环境/网关/VPC", "apigw.createApi.create-actions.CreateIngressApiSidePanel.SelectAnEnvironment": "请选择环境", "apigw.createApi.create-actions.CreateIngressApiSidePanel.SourceCluster": "来源集群", "apigw.createApi.create-actions.CreateIngressApiSidePanel.SelectASource": "请选择来源", "apigw.createApi.create-actions.CreateIngressApiSidePanel.CustomConfiguration": "自定义配置", "apigw.createApi.create-actions.CreateIngressApiSidePanel.ByDefaultTheSystemListens": "系统会默认监听集群中全部命名空间下的所有Ingress资源。如需指定监听范围可自定义配置。", "apigw.createApi.create-actions.CreateIngressApiSidePanel.IfTheFollowingInformationIs": "如果配置了以下信息，请确保在K8s集群中这些资源不会被误删除，否则同步将会出错。", "apigw.createApi.create-actions.CreateIngressApiSidePanel.Namespace": "命名空间", "apigw.createApi.create-actions.CreateIngressApiSidePanel.SpecifyTheNamespaceToListen": "请指定需要监听的命名空间。如果留空，则表示监听所有命名空间下的Ingress资源。", "apigw.createApi.create-actions.CreateIngressApiSidePanel.SpecifyTheNamespaceToListen.1": "请指定需要监听的命名空间", "apigw.createApi.create-actions.CreateIngressApiSidePanel.SpecifyTheIngressclassToListen": "请指定需要监听的IngressClass。如果留空，则表示监听所有类型的Ingress资源。", "apigw.createApi.create-actions.CreateIngressApiSidePanel.SpecifyTheIngressclassToListen.1": "请指定需要监听的IngressClass", "apigw.createApi.create-actions.CreateIngressApiSidePanel.UpdateIngressStatus": "更新Ingress Status", "apigw.createApi.create-actions.CreateIngressApiSidePanel.WhenEnabledIngressOfThe": "开启后，被监听的Ingress中的Status Endpoint将会被替换成网关入口的访问地址。", "apigw.createApi.create-actions.CreateIngressApiSidePanel.Enable": "开启", "apigw.createApi.create-actions.CreateRouterApiSidePanel.CreateHttpApi": "创建HTTP API", "apigw.createApi.create-actions.CreateSourceDialog.CreateSource": "创建来源", "apigw.createApi.create-actions.CreateSourceDialog.CreateAServiceSource": "创建服务来源", "apigw.createApi.create-actions.CreateSourceDialog.EnvironmentGatewayVpc": "环境/网关/VPC", "apigw.createApi.create-actions.CreateSourceDialog.SourceType": "来源类型", "apigw.createApi.create-actions.CreateSourceDialog.ContainerService": "容器服务", "apigw.createApi.create-actions.CreateSourceDialog.Source": "来源", "apigw.createApi.create-actions.CreateSourceDialog.SelectASource": "请选择来源", "apigw.createApi.create-actions.CreateWebSocketApiSidePanel.CreateWebsocketApi": "创建WebSocket API", "apigw.api-manage.createApi.AnHttpBasedInterfaceIs": "基于HTTP协议的接口，以资源为导向，通过标准HTTP方法（如GET、POST等）对资源进行操作。适用于API First、跨团队协作、API精细化管控等场景。", "apigw.api-manage.createApi.HttpIngress": "HTTP（Ingress）", "apigw.api-manage.createApi.ThereIsNoManageableApi": "没有可管理的API，请先创建", "apigw.api-manage.headBtn.TheCurrentApiIsBeing": "当前API正在发布中，暂不支持发布", "apigw.api-manage.headBtn.TheApiIsCurrentlyOffline": "当前API正在下线中，暂不支持发布", "apigw.api-manage.headBtn.Import": "导入", "apigw.publish.components.backendServicesColumns.Operation": "操作", "apigw.publish.components.backendServicesColumns.ServiceWeight": "服务权重", "apigw.publish.components.backendServicesColumns.CreateAService": "创建服务", "apigw.publish.components.backendServicesColumns.CannotBeEmpty": "不能为空", "apigw.publish.components.backendServicesColumns.Agreement": "协议", "apigw.publish.components.backendServicesColumns.NamespaceName": "命名空间名称", "apigw.publish.components.backendServicesColumns.NamespaceAlias": "命名空间别名", "apigw.envAndBackendServices.backendServices.Item.NoServiceIsAvailablePlease": "暂无服务,请创建服务或刷新", "apigw.envAndBackendServices.backendServices.Item.CreateAService": "创建服务", "apigw.envAndBackendServices.backendServices.columns.ServiceWeight": "服务权重", "apigw.envAndBackendServices.backendServices.columns.Operation": "操作", "apigw.envAndBackendServices.backendServices.Add": "添加", "apigw.components.envAndBackendServices.envSelect.Publishing": "发布中", "apigw.components.envAndBackendServices.envSelect.PublishedSuccessfully": "发布成功", "apigw.components.envAndBackendServices.envSelect.PublishingFailed": "发布失败", "apigw.components.envAndBackendServices.envSelect.Published": "已发布", "apigw.components.envAndBackendServices.envSelect.Offline": "下线中", "apigw.components.envAndBackendServices.envSelect.OfflineFailed": "下线失败", "apigw.components.envAndBackendServices.envSelect.EnvironmentAliasName": "环境别名/名称", "apigw.components.envAndBackendServices.envSelect.InstanceNameId": "实例名称/ID", "apigw.components.envAndBackendServices.envSelect.VpcNameId": "VPC名称/ID", "apigw.components.envAndBackendServices.TheCurrentApiIsBeing": "当前API正在发布中，暂不支持发布", "apigw.components.envAndBackendServices.TheApiIsCurrentlyOffline": "当前API正在下线中，暂不支持发布", "apigw.components.envAndBackendServices.EnvironmentGatewayVpc": "环境/网关/VPC", "apigw.components.envAndBackendServices.InTheCurrentApiMock": "当前API中，需要至少一个接口中配置了Mock响应。", "apigw.components.envAndBackendServices.InGrayscaleScenariosOnlyThe": "灰度场景仅能实现从网关到后端服务第一跳的灰度能力，如需全链路灰度，请参考服务治理相关能力 。", "apigw.components.fcService.matchCondition.TheHeaderCannotStartWith": "Header不允许以x-fc开头，该Header与调用FC函数的系统Header冲突, 请您调整", "apigw.publish.components.matchCondition.TheHeaderCannotStartWith": "Header不允许以x-fc开头，该Header与调用FC函数的系统Header冲突, 请您调整", "apigw.components.securityGroup.Item.SelectASecurityGroup": "请选择安全组", "apigw.components.securityGroup.Item.PleaseEnter": "请输入", "apigw.components.securityGroup.SecurityGroupName": "安全组名称", "apigw.components.securityGroup.PortRange": "端口范围", "apigw.components.securityGroup.Remarks": "备注", "apigw.headBtn.publish.PublishRestApi": "发布REST API", "apigw.headBtn.publish.TheDescriptionCannotExceedCharacters": "描述不能超过255个字符", "apigw.api-manage.interfaceList.ApiOperationsContent.PolicyAndPlugInConfiguration": "策略与插件配置", "apigw.api-manage.interfaceList.ApiOperationsContent.ParameterDefinition": "参数定义", "apigw.components.api-operations-debug.OperationDebug.EditByFollowingThePath": "请按当前接口path规则进行编辑", "apigw.components.api-operations-policy.AddPolicyPlugin.AddPlugIns": "添加插件", "apigw.components.api-operations-policy.DeletePlugin.AreYouSureYouWant": "确定删除当前插件吗?", "apigw.components.api-operations-policy.DeletePlugin.AfterDeletionAllAssociatedCurrent": "删除后，所有已关联当前插件的资源将不再生效", "apigw.components.api-operations-policy.DeletePlugin.ThePlugInHasBeen": "删除插件成功", "apigw.components.api-operations-policy.PolicyEnvList.Publishing": "发布中", "apigw.components.api-operations-policy.PolicyEnvList.PublishedSuccessfully": "发布成功", "apigw.components.api-operations-policy.PolicyEnvList.PublishingFailed": "发布失败", "apigw.components.api-operations-policy.PolicyEnvList.PublishedButModified": "已发布但有修改", "apigw.components.api-operations-policy.PolicyEnvList.Offline": "下线中", "apigw.components.api-operations-policy.PolicyEnvList.OfflineFailed": "下线失败", "apigw.components.api-operations-policy.PolicyEnvList.EnvironmentAliasName": "环境别名/名称", "apigw.components.api-operations-policy.PolicyEnvList.InstanceNameId": "实例名称/ID", "apigw.components.api-operations-slide.ApiBasicInfo.InterfaceId": "接口 ID", "apigw.components.api-operations-slide.ApiBasicInfo.InvalidInterfacePath": "接口Path不合法", "apigw.components.api-operations-slide.ApiBasicInfo.SpecialCharactersCannotExistIn": "接口Path中不能存在特殊字符", "apigw.components.api-operations-slide.ApiBasicInfo.TheNameAlreadyExists": "该名称已存在", "apigw.components.api-operations-slide.ApiBasicInfo.TheCharacterIsNotAllowed": "不允许有@字符", "apigw.components.api-operations-slide.ApiBasicInfo.TheInterfaceNameCannotExceed": "接口名称不能超过64个字符", "apigw.api-manage.publishHistory.TheReleaseHistoryIsRetained.1": "发布历史分环境保留最近10次。切换历史版本后，仅线上生效，接口列表中现有的API与接口定义并不会被覆盖，您可在现有定义的基础上继续更新与发布。", "apigw.router.components.Configs.PublishedButModified": "已发布但有修改", "apigw.router.components.Configs.PublishingFailed": "发布失败", "apigw.router.components.Configs.OfflineFailed": "下线失败", "apigw.router.components.Configs.EnvironmentGateway": "环境/网关", "apigw.router.components.Configs.Namespace": "命名空间", "apigw.router.components.Configs.EnterANamespace": "请输入命名空间", "apigw.router.components.CreateIngress": "创建Ingress", "apigw.router.detail.ApiList": "API列表", "apigw.components.query.TheRelationshipBetweenMultipleMatching": "多个匹配条件之间是逻辑“与”的关系", "apigw.components.create-edit-route.BasicInformationMatchingRules": "基本信息&匹配规则", "apigw.components.create-edit-route.AddRouteDescription": "添加路由描述", "apigw.components.create-edit-route.EnvironmentAndBackendServices": "所属环境&后端服务", "apigw.components.create-edit-route.EnvironmentGatewayVpc": "环境/网关/VPC", "apigw.components.create-edit-route.InGrayscaleScenariosOnlyThe": "灰度场景仅能实现从网关到后端服务第一跳的灰度能力，如需全链路灰度，请参考", "apigw.components.create-edit-route.ServiceGovernanceCapabilities": "服务治理相关能力", "apigw.components.create-edit-route.SelectEnvironmentGatewayVpcFirst": "请先选择环境/网关/VPC", "apigw.components.create-edit-route.PolicyConfiguration": "策略配置", "apigw.create-service.ServiceStep.CreateFcService.Function": "函数", "apigw.create-service.ServiceStep.CreateFcService.SelectAFunctionName": "请选择函数名称", "apigw.create-service.ServiceStep.CreateFcService.SelectAFunctionAliasOr": "请选择函数别名或版本", "apigw.create-service.ServiceStep.CreateFcService.Add": "添加", "apigw.create-service.ServiceStep.CreateVipService.TheNameIsUniqueAnd": "名称唯一，支持小写字母、数字和短横线（-），不超过64个字符", "apigw.create-service.ServiceStep.CreateVipService.EnterAServiceName": "请输入服务名称", "apigw.create-service.ServiceStep.CreateVipService.TheFormatIsDnsDomain": "格式为『DNS域名:端口』，支持填写多个，以逗号（,）分隔", "apigw.create-service.ServiceStep.CreateVipService.TheIntranetDnsDomainName": "不支持内网DNS域名。如果指定的是外网地址，需要配置NAT网关。", "apigw.create-service.ServiceStep.CreateVipService.IfYouSpecifyAnInternet": "如果指定的是外网地址，需要配置NAT网关。", "apigw.create-service.ServiceStep.CreateVipService.NatGateway": "NAT网关", "apigw.components.env-manage.DeleteEnv.AssociatedResourcesExistInThe": "当前环境上存在关联资源，请解除关联后再删除", "apigw.components.env-manage.EnvManageSlide.TheDescriptionCannotExceedCharacters": "描述不能超过255个字符", "apigw.components.env-manage.EnvManageTableProps.PublicPrivateSecondaryDomainNames": "公网/私网二级域名", "apigw.components.overview.ApiTypes.GoToCreateApi": "前往创建API", "apigw.components.overview.Examples.ExamplesOfDomainNamesAnd": "域名及环境使用示例", "apigw.components.overview.ResourceListSync.RestApiPublishedTotal": "REST API (已发布/总数)", "apigw.components.plugin-manage.APIPluginConfigBasic.PlugInId": "插件ID", "apigw.components.plugin-manage.APIPluginConfigBasic.PlugInSource": "插件来源", "apigw.components.plugin-manage.APIPluginConfigBasic.PlugInCategory": "插件类别", "apigw.components.plugin-manage.APIPluginConfigBasic.EffectiveScope": "生效范围", "apigw.components.plugin-manage.APIPluginConfigBasic.pluginVersion": "插件版本", "apigw.components.plugin-manage.APIPluginConfigBasic.EffectiveTarget": "生效目标", "apigw.components.plugin-manage.APIPluginConfigBasic.BasicInformation": "基本信息", "apigw.components.plugin-manage.APIPluginConfigPanel.TheFormatOfThePlug": "插件规则格式错误", "apigw.components.plugin-manage.APIPluginConfigPanel.ConfirmEditPlugIn": "确定编辑插件", "apigw.components.plugin-manage.APIPluginConfigPanel.TheCurrentPlugInHas": "当前插件已发布，针对插件的编辑操作会及时生效。", "apigw.components.plugin-manage.APIPluginConfigPanel.Ok": "确定", "apigw.components.plugin-manage.APIPluginConfigPanel.ConfigurationInformation": "配置信息", "apigw.components.plugin-manage.DomainTransfer.EnterADomainName": "请输入域名名称", "apigw.components.plugin-manage.DomainTransfer.OptionalDomainName": "可选域名", "apigw.components.plugin-manage.DomainTransfer.SelectedDomainName": "已选域名", "apigw.components.plugin-manage.GatewayPluginConfig.RuleConfiguration": "规则配置", "apigw.components.plugin-manage.GatewayPluginConfig.InstanceNameId": "实例名称/ID", "apigw.components.plugin-manage.GatewayPluginConfig.PlugInName": "插件名称", "apigw.components.plugin-manage.GatewayPluginConfig.PlugInVersion": "插件版本", "apigw.components.plugin-manage.GatewayPluginConfig.EffectiveScope": "生效范围", "apigw.components.plugin-manage.GatewayPluginConfig.Save": "保存", "apigw.components.plugin-manage.GatewayPluginList.InstallPlugIns": "安装插件", "apigw.components.plugin-manage.InstanceTransfer.TheCurrentGatewayVersionIs": "当前网关版本为 {engineVersion}，插件支持最低网关版本为 {supportedMinGatewayVersion}", "apigw.components.plugin-manage.InstanceTransfer.GatewayNonRunningStatus": "网关非Running状态", "apigw.components.plugin-manage.InstanceTransfer.OtherPlugInVersionsAre": "已安装当前插件其他版本，请于实例内变更插件版本", "apigw.components.plugin-manage.InstanceTransfer.EnterAGatewayNameOr": "请输入网关名称或网关ID", "apigw.components.plugin-manage.InstanceTransfer.OptionalInstanceGatewayNameGateway": "可选实例（网关名称 / 网关ID / 网关版本）", "apigw.components.plugin-manage.InstanceTransfer.SelectedInstanceGatewayNameGateway": "已选实例（网关名称 / 网关ID / 网关版本）", "apigw.components.plugin-manage.PluginAttachTable.AddRule": "添加规则", "apigw.components.plugin-manage.PluginAttachTableProps.DetermineDeleteRule": "确定删除规则", "apigw.components.plugin-manage.PluginAttachTableProps.TheDeletionMayAffectOnline": "删除后可能对线上流量产生影响，请谨慎操作。", "apigw.components.plugin-manage.PluginAttachTableProps.TheRuleHasBeenDeleted": "删除规则成功", "apigw.components.plugin-manage.PluginAttachTableProps.DetermineTheDeactivationRule": "确定停用规则", "apigw.components.plugin-manage.PluginAttachTableProps.DetermineEnablingRules": "确定启用规则", "apigw.components.plugin-manage.PluginAttachTableProps.ThisModificationMayAffectOnline": "本次修改可能对线上流量产生影响，请谨慎操作。", "apigw.components.plugin-manage.PluginAttachTableProps.TheRuleHasBeenDisabled": "停用规则成功", "apigw.components.plugin-manage.PluginAttachTableProps.TheRuleIsEnabled": "启用规则成功", "apigw.components.plugin-manage.PluginAttachTableProps.RuleContent": "规则内容", "apigw.components.plugin-manage.PluginAttachTableProps.EffectiveStatus": "生效状态", "apigw.components.plugin-manage.PluginAttachTableProps.Enabled": "已启用", "apigw.components.plugin-manage.PluginAttachTableProps.NotEnabled": "未启用", "apigw.components.plugin-manage.PluginAttachTableProps.Operation": "操作", "apigw.components.plugin-manage.PluginAttachTableProps.Edit": "编辑", "apigw.components.plugin-manage.PluginAttachTableProps.Disable": "停用", "apigw.components.plugin-manage.PluginAttachTableProps.Enable": "启用", "apigw.components.plugin-manage.PluginAttachTableProps.Delete": "删除", "apigw.components.plugin-manage.PluginAttachTableProps.TheRuleConfigurationIsBeing": "规则配置正在启用中，暂不支持删除", "apigw.components.plugin-manage.PluginAttachTableProps.GatewayId": "网关ID", "apigw.components.plugin-manage.PluginAttachTableProps.EnterTheGatewayIdTo": "请输入要检索的网关 ID", "apigw.components.plugin-manage.PluginDetails.BasicInformation": "基本信息", "apigw.components.plugin-manage.PluginDetails.PlugInId": "插件标识", "apigw.components.plugin-manage.PluginDetails.PlugInName": "插件名称", "apigw.components.plugin-manage.PluginDetails.PlugInType": "插件类型", "apigw.components.plugin-manage.PluginDetails.PlugInLanguage": "插件语言", "apigw.components.plugin-manage.PluginDetails.PlugInDescription": "插件描述", "apigw.components.plugin-manage.PluginListPanel.InstallPlugIns": "安装插件", "apigw.components.plugin-manage.PluginTableProps.DefaultPhase": "默认阶段", "apigw.components.plugin-manage.PluginTableProps.AuthenticationPhase": "认证阶段", "apigw.components.plugin-manage.PluginTableProps.AuthenticationPhase.1": "鉴权阶段", "apigw.components.plugin-manage.PluginTableProps.StatisticalPhase": "统计阶段", "apigw.components.plugin-manage.PluginTableProps.UninstallThePlugIn": "卸载插件", "apigw.components.plugin-manage.PluginTableProps.ThisModificationMayAffectOnline": "本次修改可能对线上流量产生影响，请谨慎操作。", "apigw.components.plugin-manage.PluginTableProps.ThePlugInWasUninstalled": "卸载插件成功", "apigw.components.plugin-manage.PluginTableProps.PlugInVersion": "插件版本", "apigw.components.plugin-manage.PluginTableProps.ExecutionPhase": "执行阶段", "apigw.components.plugin-manage.PluginTableProps.ExecutionPriority": "执行优先级", "apigw.components.plugin-manage.PluginTableProps.PlugInName": "插件名称", "apigw.components.plugin-manage.PluginTableProps.PlugInType": "插件类型", "apigw.components.plugin-manage.PluginTableProps.PlugInSource": "插件来源", "apigw.components.plugin-manage.PluginTableProps.Operation": "操作", "apigw.components.plugin-manage.PluginTableProps.Uninstall": "卸载", "apigw.components.plugin-manage.PluginTableProps.GatewayIdGatewayName": "网关 ID/网关名称", "apigw.components.plugin-manage.PluginTableProps.VersionDescription": "版本描述", "apigw.components.plugin-manage.PluginTableProps.GatewayId": "网关ID", "apigw.components.plugin-manage.PluginTableProps.EnterTheGatewayIdTo": "请输入要检索的网关 ID", "apigw.components.plugin-manage.RouteTransfer.PleaseEnterContent": "请输入内容", "apigw.components.plugin-manage.RouteTransfer.OptionalRoute": "可选路由", "apigw.components.plugin-manage.RouteTransfer.SelectedRoute": "已选路由", "apigw.components.plugin-manage.UploadCustomPlugin.PublishPlugIns": "发布自定义插件", "apigw.components.plugin-manage.UploadCustomPlugin.ReleaseVersion": "发布版本", "apigw.components.plugin-manage.UploadCustomPlugin.PlugInId": "插件标识", "apigw.components.plugin-manage.UploadCustomPlugin.EnterTheEnglishIdOf": "请输入插件的英文标识", "apigw.components.plugin-manage.UploadCustomPlugin.PleaseEnterAnEnglishId": "请输入英文标识", "apigw.components.plugin-manage.UploadCustomPlugin.EnterTheNameOfThe": "请输入插件的名称", "apigw.components.plugin-manage.UploadCustomPlugin.EnterTheChineseNameOf": "请输入插件的中文名称", "apigw.components.plugin-manage.UploadCustomPlugin.EnterAPlugInDescription": "请输入插件描述", "apigw.components.plugin-manage.UploadCustomPlugin.EnterAVersionDescription": "请输入版本描述信息", "apigw.components.plugin-manage.UploadCustomPlugin.PleaseUploadTheWasmFile": "请上传WASM文件", "apigw.components.plugin-manage.UploadCustomPlugin.SelectExecutionPhase": "请选择执行阶段", "apigw.components.plugin-manage.UploadCustomPlugin.AdaptToTheGatewayVersion": "适配网关版本", "apigw.components.plugin-manage.UploadCustomPlugin.TheMinimumGatewayVersionAllowed": "允许安装该插件的最小网关版本", "apigw.components.plugin-manage.UploadCustomPlugin.AnyVersion": "任意版本", "apigw.components.plugin-manage.UploadCustomPlugin.SpecifiedVersion": "指定版本", "apigw.components.plugin-manage.UploadCustomPlugin.EnterTheGatewayVersion": "请输入网关版本", "apigw.components.plugin-manage.UploadCustomPlugin.EnterTheCorrectGatewayVersion": "请输入正确的网关版本", "apigw.plugin-manage.plugin-actions.GatewayPluginAction.RuleConfiguration": "规则配置", "apigw.policy.rewrite-content.PreciselyRewritingThePathWill": "精确重写Path将导致原路径中动态参数丢失，请谨慎使用！", "apigw.shared.CustomTransfer.RemoveAll": "移除全部", "apigw.shared.MultiVersionSelector.FrontEnd": "前端", "apigw.shared.NotFound.SorryTheVisitedPageDoes": "抱歉，访问的页面不存在, 即将在", "apigw.shared.NotFound.SecondsForYouToJump": "秒为您跳转至", "apigw.shared.NotFound.Go": "前往", "apigw.shared.OpAnnouncement.CloudNativeApiGatewayHas": "云原生API网关自2024年7月1日正式开启公测，公测期间网关实例免费（公网流量费用需单独计费），欢迎试用！试用期间有任何问题，您可添加钉群", "apigw.shared.OpAnnouncement.ContactUs": "联系我们。", "apigw.shared.PluginCardList.HeaderActions.PublishPlugIns": "发布插件", "apigw.shared.PluginCardList.PluginCard.HigressCommunity": "Higress 社区", "apigw.shared.PluginCardList.PluginCard.Community": "社区", "apigw.shared.PluginCardList.PluginCard.HigressOfficial": "Higress 官方", "apigw.shared.PluginCardList.PluginCard.Official": "官方", "apigw.shared.PluginCardList.PluginCard.Installation": "安装", "apigw.shared.PluginCardList.PluginCard.Configuration": "配置", "apigw.shared.PluginCardList.PluginCard.ReleaseVersion": "添加版本", "apigw.shared.PluginCardList.PluginCard.ConfirmToDeleteThePlug": "确定删除插件", "apigw.shared.PluginCardList.PluginCard.ThisModificationMayAffectOnline": "本次修改可能对线上流量产生影响，请谨慎操作。", "apigw.shared.PluginCardList.PluginCard.DeletePlugIns": "删除插件", "apigw.shared.PluginCardList.PluginCard.ViewDocuments": "查看文档", "apigw.shared.PluginCardList.PluginCard.Installed": "已安装", "apigw.shared.PluginCardList.PluginCard.NotInstalled": "未安装", "apigw.shared.PluginCardList.PluginCard.TheCurrentGatewayVersionIs": "当前网关版本为 {gatewayVersion}，插件支持最低网关版本为 {supportedMinGatewayVersion}", "apigw.shared.PluginCardList.PluginConfig.InstanceLevel": "实例级别", "apigw.shared.PluginCardList.PluginConfig.DomainLevel": "域名级别", "apigw.shared.PluginCardList.PluginConfig.RoutingLevel": "路由级别", "apigw.shared.PluginCardList.PluginConfig.CurrentlyEnabled": "（当前启用状态", "apigw.shared.PluginCardList.PluginConfig.Enabled": "已启用", "apigw.shared.PluginCardList.PluginConfig.NotEnabled": "未启用", "apigw.shared.PluginCardList.PluginConfig.EffectiveTarget": "生效目标", "apigw.shared.PluginCardList.PluginConfig.TheEffectiveTargetCannotBe": "生效目标不能为空", "apigw.shared.PluginCardList.PluginConfig.ThePlugInRuleCannot": "插件规则不能为空", "apigw.shared.PluginCardList.PluginInstall.PlugInId": "标识", "apigw.shared.PluginCardList.PluginInstall.PlugInName": "名称", "apigw.shared.PluginCardList.PluginInstall.PlugInSource": "来源", "apigw.shared.PluginCardList.PluginInstall.PlugInCategory": "类别", "apigw.shared.PluginCardList.PluginInstall.PlugInDescription": "描述", "apigw.shared.PluginCardList.PluginInstall.PlugInVersion": "版本", "apigw.shared.PluginCardList.PluginInstall.GatewayInstance": "网关实例", "apigw.shared.PluginCardList.PluginInstall.SelectAGatewayInstance": "请选择网关实例", "apigw.shared.PluginCardList.PluginInstallPanels.InstallPlugIns": "安装插件", "apigw.shared.PluginCardList.PluginInstallPanels.InstallAndConfigure": "安装并配置", "apigw.shared.PluginCardList.configHook.TheFormatOfThePlug": "插件规则格式错误", "apigw.shared.PluginCardList.configHook.DetermineModificationRules": "确定修改规则", "apigw.shared.PluginCardList.configHook.ThisModificationMayAffectOnline": "本次修改可能对线上流量产生影响，请谨慎操作。", "apigw.shared.PluginCardList.Authentication": "认证鉴权", "apigw.shared.PluginCardList.TrafficControl": "流量管控", "apigw.shared.PluginCardList.TransportProtocol": "传输协议", "apigw.shared.PluginCardList.SecurityProtection": "安全防护", "apigw.shared.PluginCardList.FlowObservation": "流量观测", "apigw.shared.PluginCardList.Custom": "自定义", "apigw.shared.PreEnvSelector.Service": "服务", "apigw.shared.ProductNotOpen.ContainerService": "容器服务", "apigw.shared.ProductNotOpen.MicroserviceEngineMse": "微服务引擎MSE", "apigw.shared.ProductNotOpen.ProductmapproductcodenameConsole": "{productMapProductCodeName}控制台", "apigw.shared.ProductNotOpen.Activate.1": "开通。", "apigw.src.constants.Default": "默认", "apigw.src.constants.PlugInMarket": "插件市场", "apigw.apiId.router.ApiList": "API列表", "apigw.apiId.router.Edit": "编辑", "apigw.apiId.router.DeleteAnApi": "删除API", "apigw.apiId.router.GatewayInstance": "网关实例", "apigw.apiId.router.ContainerCluster": "容器集群", "apigw.apiId.router.UpdateIngressstatus": "更新IngressStatus", "apigw.apiId.router.Enabled": "已启用", "apigw.apiId.router.NotEnabled": "未启用", "apigw.apiName.apiId.TheCurrentApiHasChanged": "当前API在该环境下存在变更，请", "apigw.apiName.apiId.ApiList": "API列表", "apigw.apiName.apiId.ApiListPage": "API列表页", "apigw.id.components.route-detail-status.PublishedButModified": "已发布但有修改", "apigw.id.components.route-detail-status.PublishingFailed": "发布失败", "apigw.id.components.route-detail-status.OfflineFailed": "下线失败", "apigw.BasicInfo.CustomPorts.TheHttpProtocolPortCannot": "HTTP协议端口不能重复", "apigw.BasicInfo.CustomPorts.HttpsPortsCannotBeDuplicated": "HTTPS协议端口不能重复", "apigw.BasicInfo.CustomPorts.ManagementPort": "管理端口", "apigw.BasicInfo.CustomPorts.TheFollowingPortsOnlyTake": "以下端口只对内置网关入口生效。自定义网关入口需自行到相关产品的控制台添加监听端口。", "apigw.BasicInfo.CustomPorts.Agreement": "协议", "apigw.BasicInfo.CustomPorts.Port": "端口", "apigw.BasicInfo.CustomPorts.SelectAProtocol": "请选择协议", "apigw.BasicInfo.CustomPorts.EnterAPort": "请输入端口", "apigw.BasicInfo.CustomPorts.AddCustomportslength": "添加（{customPortsLength}/5）", "apigw.components.BasicInfo.Management": "管理", "apigw.gateway.id.Service": "服务", "apigw.gateway.id.PlugIn": "插件", "apigw.id.plugin.TheFollowingListIsArranged": "以下列表按插件执行顺序排列，越靠上的插件越优先执行。", "apigw.router.components.CommonRouting": "普通路由", "apigw.router.components.IngressRouting": "Ingress路由", "apigw.id.service.Configs.NotSupported": "不支持", "apigw.EditForm.components.fc.SelectAFunctionName": "请选择函数名称", "apigw.EditForm.components.fc.SelectAFunctionAliasOr": "请选择函数别名或版本", "apigw.EditForm.components.fc.Add": "添加", "apigw.components.EditForm.CompleteTheBackendService": "请完善后端服务", "apigw.components.EditForm.TheNameIsUniqueAnd": "名称唯一，支持小写字母、数字和短横线（-），不超过64个字符", "apigw.components.EditForm.EnterAServiceName": "请输入服务名称", "apigw.components.EditForm.TheFormatIsDnsDomain": "格式为『DNS域名:端口』，支持填写多个，以逗号（,）分隔", "apigw.components.EditForm.TheIntranetDnsDomainName": "不支持内网DNS域名。如果指定的是外网地址，需要配置NAT网关。", "apigw.components.EditForm.IfYouSpecifyAnInternet": "如果指定的是外网地址，需要配置NAT网关。", "apigw.components.EditForm.NatGateway": "NAT网关", "apigw.plugin-manage.id.ConfirmToDeleteThePlug": "确定删除插件", "apigw.plugin-manage.id.ThisModificationMayAffectOnline": "本次修改可能对线上流量产生影响，请谨慎操作。", "apigw.plugin-manage.id.Configuration": "配置", "apigw.abnormal.status.InstanceListPage": "实例列表页", "apigw.pages.auth.CostEffective": "性价比高", "apigw.pages.auth.ComparedWithOpenSourceSelf": "相比开源自建，适当溢价。结合性能易用性、稳定性、安全性，降低隐性成本，可保障整体 ROI。", "apigw.pages.auth.PerformanceImprovement": "性能提升", "apigw.pages.auth.ComparedWithOpenSourceSelf.1": "相比开源自建，服务器、操作系统和网关软硬一体，整体性能提升 90%。多合一的网关架构减少了性能损粍和故障排查难度。", "apigw.pages.auth.FeatureEnhancement": "功能增强", "apigw.pages.auth.ComparedWithOpenSourceSelf.2": "相比开源自建，新增开箱即用的 WAF 防护、认证鉴权、协议转化能力、路由级限流降级、多种服务发现和插件市场。", "apigw.pages.auth.HighAvailability": "可用性高", "apigw.pages.auth.MultiAzDeploymentNodeException": "多 AZ 部署、节点异常自愈等设计，网关自身可用性 SLA 达 99.99% 以上。", "apigw.pages.auth.OpenWithoutBinding": "开放不绑定", "apigw.pages.auth.CompatibleWithMainstreamGatewayOpen": "兼容主流网关开源方案，提供自主开源方案 Higress、商业和社区可自由切换。", "apigw.pages.auth.EasyToUse": "易用性好", "apigw.pages.auth.ProvidesACompleteAndContinuous": "提供完整、持续演进的可观测体系，云产品深度集成，免运维。", "apigw.pages.auth.ApiLifecycleManagement": "API 全生命周期管理", "apigw.pages.auth.CloudNativeApiGatewayProvides": "云原生 API 网关提供 API 设计、开发、发布及下线的全生命周期管理能力，并支持多环境管理。帮助团队更高效、安全地管理和发布 API，推动业务的快速迭代和创新。", "apigw.pages.auth.MidEndHubAndMulti": "中台枢纽和多端兼容", "apigw.pages.auth.CloudNativeApiGatewayCan": "云原生 API 网关可作为各系统的 API 统一管控工具、快速实现系统间集成对接与多端接入。", "apigw.pages.auth.BuildAServerlessComputingPlatform": "搭建 Serverless 计算平台", "apigw.pages.auth.CloudNativeApiGatewayIs": "云原生 API 网关搭配函数计算，服务提供者将自己的服务能力搭建在函数计算上，通过API网关开放管理这些能力。", "apigw.pages.auth.UserPermissionsRequiredToPerform.1": "执行该操作所需的用户权限：apig:CreateAPIGServiceLinkedRole、ram:CreateServiceLinkedRole", "apigw.pages.auth.ActivateService": "开通服务", "apigw.pages.auth.ServiceActivatedSuccessfully": "服务开通成功", "apigw.pages.auth.YouHaveNotActivatedThe": "您尚未开通服务，请前往开通页开通（因商业化调整，公测期间已创建实例的用户，您需重新开通后，才可进入控制台继续使用产品）", "apigw.pages.auth.GoToActivate": "前往开通", "apigw.pages.auth.NextStep": "下一步", "apigw.pages.auth.RoleAuthorization": "角色授权", "apigw.pages.auth.RoleAuthorizationSucceeded": "角色授权成功", "apigw.pages.auth.Role": "角色:", "apigw.pages.auth.TheRamAccountYouAre": "您当前登陆的RAM账号没有关联角色权限，请点击复制", "apigw.pages.auth.AuthorizationLink": "授权链接", "apigw.pages.auth.ContactThePrimaryAccountFor": "，联系主账号进行授权。", "apigw.pages.auth.RoleName": "角色名称", "apigw.pages.auth.RolePermissionPolicy": "角色权限策略", "apigw.pages.auth.PermissionDescription": "权限说明", "apigw.pages.auth.CloudNativeApiGatewayUses": "云原生API网关使用此角色访问其他云服务中的资源。", "apigw.pages.auth.RoleDescription": "角色说明", "apigw.pages.auth.AuthorizationStatus": "授权状态", "apigw.pages.auth.Authorized.2": "授权中", "apigw.pages.auth.Unauthorized": "未授权", "apigw.pages.auth.CloudNativeApiGatewayUses.1": "云原生API网关使用此角色访问函数计算服务", "apigw.pages.auth.Authorization": "授权", "apigw.pages.auth.ActivatedSuccessfully": "开通成功", "apigw.pages.auth.CongratulationsEnabled": "恭喜您！开通成功。", "apigw.pages.auth.GoToTheConsole": "进入控制台", "apigw.pages.auth.CloudNativeApiGateway": "云原生API网关", "apigw.pages.auth.CloudNativeApiGatewayIs.1": "云原生 API 网关是一款全能型网关产品，整合了流量网关、微服务网关和安全网关能力，兼容\n            Kubernetes Ingress 标准，提供 API 全生命周期管理。云原生 API\n            网关深度融合了容器服务、微服务、函数计算等云产品，实现了云产品资源的快速对外服务，为用户打造安全、高效的API开放平台，助力企业无缝过渡至数字化时代。", "apigw.pages.auth.LearnMore": "了解详情", "apigw.pages.auth.ProductAdvantages": "产品优势", "apigw.pages.auth.ApplicationScenarios": "应用场景", "apigw.src.useHooks.useAiSchemaOrMock.IntelligentApiGeneration": "API智能生成", "apigw.utils.services.requestTimeOut.RequestApiProcessingTimedOut": "请求API处理超时，请重试", "apigw.utils.services.requestTimeOut.RetrySendingRequest": "重试发送请求", "apigw.utils.services.requestTimeOut.ErrorCode": "错误码", "apigw.utils.services.requestTimeOut.RequestId": "请求ID", "apigw.api-manage.apiList.DeleteApiAction.TheCurrentApiHasPublished": "当前API存在已发布的路由，需所有路由下线后再进行删除操作", "apigw.components.conflictDetection.Interface": "接口:", "apigw.components.conflictDetection.Route": "路由:", "apigw.components.envFilter.SelectAnEnvironment": "请选择环境", "apigw.components.envFilter.Environment": "环境", "apigw.detail.components.RouteOverview.RouteId": "路由ID", "apigw.create-service.ServiceStep.CreateVipService.TheFormatIsIpPort": "格式为『IP:端口』，支持填写多个，以逗号（,）分隔", "apigw.create-service.SourceStep.FcServiceIntegrationHasBeen": "FC服务集成已升级，要求网关版本大于等于2.0.5，当前网关实例版本过低，请", "apigw.create-service.SourceStep.UpgradeTheGatewayVersion": "升级网关版本", "apigw.components.domain-certificate.DeployResourceTableProps.Gateway": "网关", "apigw.components.domain-certificate.DeployResourceTableProps.Certificate": "证书", "apigw.components.domain-certificate.DeployResourceTableProps.CaCertificate": "Ca证书", "apigw.components.domain-certificate.DeployResourceTableProps.GatewayIdName": "网关ID/名称", "apigw.components.domain-certificate.DeployResourceTableProps.ResourceTypeResourceIdentifier": "资源类型/资源标识", "apigw.components.domain-certificate.DeployResourceTableProps.WaitingForUpdates": "等待更新", "apigw.components.domain-certificate.DeployResourceTableProps.UpdatedSuccessfully": "更新成功", "apigw.components.domain-certificate.DeployResourceTableProps.UpdateFailed": "更新失败", "apigw.components.domain-certificate.DeployResourceTableProps.Updating": "更新中", "apigw.components.domain-certificate.DeployResourceTableProps.Retry": "重试", "apigw.components.domain-certificate.DeployResources.Retried": "已重试", "apigw.components.domain-certificate.DeployResources.TheDomainNameHasFailed": "域名存在更新失败，请", "apigw.components.domain-certificate.DeployResources.Retry": "重试", "apigw.components.domain-certificate.DeployResources.DomainNameBoundResources": "域名已绑定资源", "apigw.components.domain-certificate.DeployResources.Cancel": "取消", "apigw.components.domain-certificate.DeployResources.TheCurrentDomainNameHas": "当前域名已被下列资源绑定，更新后将立即生效", "apigw.components.domain-certificate.DomainSlide.ResourcesWithFailedUpdates": "存在更新失败的资源", "apigw.components.domain-certificate.DomainSlide.TheFormInformationHasNot": "表单信息未修改", "apigw.components.domain-certificate.DomainSlide.RetryUpdateSucceeded": "重试更新成功", "apigw.components.domain-certificate.DomainSlide.AreYouSureYouWant": "确认更新域名吗？", "apigw.components.domain-certificate.DomainSlide.ResourceUpdateFailedPlease": "存在资源更新失败，请", "apigw.components.domain-certificate.DomainSlide.Retry": "重试", "apigw.components.domain-certificate.DomainSlide.Cancel": "取消", "apigw.components.domain-certificate.DomainSlide.TheCurrentDomainNameHas": "当前域名已被下列资源绑定，更新后将立即生效", "apigw.components.domain-certificate.DomainTableProps.TheDomainNameIsBeing": "域名更新中，暂不可操作", "apigw.components.env-manage.EnvManageSlide.Ok": "确定", "apigw.components.gateway.GatewayListTable.UpgradeDowngrade": "升配/降配", "apigw.components.gateway.GatewayListTable.TransferToSubscription": "转包年包月", "apigw.components.gateway.GatewayListTable.TransferToPayAsYou": "转按量付费", "apigw.components.gateway.GatewayListTable.Renew": "续费", "apigw.components.overview.QuickApiFlow.CreateAndPublishRestApi": "创建 REST API 并发布", "apigw.components.overview.QuickApiFlow.CreateAndPublishHttpApi": "创建 HTTP API 并发布", "apigw.components.overview.QuickApiFlow.GetStartedWithCloudNative": "快速上手云原生API网关", "apigw.components.overview.QuickApiFlow.ApiTypeDescription": "API类型说明", "apigw.components.overview.ResourceListSync.InstanceManagement": "实例管理", "apigw.shared.PluginCardList.HeaderActions.CanceledSuccessfully": "取消成功", "apigw.shared.PluginCardList.HeaderActions.Rebuild": "重新生成", "apigw.shared.PluginCardList.WebIDE.ThereAreCurrentlyOngoingAi": "当前有正在进行的AI任务，请返回列表查看", "apigw.shared.PluginCardList.WebIDEPage.Cancel": "取消", "apigw.apiId.router.Namespace": "命名空间", "apigw.router.detail.TheNewRouteHasBeen": "新版路由已迁移到API管理下，", "apigw.id.router.TheNewRouteHasBeen": "新版路由已迁移到API管理下，", "apigw.id.router.TheNewDomainNameHas": "新版域名已经迁移到域名管理页面，", "apigw.id.router.DomainNameManagementPage": "域名管理页", "apigw.components.EditForm.ItIsUsedToAdd": "用于在创建Cookie时添加数字签名，确保其不会被非法篡改。​", "apigw.components.EditForm.EnterCookieSecret": "请输入<PERSON><PERSON>", "apigw.components.EditForm.AutomaticGeneration": "自动生成", "apigw.components.EditForm.FcServiceIntegrationHasBeen": "FC服务集成已升级，要求网关版本大于等于2.0.5，当前网关实例版本过低，请", "apigw.components.EditForm.UpgradeTheGatewayVersion": "升级网关版本", "apigw.components.EditForm.TheFormatIsIpPort": "格式为『IP:端口』，支持填写多个，以逗号（,）分隔", "apigw.components.BasicInfo.ServiceId": "服务ID", "apigw.components.createApiDialog.TheCurrentApiIsNot": "。当前API与已存在API版本管理开启状态不同，若为同一API，版本管理开启状态需保持一致，否则需调整API名称并重新生成 Swagger 示例", "apigw.components.api-operations-policy.DeletePolicy.DeleteWafSecurityPolicies": "删除 WAF 安全防护策略", "apigw.components.api-operations-policy.DeletePolicy.YouAreCurrentlyDeletingWaf": "您当前正在删除 WAF 安全防护策略。关闭后，API 或接口将取消接入\n            WAF，您的业务系统将可能面临安全风险。是否继续？", "apigw.components.api-operations-policy.DeletePolicy.ContinueToDeleteWafSecurity": "继续，删除 WAF 安全防护策略（不推荐）", "apigw.components.api-operations-policy.PolicyPanel.EnableWafProtection": "开启 WAF 防护", "apigw.components.api-operations-policy.PolicyPanel.DisableWafProtection": "关闭 WAF 防护", "apigw.components.api-operations-policy.PolicyPanel.IfWafHasNotBeen": "如果尚未开通 WAF 3.0，本操作将以按量计费的方式为您代为开通，并将此API或接口接入 WAF 默认规则防护引擎。是否继续？", "apigw.components.api-operations-policy.PolicyPanel.ConfirmToDisableWafProtection": "确定 关闭 WAF 防护", "apigw.router.components.Configs.SelectARouteStatus": "请选择路由状态", "apigw.detail.components.RouteOverview.Environment": "环境", "apigw.detail.components.RouteOverview.Gateway": "网关", "apigw.components.gateway.GatewayListTable.Upgrade": "升配", "apigw.components.gateway.GatewayListTable.Downgrade": "降配", "apigw.policy.waf-api-content.WafProtectionIsNotEnabled": "WAF 防护功能未开通", "apigw.policy.waf-api-content.GoToActivate": "去开通", "apigw.policy.waf-api-content.WafSecurityProtectionEnabled": "WAF 安全防护已开启", "apigw.policy.waf-api-content.TheApiOrInterfaceIs": "API 或接口已处于 WAF 安全防护中，如需配置防护规则，请前往 WAF 控制台。", "apigw.policy.waf-api-content.GoToTheWafConsole": "前往 WAF 控制台配置安全防护规则", "apigw.policy.waf-api-content.WafSecurityProtectionIsNot": "WAF 安全防护未开启", "apigw.policy.waf-content.UpdateWaf.DisableRouteLevelWafProtection": "关闭路由级 WAF 防护", "apigw.policy.waf-content.UpdateWaf.EnableRouteLevelWafProtection": "开启路由级 WAF 防护", "apigw.policy.waf-content.UpdateWaf.ThisOperationMayRestartThe": "该操作可能会重启网关实例", "apigw.policy.waf-content.UpdateWaf.IfWafHasNotBeen": "如果尚未开通 WAF 3.0，本操作将以按量计费的方式为您代为开通，并将此路由接入 WAF\n            默认规则防护引擎，开启后预计五分钟左右生效。是否继续？", "apigw.policy.waf-content.WafProtectionIsNotEnabled": "WAF 防护功能未开通", "apigw.policy.waf-content.GoToActivate": "去开通", "apigw.shared.PluginCardList.PluginConfig.CurrentStatus": "（当前生效状态:", "apigw.shared.PluginCardList.PluginConfig.Effective": "已生效", "apigw.shared.PluginCardList.PluginConfig.NotEffective": "未生效", "apigw.shared.PluginCardList.PluginConfig.SelectAnApi": "请选择API", "apigw.shared.PluginCardList.configHook.InstalledSuccessfully": "安装成功", "apigw.shared.PluginCardList.configHook.InstallationFailed": "安装失败", "apigw.components.BasicInfo.WafProtection": "WAF 防护", "apigw.components.BasicInfo.wafPolicy.NotActivated": "未开通", "apigw.components.BasicInfo.wafPolicy.Activate": "开通", "apigw.components.BasicInfo.wafPolicy.Enabled": "已开启", "apigw.components.BasicInfo.wafPolicy.Close": "关闭", "apigw.components.BasicInfo.wafPolicy.RuleConfiguration": "规则配置", "apigw.components.BasicInfo.wafPolicy.NotEnabled": "未开启", "apigw.components.BasicInfo.wafPolicy.InstanceLevelWafProtectionIs": "实例级 WAF 防护处于关闭状态。建议您将网关实例接入\n              WAF，从而为业务系统提供一站式安全防护。", "apigw.components.BasicInfo.wafPolicy.LearnMore": "了解更多", "apigw.components.BasicInfo.wafPolicy.Enable": "开启", "apigw.components.BasicInfo.wafPolicy.DisableInstanceLevelWafProtection": "关闭实例级 WAF 防护", "apigw.components.BasicInfo.wafPolicy.EnableInstanceLevelWafProtection": "开启实例级 WAF 防护", "apigw.components.BasicInfo.wafPolicy.ContinueDisableInstanceLevelWaf": "继续，关闭实例级 WAF 防护（不推荐）", "apigw.components.BasicInfo.wafPolicy.ContinueEnableInstanceLevelWaf": "继续，开启实例级 WAF 防护（推荐）", "apigw.components.BasicInfo.wafPolicy.WebApplicationFirewallWafFor": "Web 应用防火墙（WAF 3.0）为您的网站或 App\n                  提供一站式安全防护能力，可以有效识别恶意流量，并在清洗和过滤后，将正常、安全的流量发送给后端服务器，进而避免系统被入侵，导致功能和性能异常，保障业务和数据安全。", "apigw.components.BasicInfo.wafPolicy.IfWafHasNotBeen": "如果尚未开通 WAF 3.0，本操作将以按量计费的方式为您代为开通，并将网关实例整体接入 WAF\n                默认规则防护引擎，开启后预计五分钟左右生效。您也可以稍后为每条路由配置单独的防护规则。是否继续？", "apigw.components.BasicInfo.wafPolicy.YouAreCurrentlyDisablingInstance": "您当前正在关闭实例级 WAF 防护。关闭后，网关实例将取消接入\n              WAF，您的业务系统将可能面临安全风险。此操作不影响路由级 WAF 接入。是否继续？", "apigw.defense.components.DefensePanel.SelectAnApi": "请选择API", "apigw.api-manage.createApi.CreateApiTypes.YouCanAlsoCreateAn": "您也可先创建API，待发布时再创建实例。", "apigw.api-manage.headBtn.TheCurrentApiIsBeing.1": "当前API正在发布中", "apigw.api-manage.headBtn.TheCurrentApiIsBeing.2": "当前API正在下线中", "apigw.api-manage.headBtn.TheCurrentEnvironmentHasNot": "当前环境暂未发布", "apigw.components.envAndBackendServices.envSelect.ThereIsNoGatewayInstance": "当前Region无网关实例，请前往创建。创建实例后，会自动创建默认环境，您也可继续添加自定义环境", "apigw.components.api-operations-debug.OperationDebug.TheMockConfigurationIsNot": "接口没有开启mock配置，无响应数据", "apigw.components.api-operations-debug.OperationDebug.TheChangeDoesNotConform": "变更不符合原接口规则，原接口path为:", "apigw.components.api-operations-debug.OperationDebug.OnlyParameterPathParameterChanges": "仅支持Parameter Path参数变更", "apigw.components.api-operations-policy.PolicyEnvList.EnvironmentNameId": "环境名称/ID", "apigw.components.plugin-manage.APIPluginConfigPanel.ViewDocuments": "查看文档", "apigw.shared.PluginCardList.HeaderActions.PlugInGenerationMoveThe": "插件生成中，点开查看详细日志", "apigw.shared.PluginCardList.HeaderActions.ThePlugInIsBeing": "插件取消中...", "apigw.shared.PluginCardList.HeaderActions.PlugInGenerationFailed": "插件生成失败", "apigw.shared.PluginCardList.HeaderActions.ThePlugInHasBeen": "插件已生成，请点击详情查看", "apigw.shared.PluginCardList.HeaderActions.PlugInCanceled": "插件已取消", "apigw.shared.PluginCardList.PluginCard.UploadFailed": "上传失败", "apigw.shared.PluginCardList.PluginCard.Uploading": "上传中", "apigw.shared.PluginCardList.PluginCard.UploadedSuccessfully": "上传成功", "apigw.shared.PluginCardList.PluginConfig.ViewDocuments": "查看文档", "apigw.components.plugin-manage.PluginTableProps.PreCertificationPhase": "认证前阶段", "apigw.components.plugin-manage.PluginTableProps.PreAuthenticationStage": "鉴权前阶段", "apigw.components.plugin-manage.PluginTableProps.PreStatisticalStage": "统计前阶段", "apigw.components.plugin-manage.PluginTableProps.DefaultPrePhase": "默认前阶段", "apigw.components.plugin-manage.PluginTableProps.PostPlugInExecutionPhase": "默认后阶段", "apigw.api-manage.apiList.ApiListTable.CurrentlyTheMockTypeIs": "当前环境发布的是Mock类型的不支持展示监控信息。", "apigw.components.api-operations-slide.ApiBasicInfo.TheInterfacePathCannotContain": "接口Path中不能存在空格", "apigw.components.operations-monitor.CurrentlyTheMockTypeIs": "当前环境发布的是Mock类型的不支持展示监控信息。", "apigw.api-manage.monitoring.CurrentlyTheMockTypeIs": "当前环境发布的是Mock类型的不支持展示监控信息。", "apigw.detail.components.RouteOverview.EnvironmentLevelDomainName": "环境二级域名", "apigw.detail.components.RouteOverview.TheEnvironmentLevelDomainName": "环境二级域名仅供测试使用，客户端直接调用时，每天有100次访问限制。生产使用需为API绑定自定义域名，并将自定义域名CNAME至环境域名上。", "apigw.components.env-manage.EnvManageTableProps.EnvironmentLevelDomainName": "环境二级域名", "apigw.components.env-manage.EnvManageTableProps.TheEnvironmentLevelDomainName": "环境二级域名仅供测试使用，客户端直接调用时，每天有100次访问限制。生产使用需为API绑定自定义域名，并将自定义域名CNAME至环境域名上。", "apigw.components.overview.QuickApiFlow.RoutingCentricNotSpecificTo": "以路由为核心的HTTP接口，适用于对接口没有统一规范约束的场景，如果您需要流量转发（包括SSE）、K8s Ingress、微服务请求处理等，建议选择当前API类型。", "apigw.components.overview.QuickApiFlow.ResourceOrientedReadsCreatesUpdates": "RESTful风格的HTTP接口，所有接口遵循统一的OpenAPI规范，适用于前后端及系统间协作、API精细化管理等场景。如果您需要为协作方提供标准访问接口，包括文档、SDK，建议选择当前API类型。", "apigw.components.overview.QuickApiFlow.CreateAnApi": "创建API", "apigw.components.overview.CloudNativeApiGatewayWas": "云原生API网关已于2024年9月9日正式开启商业化", "apigw.components.overview.CommercialProductsProvideYouWith": "商业化产品为您提供更多规格选择以及99.99%的SLA保障。公测期间创建的网关实例自2024年9月9日00:00:00起自动开启收费，若您不再使用，请及时释放！使用中有任何问题，欢迎加入钉钉群\n        88010006189 交流。", "apigw.pages.auth.YouHaveNotActivatedThe.1": "您尚未开通服务，请前往开通页开通", "apigw.pages.auth.DueToCommercialAdjustmentsUsers": "因商业化调整，公测期间已创建实例的用户，您需重新开通后，才可进入控制台继续使用产品", "apigw.api-manage.apiList.ApiListTable.ForMoreMonitoringDataGo": "更多监控数据请前往", "apigw.api-manage.apiList.ApiListTable.CloudMonitoring": "云监控", "apigw.api-manage.apiList.ApiListTable.View": "查看", "apigw.api-manage.DeleteApiAction.AreYouSureYouWant": "确定删除API：{apiName}？", "apigw.api-manage.DeleteApiAction.ThisOperationWillDeleteThe": "API所在实例：{apiGateway}，该操作将从上述实例中删除API，且不可恢复，请谨慎执行。如您确认此操作，请在下方文本框中输入API名称：{apiName}", "apigw.api-manage.apiList.DeleteApiAction.ThisOperationWillDeleteThe": "该操作将删除API，且不可恢复，请谨慎执行。如您确认此操作，请在下方文本框中输入API名称：{apiName}", "apigw.api-manage.apiList.DeleteApiAction.EnterTheCurrentApiName": "请输入当前的API名称", "apigw.components.createApiDialog.SelectYamlYmlAndJson": "请选择yaml、yml、json格式的文件", "apigw.components.createApiDialog.UploadMethod": "上传方式", "apigw.components.createApiDialog.LocalFile": "本地文件", "apigw.components.createApiDialog.ImportOssFiles": "OSS文件导入", "apigw.components.createApiDialog.CustomUrl": "自定义URL", "apigw.components.createApiDialog.TheUrlCannotBeEmpty": "URL不能为空", "apigw.components.createApiDialog.EnterAUrl": "请输入URL", "apigw.components.createApiDialog.Preview": "预览", "apigw.components.createApiDialog.PreviewIsNotCurrentlySupported": "当前不支持预览", "apigw.components.createApiDialog.ossBucket.Dubai": "迪拜", "apigw.components.createApiDialog.ossBucket.Meixi": "美西", "apigw.components.createApiDialog.ossBucket.EastChinaHangzhou": "华东1（杭州）", "apigw.components.createApiDialog.ossBucket.EastChinaShanghai": "华东2（上海）", "apigw.components.createApiDialog.ossBucket.NorthChinaBeijing": "华北2（北京）", "apigw.components.createApiDialog.ossBucket.SouthChinaShenzhen": "华南1（深圳）", "apigw.components.createApiDialog.ossBucket.NorthChinaZhangjiakou": "华北3（张家口）", "apigw.components.createApiDialog.ossBucket.NorthChinaQingdao": "华北1（青岛）", "apigw.components.createApiDialog.ossBucket.NorthChinaHohhot": "华北5（呼和浩特）", "apigw.components.createApiDialog.ossBucket.NorthChinaWulanchabu": "华北6（乌兰察布）", "apigw.components.createApiDialog.ossBucket.SouthChinaHeyuan": "华南2（河源）", "apigw.components.createApiDialog.ossBucket.SouthChinaGuangzhou": "华南3（广州）", "apigw.components.createApiDialog.ossBucket.SouthwestChengdu": "西南1（成都）", "apigw.components.createApiDialog.ossBucket.ChinaHongKong": "中国（香港）", "apigw.components.createApiDialog.ossBucket.EastChinaFinancialCloud": "华东 2 金融云", "apigw.components.createApiDialog.ossBucket.NorthChinaFinancialCloud": "华北2 金融云", "apigw.components.createApiDialog.ossBucket.NorthChinaAlibabaGovernmentCloud": "华北 2 阿里政务云1", "apigw.components.createApiDialog.ossBucket.SouthChinaFinancialCloud": "华南 1 金融云", "apigw.components.createApiDialog.ossBucket.SingaporeSingapore": "新加坡（新加坡）", "apigw.components.createApiDialog.ossBucket.AustraliaSydney": "澳大利亚（悉尼）", "apigw.components.createApiDialog.ossBucket.MalaysiaKualaLumpur": "马来西亚（吉隆坡）", "apigw.components.createApiDialog.ossBucket.IndonesiaJakarta": "印度尼西亚（雅加达）", "apigw.components.createApiDialog.ossBucket.JapanTokyo": "日本（东京）", "apigw.components.createApiDialog.ossBucket.GermanyFrankfurt": "德国（法兰克福）", "apigw.components.createApiDialog.ossBucket.UkLondon": "英国（伦敦）", "apigw.components.createApiDialog.ossBucket.UsSiliconValley": "美国（硅谷）", "apigw.components.createApiDialog.ossBucket.UsaVirginia": "美国（弗吉尼亚）", "apigw.components.createApiDialog.ossBucket.RussiaMoscow": "俄罗斯（莫斯科）", "apigw.components.createApiDialog.ossBucket.PhilippinesManila": "菲律宾（马尼拉）", "apigw.components.createApiDialog.ossBucket.IndiaMumbai": "印度（孟买）", "apigw.components.createApiDialog.ossBucket.NoOssFilesAreAvailable": "该Bucket下暂无oss文件", "apigw.components.createApiDialog.ossBucket.SelectAnOssFile": "请选择OSS的文件", "apigw.components.createApiDialog.ossBucket.BackToHomePage": "回到首页", "apigw.components.createApiDialog.ossBucket.SelectYamlYmlAndJson": "请选择yaml、yml、json格式的文件", "apigw.components.createApiDialog.ossBucket.RegionSelection": "Region选择", "apigw.components.createApiDialog.ossBucket.RegionCannotBeEmpty": "Region不能为空", "apigw.components.createApiDialog.ossBucket.OssBucketDoesNotChange": "OSS Bucket不变为空", "apigw.components.createApiDialog.ossBucket.CurrentlyBucketsWithoutRegionAttributes": "暂不支持无地域属性的Bucket", "apigw.api-manage.headBtn.AreYouSureYouWant": "确定删除API？", "apigw.api-manage.headBtn.ThisOperationWillDeleteThe": "该操作将删除API，且不可恢复，请谨慎执行。如您确认此操作，请在下方文本框中输入API名称：{apiName}", "apigw.api-manage.headBtn.EnterTheCurrentApiName": "请输入当前的API名称", "apigw.components.envAndBackendServices.envSelect.Instance": "实例", "apigw.components.envAndBackendServices.envSelect.Creating": "创建中", "apigw.components.envAndBackendServices.envSelect.ExpectedMinutesPleaseRefreshAfter": "，预计1-3分钟，请在实例创建成功后刷新", "apigw.headBtn.publish.DebuggingInterface": "1．调试接口：", "apigw.headBtn.publish.OnlineTestInterface": "在线测试接口", "apigw.headBtn.publish.ConnectivityAndFunctions": "的连通性与功能", "apigw.api-manage.interfaceList.ApiOperationsContent.AddInterface": "添加接口", "apigw.api-manage.interfaceList.ApiOperationsMenu.InterfaceList": "接口列表", "apigw.api-manage.interfaceList.ApiOperationsMenu.Total": "({total} 个)", "apigw.api-operations-slide.api-definition-info.Mock.WhenTheApiPublishingScenario": "当 API 发布场景为 Mock 时，将返回 Mock 结果；当后端调整为真实服务后，返回结果将自动变更为后端真实服务的返回。", "apigw.api-manage.monitoring.ForMoreMonitoringDataGo": "更多监控数据请前往", "apigw.api-manage.monitoring.CloudMonitoring": "云监控", "apigw.api-manage.monitoring.View": "查看", "apigw.api-manage.publishHistory.switchVersion.AfterSwitchingOnlyOnlinePublished": "切换后，仅覆盖线上已发布的数据，API设计列表中的数据不会被覆盖", "apigw.router.components.ForMoreMonitoringDataGo": "更多监控数据请前往", "apigw.router.components.CloudMonitoring": "云监控", "apigw.router.components.View": "查看", "apigw.router.components.DueToTheAdjustmentOf": "因监控体系调整，2024年9月26日之前的监控数据需前往云监控查看", "apigw.detail.components.RouteMonitor.ForMoreMonitoringDataGo": "更多监控数据请前往", "apigw.detail.components.RouteMonitor.CloudMonitoring": "云监控", "apigw.detail.components.RouteMonitor.View": "查看", "apigw.detail.components.RouteMonitor.DueToTheAdjustmentOf": "因监控体系调整，2024年9月26日之前的监控数据需前往云监控查看", "apigw.detail.components.RoutePolicyConfig.ConsumerCertification": "消费者认证", "apigw.CreateConsumerSlide.Authentication.AuthCardWrapper.PleaseChangeTheAuthenticationMethod": "请在详情页变更认证方式", "apigw.CreateConsumerSlide.Authentication.Columns.Type": "类型", "apigw.CreateConsumerSlide.Authentication.Columns.Prefix": "前缀", "apigw.CreateConsumerSlide.Authentication.Columns.WhetherToPassThrough": "是否透传", "apigw.components.CreateConsumerSlide.SlideContent.CreatedSuccessfully": "创建成功", "apigw.components.CreateConsumerSlide.SlideContent.EditedSuccessfully": "编辑成功", "apigw.components.CreateConsumerSlide.SlideContent.ConsumerName": "消费者名称", "apigw.components.CreateConsumerSlide.SlideContent.TheConsumerNameCannotBe": "消费者名称不能为空", "apigw.components.CreateConsumerSlide.SlideContent.TheConsumerNameMustStart": "消费者名称必须以字母或数字开头和结尾，长度至少为 2，仅支持字母、数字、“-”、“.”。", "apigw.components.CreateConsumerSlide.SlideContent.EnterAConsumerName": "请输入消费者名称", "apigw.components.CreateConsumerSlide.SlideContent.Description": "描述", "apigw.components.CreateConsumerSlide.SlideContent.TheDescriptionCannotExceedCharacters": "描述不能超过100个字符", "apigw.components.CreateConsumerSlide.SlideContent.EnterADescription": "请输入描述内容", "apigw.components.CreateConsumerSlide.SlideContent.AuthenticationMethod": "认证方式", "apigw.components.CreateConsumerSlide.SlideContent.TheAuthenticationMethodCannotBe": "认证方式不能为空", "apigw.components.CreateConsumerSlide.SlideContent.PleaseFillInTheAuthentication": "请填写认证方式信息", "apigw.components.CreateConsumerSlide.CreateAConsumer": "创建消费者", "apigw.components.CreateConsumerSlide.Create": "创建", "apigw.components.CreateConsumerSlide.EditConsumer": "编辑消费者", "apigw.components.CreateConsumerSlide.Ok": "确定", "apigw.components.shared.AkSkForm.GenerationMethod": "生成方式", "apigw.components.shared.AkSkForm.CannotBeEmpty": "不能为空", "apigw.components.shared.AkSkForm.SystemProduction": "系统生成", "apigw.components.shared.AkSkForm.Custom": "自定义", "apigw.components.shared.AkSkForm.ItCanBeToCharacters": "长度为8-128个字符，可包含英文、数字、下划线（_）和短横线（-）", "apigw.components.shared.AkSkForm.AccessKeyCannotBeEmpty": "Access Key不能为空", "apigw.components.shared.AkSkForm.EnterAccessKey": "请输入Access Key", "apigw.components.shared.AkSkForm.SecretKeyCannotBeEmpty": "Secret Key不能为空", "apigw.components.shared.ApiKeyForm.CustomHttpHeader": "自定义 HTTP Header", "apigw.components.shared.ApiKeyForm.CustomQueryString": "自定义 Query 参数", "apigw.components.shared.ApiKeyForm.GenerationMethod": "生成方式", "apigw.components.shared.ApiKeyForm.CannotBeEmpty": "不能为空", "apigw.components.shared.ApiKeyForm.SystemProduction": "系统生成", "apigw.components.shared.ApiKeyForm.Custom": "自定义", "apigw.components.shared.ApiKeyForm.Credential": "凭证", "apigw.components.shared.ApiKeyForm.ItCanBeToCharacters": "长度为8-128个字符，可包含英文、数字、下划线（_）和短横线（-）", "apigw.components.shared.ApiKeyForm.EnterACredential": "请输入凭证", "apigw.components.shared.ApiKeyForm.CredentialSource": "凭证来源", "apigw.components.shared.ApiKeyForm.SelectACredentialSource": "请选择凭证来源", "apigw.components.shared.ApiKeyForm.EnterAHeader": "请输入 Header", "apigw.components.shared.ApiKeyForm.EnterAQuery": "请输入 Query", "apigw.components.shared.JwtForm.TheIndexOfThePublic": "公钥的指数，例如AQAB", "apigw.components.shared.JwtForm.TheFamilyOfEncryptionAlgorithms": "使用的加密算法的家族，例如RSA，必填，大小写敏感", "apigw.components.shared.JwtForm.TheSpecificEncryptionAlgorithmUsed": "使用的具体的加密算法，例如RS256，必填，大小写敏感", "apigw.components.shared.JwtForm.ThePurposeOfTheKey": "密钥的用途，例如sig，用于签名", "apigw.components.shared.JwtForm.ModulusOfPublicKey": "公钥的模值", "apigw.components.shared.JwtForm.KeyType": "密钥类型", "apigw.components.shared.JwtForm.CannotBeEmpty": "不能为空", "apigw.components.shared.JwtForm.SymmetricKey": "对称密钥", "apigw.components.shared.JwtForm.AsymmetricKey": "非对称密钥", "apigw.components.shared.JwtForm.JwtPublicKeyJsonFormat": "JWT公钥，JSON格式", "apigw.components.shared.JwtForm.InvalidJson": "JSON不合法", "apigw.components.shared.JwtForm.JwksTokenConfiguration": "JWKS Token 配置", "apigw.components.shared.JwtForm.TheTokenParameterInformationTo": "需要校验的token参数信息，默认是以 Bearer 为前缀放在 Authorization header\n              中，如：Authorization。", "apigw.components.shared.JwtForm.PleaseSelect": "请选择", "apigw.components.shared.JwtForm.PleaseEnter": "请输入", "apigw.components.shared.JwtForm.Yes": "是", "apigw.components.shared.JwtForm.No": "否", "apigw.components.shared.JwtForm.ConsumerIdentificationWithinJwksPayload": "JWKS Payload 内消费者标识", "apigw.components.shared.JwtForm.YouCanUseTheUid": "您可以在 JWT Payload 中使用下方提供的 uid 来表示该消费者身份，也可以填入其他的\n              Key/Value 来适配您的 JWT 逻辑。", "apigw.consumer-manage.consumer-auth.ApiTransfer.OptionalApi": "可选API", "apigw.consumer-manage.consumer-auth.ApiTransfer.SelectedApi": "已选API", "apigw.consumer-manage.consumer-auth.ApiVersionFormItem.CurrentApi": "当前API", "apigw.consumer-manage.consumer-auth.ApiVersionFormItem.EffectiveApi": "生效API", "apigw.consumer-manage.consumer-auth.ApiVersionFormItem.SelectAnApi": "请选择API", "apigw.consumer-manage.consumer-auth.ApiVersionFormItem.ApiVersion": "API版本", "apigw.consumer-manage.consumer-auth.ApiVersionFormItem.EffectiveVersion": "生效版本", "apigw.consumer-manage.consumer-auth.ApiVersionFormItem.InitializeVersion": "初始化版本", "apigw.consumer-manage.consumer-auth.ApiVersionPreview.CurrentApi": "当前API", "apigw.consumer-manage.consumer-auth.ApiVersionPreview.ApiVersion": "API版本", "apigw.consumer-manage.consumer-auth.ApiVersionPreview.InitializeVersion": "初始化版本", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectApi": "选择API", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectAnInterface": "选择接口", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectARoute": "选择路由", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.YyyyMmMonthDdDay": "YYYY年MM月DD日", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.AuthorizationSucceeded": "授权成功", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.EditedSuccessfully": "编辑成功", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.AuthorizationFailed": "授权失败", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.FailedToEdit": "编辑失败", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ApiAuthorization": "API授权", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.InterfaceAuthorization": "接口授权", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.RouteAuthorization": "路由授权", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.AddTitlesgetvalueattachresourcetype": "添加{titlesGetValueAttachResourceType}", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.EditTitlesgetvalueattachresourcetype": "编辑{titlesGetValueAttachResourceType}", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Ok": "确定", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Cancel": "取消", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ResourceChangeFailedPleaseTry": "资源变更失败，请重试", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ApiType": "API类型", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.PleaseSelect": "请选择", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.EffectiveEnvironmentNameId": "生效环境名称/ID", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.InstanceNameId": "所属实例名称/ID", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ScopeOfAuthorization": "授权范围", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Interface": "接口", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Routing": "路由", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectTheApiToAuthorize": "请选择要授权的API", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectTheInterfaceToAuthorize": "请选择要授权的接口", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectARouteToAuthorize": "请选择要授权的路由", "apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.ConfirmTheAuthorizationOfName": "确认解除 {name} 的授权", "apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.Release": "解除", "apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.RevokeAuthorization": "解除授权", "apigw.consumer-manage.consumer-auth.OperationsTransfer.OptionalInterface": "可选接口", "apigw.consumer-manage.consumer-auth.OperationsTransfer.SelectedInterface": "已选接口", "apigw.consumer-manage.consumer-auth.RouteTransfer.OptionalRoute": "可选路由", "apigw.consumer-manage.consumer-auth.RouteTransfer.SelectedRoute": "已选路由", "apigw.consumer-manage.consumer-auth.Authorization": "授权", "apigw.consumer-manage.consumer-auth.tableProps.InitializeVersion": "初始化版本", "apigw.consumer-manage.consumer-auth.tableProps.ResourceChangeFailedPleaseTry": "资源变更失败，请重试", "apigw.consumer-manage.consumer-auth.tableProps.VersionVersion": "版本：{version}", "apigw.consumer-manage.consumer-auth.tableProps.ScopeOfAuthorization": "授权范围", "apigw.consumer-manage.consumer-auth.tableProps.Interface": "接口", "apigw.consumer-manage.consumer-auth.tableProps.Routing": "路由", "apigw.consumer-manage.consumer-auth.tableProps.EnvironmentGateway": "环境/网关", "apigw.consumer-manage.consumer-auth.tableProps.InterfaceName": "接口名称", "apigw.consumer-manage.consumer-auth.tableProps.Path": "路径", "apigw.consumer-manage.consumer-auth.tableProps.RouteName": "路由名称", "apigw.consumer-manage.consumer-auth.tableProps.EnterAnApiNameTo": "请输入API名称进行搜索", "apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe": "当前API已存在授权", "apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe.1": "当前接口已存在授权", "apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe.2": "当前路由已存在授权", "apigw.consumer-manage.consumer-auth.useResourceList.AuthorizationAlreadyExistsForThe.3": "当前 MCP 服务已存在授权", "apigw.consumer-manage.consumer-auth.useResourceList.ConsumerAuthenticationNotEnabled": "未配置消费者认证策略", "apigw.consumer-manage.consumer-auth.useResourceList.InconsistentAuthorizationMethods": "授权方式不一致", "apigw.consumer-manage.consumer-auth.useResourceList.InitializeVersion": "初始化版本", "apigw.consumer-manage.consumer-basic-infor.AddKeyDialog.AddedSuccessfully": "添加成功", "apigw.consumer-manage.consumer-basic-infor.AddKeyDialog.FailedToAdd": "添加失败", "apigw.consumer-manage.consumer-basic-infor.AddKeyDialog.AddAkSk": "添加 AK/SK", "apigw.consumer-manage.consumer-basic-infor.AddKeyDialog.AddApiKey": "添加 API Key", "apigw.consumer-manage.consumer-basic-infor.AddKeyDialog.Add": "添加", "apigw.consumer-manage.consumer-basic-infor.Authentication.ResetSecretKey": "重置Secret Key", "apigw.consumer-manage.consumer-basic-infor.Authentication.ResetCredentials": "重置凭证", "apigw.consumer-manage.consumer-basic-infor.Authentication.Delete": "删除", "apigw.consumer-manage.consumer-basic-infor.Authentication.AtLeastOneAuthenticationMethod": "必须至少保留一个认证方式", "apigw.consumer-manage.consumer-basic-infor.Authentication.Credential": "凭证", "apigw.consumer-manage.consumer-basic-infor.Authentication.CredentialSource": "凭证来源", "apigw.consumer-manage.consumer-basic-infor.Authentication.AreYouSureYouWant": "确认删除当前 AK/SK 吗？", "apigw.consumer-manage.consumer-basic-infor.Authentication.AreYouSureYouWant.1": "确认删除当前凭证吗？", "apigw.consumer-manage.consumer-basic-infor.Authentication.DeletingAkSkWillAffect": "删除 AK/SK 后，将影响已使用该方式调用的API，请谨慎操作。", "apigw.consumer-manage.consumer-basic-infor.Authentication.DeletingACredentialWillAffect": "删除凭证后，将影响已使用该方式调用的API，请谨慎操作。", "apigw.consumer-manage.consumer-basic-infor.Authentication.AuthenticationMethod": "认证方式", "apigw.consumer-manage.consumer-basic-infor.Authentication.AddAkSk": "添加AK/SK", "apigw.consumer-manage.consumer-basic-infor.Authentication.AddCredentials": "添加凭证", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.DeleteJwtAuthenticationMethod": "删除JWT认证方式", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.AreYouSureYouWant": "确定要删除JWT认证方式吗？", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.AuthenticationMethod": "认证方式", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.TokenLocation": "Token 位置", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.KeyType": "密钥类型", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.SymmetricKey": "对称密钥", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.AsymmetricKey": "非对称密钥", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.ConsumerIdentification": "消费者标识", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.Prefix": "前缀", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.GeneticOrNot": "是否遗传", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.Yes": "是", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.No": "否", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.EditJwt": "编辑JWT", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.CreateJwt": "创建JWT", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.Edit": "编辑", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.Create": "创建", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.Ok": "确定", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.Cancel": "取消", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.Delete": "删除", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.JwtAuthenticationMethodNotConfigured": "未配置JWT认证方式", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.AtLeastOneAuthenticationMethod": "必须至少保留一个认证方式", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.ResetSucceeded": "重置成功", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.ResetFailed": "重置失败", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.AreYouSureYouWant": "确认重置 Secret Key 吗？", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.AreYouSureToReset": "确认重置凭证吗", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.Reset": "重置", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.ResettingSecretKeyWillAffect": "重置 Secret Key 后，将影响已使用该方式调用的API，请谨慎操作。", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.ResettingTheCredentialWillAffect": "重置凭证后，将影响已使用该方式调用的API，请谨慎操作。", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.CustomSecretKey": "自定义 Secret Key", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.CustomCredentials": "自定义凭证", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.Enable": "开启", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.Close": "关闭", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.Credential": "凭证", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.ItCanBeToCharacters": "长度为8-128个字符，可包含英文、数字、下划线（_）和短横线（-）", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.SecretKeyCannotBeEmpty": "Secret Key不能为空", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.ApiKeyCannotBeEmpty": "API Key不能为空", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.EnterSecretKey": "请输入 Secret Key", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.EnterACredential": "请输入凭证", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.CredentialSource": "凭证来源", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.SelectACredentialSource": "请选择凭证来源", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.EnterAHeader": "请输入 Header", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.EnterAQuery": "请输入 Query", "apigw.consumer-manage.consumer-basic-infor.Name": "名称", "apigw.consumer-manage.consumer-basic-infor.CreationTime": "创建时间", "apigw.consumer-manage.consumer-basic-infor.Description": "描述", "apigw.consumer-manage.consumer-basic-infor.BasicInformation": "基本信息", "apigw.consumer-manage.consumer-list.ConsumerTable.CreateAConsumer": "创建消费者", "apigw.consumer-manage.consumer-list.ConsumerTableProps.ConsumerNameId": "消费者名称/ID", "apigw.consumer-manage.consumer-list.ConsumerTableProps.ResourceChangeFailedPleaseTry": "资源变更失败，请重试", "apigw.consumer-manage.consumer-list.ConsumerTableProps.ConsumerId": "消费者ID", "apigw.consumer-manage.consumer-list.ConsumerTableProps.CreationTime": "创建时间", "apigw.consumer-manage.consumer-list.ConsumerTableProps.SearchForConsumerNames": "搜索消费者名称", "apigw.consumer-manage.consumer-list.DeleteConsumer.Consumers": "消费者", "apigw.consumer-manage.consumer-list.DeleteConsumer.Delete": "删除", "apigw.components.create-default-alert-rule.DefaultAlertForGatewayCpu": "网关CPU使用率默认告警", "apigw.components.create-default-alert-rule.DefaultAlertForGatewayMemory": "网关内存使用率默认告警", "apigw.components.create-default-alert-rule.DefaultAlertForGatewayPlug": "网关插件崩溃默认告警", "apigw.components.create-default-alert-rule.TheDefaultAlertRuleHas": "默认告警规则已创建，如需调整，请至", "apigw.components.create-default-alert-rule.AlarmManagement": "告警管理", "apigw.components.create-default-alert-rule.ModifyTheMenu": "菜单中修改。", "apigw.create-service.ServiceStep.CreateFcService.TheCurrentAliasOrVersion": "当前别名或版本已添加", "apigw.create-service.SourceStep.NotOpenYet": "暂未开放", "apigw.create-service.SourceStep.TheCurrentInstanceVersionIs": "当前实例版本较低，暂不支持此功能，请先", "apigw.create-service.SourceStep.Upgrade": "升级", "apigw.create-service.SourceStep.ToOrLater": "至2.0.8版本及以上。", "apigw.create-service.SourceStep.TheInstanceUpgradeTakesAbout": "实例升级中，大约需要持续 5～10 分钟，请稍后再创建 SAE Kubernetes 服务。", "apigw.components.create-service.FunctionComputeIsSupportedCurrently": "支持函数计算3.0，暂不支持函数计算2.0的服务来源", "apigw.components.domain-certificate.DeleteDomain.AreYouSureYouWant": "确定删除域名？", "apigw.components.domain-certificate.DeleteDomain.ThisOperationWillDeleteThe": "该操作将删除域名，且不可恢复，请谨慎执行。如您确认此操作，请在下方文本框中输入域名：{name}", "apigw.components.domain-certificate.DeleteDomain.EnterTheCurrentDomainName": "请输入当前的域名", "apigw.components.domain-certificate.DomainSlide.AddingDomainNamesIsNot": "添加域名不会自动进行", "apigw.components.domain-certificate.DomainSlide.DomainNameRegistration": "域名注册", "apigw.components.domain-certificate.DomainSlide.And": "及", "apigw.components.domain-certificate.DomainSlide.DnsResolution": "DNS解析", "apigw.components.domain-certificate.DomainSlide.YouMustCompleteTheDomain": "，您需要自行完成域名注册，以及到环境二级域名之间的解析设置。", "apigw.components.domain-certificate.DomainTableProps.DomainName": "域名", "apigw.components.env-manage.DeleteEnv.ThisOperationWillDeleteThe": "该操作将删除环境，且不可恢复，请谨慎执行。如您确认此操作，请在下方文本框中输入环境的名称：{name}", "apigw.components.env-manage.DeleteEnv.EnterTheCurrentEnvironmentName": "请输入当前的环境名称", "apigw.components.env-manage.EnvManageSlide.EnterAnEnvironmentNameSuch": "请输入环境名称，如dev、pre、prod等", "apigw.components.overview.QuickApiFlow.FreeTrial": "免费试用", "apigw.components.plugin-manage.PluginLog.CreateAnInstance": "创建实例", "apigw.components.plugin-manage.PluginLog.Refresh": "刷新", "apigw.components.plugin-manage.PluginLog.PlugInLogs": "插件日志", "apigw.components.plugin-manage.PluginLog.TheCurrentFeatureIsNot": "当前功能暂不可用。您需要首先安装插件到网关实例，然后才能查看日志。", "apigw.components.plugin-manage.PluginUpload.UploadFailedPleaseUploadAgain": "上传失败，请重新上传", "apigw.components.plugin-manage.RouteTransfer.Name": "名称", "apigw.components.plugin-manage.RouteTransfer.Path": "路径", "apigw.policy.consumer.ConsumerCertification": "消费者认证", "apigw.policy.consumer.AuthenticationMethod": "认证方式", "apigw.policy.flowMirror-content.FunctionComputeIsNotSupported": "暂不支持函数计算服务", "apigw.shared.ConsumerTransfer.TransferItem.View": "查看", "apigw.shared.ConsumerTransfer.TransferList.ApiName": "API名称", "apigw.shared.ConsumerTransfer.TransferList.Version": "版本", "apigw.shared.ConsumerTransfer.TransferList.View": "查看", "apigw.shared.ConsumerTransfer.TransferList.DeleteSelectedItems": "删除选中项", "apigw.shared.ConsumerTransfer.Total": "({total}个)", "apigw.shared.PluginCardList.PluginCard.PlugInUploadFailed": "插件上传失败", "apigw.shared.PluginCardList.configHook.RuleAddedSuccessfully": "添加规则成功", "apigw.shared.PluginCardList.configHook.TheRuleHasBeenModified": "修改规则成功", "apigw.shared.RiskConfirm.ThisOperationWillDeleteType": "该操作将删除{type}，且不可恢复，请谨慎执行。如您确认此操作，请在下方文本框中输入{type}名称：{confirmKey}", "apigw.shared.RiskConfirm.EnterTheCurrentTypeName": "请输入当前的{type}名称", "apigw.shared.RiskConfirm.Ok": "确定", "apigw.shared.RiskConfirm.Cancel": "取消", "apigw.shared.RiskConfirm.AreYouSureYouWant": "确定删除{type}？", "apigw.src.constants.apiManage.PublicNetworkPrivateNetwork": "公网+私网", "apigw.$apiName.$apiId.ApiDesign": "API设计", "apigw.consumer-manage.$id.Consumers": "消费者", "apigw.consumer-manage.$id.Edit": "编辑", "apigw.consumer-manage.$id.BasicInformation": "基本信息", "apigw.consumer-manage.$id.ConsumerAuthorization": "消费者授权", "apigw.$regionId.consumer-manage.Consumers": "消费者", "apigw.components.BasicInfo.NetworkAccessType": "网络访问类型", "apigw.components.EntranceSet.BindOrMigrateEntrance.AMaximumOfClbNlb": "最多只能绑定20个 CLB/NLB 实例", "apigw.components.EntranceSet.BindOrMigrateEntrance.BindOrMigrateEntries": "绑定或迁移入口", "apigw.components.EntranceSet.BindOrMigrateEntrance.Bind": "绑定", "apigw.components.EntranceSet.BindOrMigrateEntrance.Migration": "迁移", "apigw.components.EntranceSet.BindOrMigrateEntrance.YourOperationBelongs": "操作场景", "apigw.components.EntranceSet.BindOrMigrateEntrance.BindAnIdleGatewayEntry": "绑定空闲的网关入口", "apigw.components.EntranceSet.BindOrMigrateEntrance.MigrateGatewayEntriesInUse": "迁移正在使用的网关入口", "apigw.components.EntranceSet.BindOrMigrateEntrance.SlbType": "SLB类型", "apigw.components.EntranceSet.BindOrMigrateEntrance.TraditionalClb": "传统型负载均衡CLB", "apigw.components.EntranceSet.BindOrMigrateEntrance.NetworkSlb": "网络型负载均衡NLB", "apigw.components.EntranceSet.BindOrMigrateEntrance.NetworkType": "网络类型", "apigw.components.EntranceSet.BindOrMigrateEntrance.PublicNetwork": "公网", "apigw.components.EntranceSet.BindOrMigrateEntrance.PrivateNetwork": "私网", "apigw.components.EntranceSet.BindOrMigrateEntrance.ClbInstance": "CLB实例", "apigw.components.EntranceSet.BindOrMigrateEntrance.NlbInstance": "NLB实例", "apigw.components.EntranceSet.BindOrMigrateEntrance.SelectAClbInstance": "请选择CLB实例", "apigw.components.EntranceSet.BindOrMigrateEntrance.DoNotSelectTheClb": "请勿选择云产品正在使用的 CLB 实例。", "apigw.components.EntranceSet.BindOrMigrateEntrance.ListeningPortAndBackendServer": "监听端口及后端服务器", "apigw.components.EntranceSet.BindOrMigrateEntrance.PleaseFillInTheWeight": "请填写权重", "apigw.components.EntranceSet.BindOrMigrateEntrance.EnterTheWeightRatioOf": "请填写网关在虚拟服务器组中的权重比例（0-100）", "apigw.components.EntranceSet.Configs.InstanceIdName": "实例ID/名称", "apigw.components.EntranceSet.Configs.EntryAddress": "入口地址", "apigw.components.EntranceSet.Configs.ListeningPort": "监听端口", "apigw.components.EntranceSet.Configs.InstanceType": "实例类型", "apigw.components.EntranceSet.Configs.NetworkType": "网络类型", "apigw.components.EntranceSet.Configs.OperationResult": "操作结果", "apigw.components.EntranceSet.Configs.ViewMonitoring": "查看监控", "apigw.components.EntranceSet.Configs.Edit": "编辑", "apigw.components.EntranceSet.Configs.Loadbalancerid": "（{loadBalancerId}）", "apigw.components.EntranceSet.TheMaximumNetworkBandwidthOf": "单一CLB实例网络带宽上限为5G，可通过绑定多个CLB实例或改用NLB来提升带宽。但最多只能绑定20个\n        CLB/NLB 实例。", "apigw.components.EntranceSet.BindMigrateGatewayEntry": "绑定/迁移网关入口", "apigw.gateway.$id.PlugIn": "插件", "apigw.gateway.$id.BlackWhitelist": "黑/白名单", "apigw.gateway.$id.GlobalAuthentication": "全局认证", "apigw.gateway.$id.BusinessMonitoring": "业务监控", "apigw.gateway.$id.ResourceMonitoring": "资源监控", "apigw.gateway.$id.LogCenter": "日志中心", "apigw.gateway.$id.LinkTracking": "链路追踪", "apigw.gateway.$id.ParameterConfiguration": "参数配置", "apigw.EditForm.components.fc.TheCurrentAliasOrVersion": "当前别名或版本已添加", "apigw.components.EditForm.NotOpenYet": "暂未开放", "apigw.components.EditForm.FunctionComputeIsSupportedCurrently": "支持函数计算3.0，暂不支持函数计算2.0的服务来源", "apigw.components.EditForm.TheCurrentInstanceVersionIs": "当前实例版本较低，暂不支持此功能，请先", "apigw.components.EditForm.Upgrade": "升级", "apigw.components.EditForm.ToOrLater": "至2.0.8版本及以上。", "apigw.components.EditForm.TheInstanceUpgradeTakesAbout": "实例升级中，大约需要持续 5～10 分钟，请稍后创建 SAE Kubernetes 服务。", "apigw.src.sidebar.Consumers": "消费者", "apigw.components.api-operations-policy.PolicyPanel.Prompt": "提示", "apigw.components.api-operations-policy.PolicyPanel.AfterEnablingTheAuthenticationPolicy": "开启认证策略后，请前往消费者配置授权规则，否则将无权访问该API 。", "apigw.components.api-operations-policy.PolicyPanel.SeeDetails": "参见详情", "apigw.router.components.RouteLogs.NoteThatLogDeliveryIs": "请注意，日志投递的开启状态是网关实例级别的，也就是说只要任何一个路由打开了此功能，该网关内的其他路由都会共享此状态。", "apigw.detail.components.RouteLogs.YouHaveNotEnabledRouting": "您尚未开启路由日志投递功能", "apigw.detail.components.RouteLogs.OnlyWhenThisFeatureIs": "只有开启此功能，路由日志才会被投递到指定的日志服务 Project 中。", "apigw.detail.components.RouteLogs.NoteThatLogDeliveryIs": "请注意，日志投递的开启状态是网关实例级别的，也就是说只要任何一个路由打开了此功能，该网关内的其他路由都会共享此状态。", "apigw.components.shared.AkSkForm.EnterSecretKey": "请输入 Secret Key", "apigw.components.shared.ApiKeyForm.EnterQueryParameters": "请输入 Query 参数", "apigw.consumer-manage.consumer-basic-infor.Authentication.JsonWebTokenJwtIs": "JSON Web Token (JWT) 用于在客户端和服务端之间以JSON对象的形式安全地传输信息。该信息可以被验证和信任，因为JWT使用HMAC算法、RSA或ECDSA的公钥/私钥对其进行签名。JWT认证可以在网关中验证身份并控制授权访问。", "apigw.consumer-manage.consumer-basic-infor.Authentication.AkSkIsASignature": "一种基于HMAC算法的AK/SK签名认证方式。客户端在调用API时，需要使用签名密钥对请求内容进行签名计算，并将签名同步传输给服务器端进行签名验证。", "apigw.consumer-manage.consumer-basic-infor.Authentication.ApiKeyIsASimple": "API Key是一种简单的认证方式，客户端访问时，需将凭证以指定的方式添加至请求中，网关收到请求后会验证API Key的合法性及权限。API Key常用于不涉及敏感操作的简单场景，安全性相比JWT、AK/SK较低，请注意凭证的管理与保护。", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.WhetherToPassThrough": "是否透传", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.EnterQueryParameters": "请输入 Query 参数", "apigw.consumer-manage.consumer-basic-infor.ModifiedSuccessfully": "修改成功", "apigw.consumer-manage.consumer-basic-infor.FailedToModify": "修改失败", "apigw.consumer-manage.consumer-list.ConsumerTable.ConsumersAreTheCredentialsFor": "消费者是客户端访问API的凭证。启用消费者，需要在对应接口/路由中开启消费者认证，并创建消费者与接口/路由的授权关系。开启消费者认证后，只有被消费者授权的接口/路由，才可使用对应凭证访问。", "apigw.components.CreateConsumerSlide.SlideContent.SupportsLettersDigitsCaseInsensitive": "支持英文字母、数字、\"-\"、\".\"，大小写不敏感，以英文字母或数字开头及结尾，长度至少为2个字符，不超过64个字符", "apigw.components.shared.ApiKeyForm.QueryParameters": "Query 参数", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.NoOptionalActiveEnvironmentIs": "暂无可选的生效环境", "apigw.consumer-manage.consumer-basic-infor.RestKeyDialog.QueryParameters": "Query 参数", "apigw.components.domain-certificate.DeleteDomain.Routing": "路由", "apigw.consumer-manage.consumer-basic-infor.Authentication.AkSkHmac": "HMAC", "apigw.components.create-default-alert-rule.Refresh": "刷新", "apigw.policy.consumer.AkSkHmac": "HMAC", "apigw.$regionId.env-manage.TheFollowingFigureShowsHow": "环境二级域名使用图示说明", "apigw.$regionId.env-manage.Close": "关闭", "apigw.$regionId.env-manage.EnvironmentIsALogicalConcept": "环境是实例中的逻辑概念，用来实现API的环境管理与流量隔离，每个环境均提供独立的访问入口（二级域名）。在API发布时，可将API发布至指定环境，同时您需要将该API的业务域名通过DNS解析服务CNAME至环境二级域名，从而建立起访问通路。环境二级域名也可直接访问，直接访问每天有100次的访问限制，可用于测试，切勿直接生产使用。", "apigw.apiAi.detail.ConfirmToEnableAiRequest": "确定开启AI请求观测", "apigw.apiAi.detail.ConfirmToDisableAiRequest": "确定关闭AI请求观测", "apigw.apiAi.detail.EnabledSuccessfully": "开启成功", "apigw.apiAi.detail.ClosedSuccessfully": "关闭成功", "apigw.apiAi.detail.BasicInformation": "基本信息", "apigw.apiAi.detail.ApiNameId": "API名称/ID", "apigw.apiAi.detail.ModelProtocol": "模型协议", "apigw.apiAi.detail.EnvironmentNameId": "环境名称/ID", "apigw.apiAi.detail.InstanceNameId": "实例名称/ID", "apigw.apiAi.detail.DomainName": "域名", "apigw.apiAi.detail.Observation": "观测", "apigw.apiAi.detail.Enabled": "已开启", "apigw.apiAi.detail.NotEnabled": "未开启", "apigw.apiAi.detail.Close": "关闭", "apigw.apiAi.detail.Enable": "开启", "apigw.apiAi.detail.ALargeModelService": "大模型服务", "apigw.apiAi.detail.SelectModelService": "选择模型服务", "apigw.apiAi.detail.ServiceMatchingRules": "服务(匹配规则)", "apigw.apiAi.detail.ServicesProportion": "服务(比例)", "apigw.apiAi.logs.ApiLogs": "API日志", "apigw.apiAi.logs.TheCurrentFeatureIsNot": "当前功能暂不可用。您需要首先发布API到网关实例，然后才能查看日志。", "apigw.policiesPlugins.AiPolicyPlugin.AiAddPolicyPlugin.InstallPlugIns": "安装插件", "apigw.apiAi.share.NotOpenStatistics.ConfirmToEnableAiRequest": "确定开启AI请求观测", "apigw.apiAi.share.NotOpenStatistics.EnabledSuccessfully": "开启成功", "apigw.apiAi.share.NotOpenStatistics.ToUseThisFeatureClick": "为了使用此功能，请先点击", "apigw.apiAi.share.NotOpenStatistics.Enable": "启用", "apigw.apiAi.share.NotOpenStatistics.ToEnableAiRequestObservation": "以开启AI请求观测。", "apigw.apiAi.share.SnatCheck.CurrentlyYourGatewayHasNot": "目前您的网关尚未绑定SNAT服务，因此可能暂时无法访问公网。若您已配置VPC下公网访问方式，可忽略本提示。若您的网关实例无法访问公网，请您前往", "apigw.apiAi.share.SnatCheck.VpcConsole": "VPC控制台", "apigw.apiAi.share.SnatCheck.CreateANatGateway": "创建NAT网关。", "apigw.apiAi.userGuide.CurlLocationDomainVChat": "curl --location '{domain}/v1/chat/completions' \\\\\n     --header 'Content-Type: application/json' \\\\\n     --data '{\n        \"stream\": false,\n        \"model\": \"qwen-max\",\n        \"messages\": [\n            {\n                \"role\": \"system\",\n                \"content\": \"You are a helpful assistant.\"\n            },\n            {\n                \"role\": \"user\",\n                \"content\": \"你是谁？\"\n            }\n        ],\n        \"temperature\": 0.7,\n        \"top_p\": 1.0,\n        \"max_tokens\": 100,\n        \"presence_penalty\": 0,\n        \"frequency_penalty\": 0\n    }'", "apigw.apiAi.userGuide.DomainName": "域名", "apigw.apiAi.userGuide.Copy": "复制", "apigw.apiAi.userGuide.Example": "使用示例", "apigw.apiAi.userGuide.CopyCode": "复制代码", "apigw.api-manage.apiList.ApiListTable.CreateAiApi": "创建AI API", "apigw.api-manage.apiList.ApiListTableProps.DomainName": "域名", "apigw.api-manage.apiList.ApiListTableProps.ViewAll": "查看全部", "apigw.api-manage.apiList.ApiListTableProps.Agreement": "协议", "apigw.api-manage.apiList.ApiListTableProps.EnvironmentLevelDomainName": "环境二级域名", "apigw.api-manage.apiList.ApiListTableProps.ModelService": "模型服务", "apigw.api-manage.apiList.AllApis": "全部API", "apigw.createApiDialog.ai.TheNameIsUniqueAnd": "名称唯一，支持中文、英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.createApiDialog.ai.TheApiNameDoesNot": "API名称不符合规范", "apigw.components.createApiDialog.TheNameIsUniqueAnd": "名称唯一，支持中文、英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.components.createApiDialog.TheApiNameDoesNot": "API名称不符合规范", "apigw.api-manage.createApi.CreateApiTypes.ApiTypesOptimizedForAi": "针对AI网关优化的API类型，提供更友好的AI网关配置和调试能力，并预置AI代理、AI观测、消费者鉴权、内容安全防护等插件能力。", "apigw.CreateAiApiSidePanel.FallbackConfigs.ServiceNameFallbackServicesAre": "服务名称（Fallback服务按照排序降序执行）", "apigw.CreateAiApiSidePanel.FallbackConfigs.Add": "添加", "apigw.CreateAiApiSidePanel.ServiceConfigs.ApiKeys.Add": "添加", "apigw.CreateAiApiSidePanel.ServiceConfigs.ApiKeys.Display": "显示", "apigw.CreateAiApiSidePanel.ServiceConfigs.ApiKeys.Hide": "隐藏", "apigw.CreateAiApiSidePanel.ServiceConfigs.ApiKeys.Save": "保存", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.CannotBeEmpty": "不能为空", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.ThePrefixCannotBeSet": "前缀不可重复设置", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.PrefixIs": "前缀是", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.TheALargeModelService": "该大模型服务尚未配置凭证，请", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.ConfigureCredentials": "配置凭证", "apigw.CreateAiApiSidePanel.ServiceConfigs.ServiceName": "服务名称", "apigw.CreateAiApiSidePanel.ServiceConfigs.ModelNameMatchingRules": "模型名称匹配规则", "apigw.CreateAiApiSidePanel.ServiceConfigs.RequestRatio": "请求比例", "apigw.CreateAiApiSidePanel.ServiceConfigs.ModelName": "模型名称", "apigw.CreateAiApiSidePanel.ServiceConfigs.Add": "添加", "apigw.create-actions.CreateAiApiSidePanel.CreateAiApi": "创建AI API", "apigw.create-actions.CreateAiApiSidePanel.EditAiApi": "编辑AI API", "apigw.create-actions.CreateAiApiSidePanel.BasicInformation": "基本信息", "apigw.create-actions.CreateAiApiSidePanel.TheNameIsUniqueAnd": "名称唯一，支持中文、英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.create-actions.CreateAiApiSidePanel.TheApiNameDoesNot": "API名称不符合规范", "apigw.create-actions.CreateAiApiSidePanel.ModelProtocol": "模型协议", "apigw.create-actions.CreateAiApiSidePanel.TheModelProtocolCannotBe": "模型协议不能为空", "apigw.create-actions.CreateAiApiSidePanel.DomainName": "域名", "apigw.create-actions.CreateAiApiSidePanel.Environment": "所属环境", "apigw.create-actions.CreateAiApiSidePanel.TheEnvironmentToWhichIt": "所属环境不能为空", "apigw.create-actions.CreateAiApiSidePanel.SelectYourEnvironment": "请选择所属环境", "apigw.create-actions.CreateAiApiSidePanel.Instance": "所属实例", "apigw.create-actions.CreateAiApiSidePanel.Vpc": "所属VPC", "apigw.create-actions.CreateAiApiSidePanel.AiRequestObservation": "AI请求观测", "apigw.create-actions.CreateAiApiSidePanel.Enable": "开启", "apigw.create-actions.CreateAiApiSidePanel.Close": "关闭", "apigw.create-actions.CreateAiApiSidePanel.AfterEnablingYouCanView": "开启后可查看AI请求的Metrics、Logging、Tracing信息，Logging和Tracing依赖SLS日志投递服务", "apigw.create-actions.CreateAiApiSidePanel.PleaseGo": "，请前往", "apigw.create-actions.CreateAiApiSidePanel.EnableLogDelivery": "开启日志投递", "apigw.create-actions.CreateAiApiSidePanel.ALargeModelService": "大模型服务", "apigw.create-actions.CreateAiApiSidePanel.SelectModelService": "选择模型服务", "apigw.create-actions.CreateAiApiSidePanel.TheGatewayWillMatchThe": "网关将匹配请求Body中的模型名称前缀，将请求转发给对应的大模型服务", "apigw.create-actions.CreateAiApiSidePanel.ServiceList": "服务列表", "apigw.createApi.create-actions.CreateApiBasicForm.TheNameIsUniqueAnd.1": "名称唯一，支持中文、英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.createApi.create-actions.CreateApiBasicForm.TheApiNameDoesNot": "API名称不符合规范", "apigw.envAndBackendServices.backendServices.columns.IfTheRequestDoesNot": "当请求与任意匹配条件均不匹配时，将转发至默认服务", "apigw.elastic-expansion.components.elastic-event.ResourceName": "资源名称", "apigw.elastic-expansion.components.elastic-event.EventName": "事件名称", "apigw.elastic-expansion.components.elastic-event.EventLevel": "事件等级", "apigw.elastic-expansion.components.elastic-event.Time": "时间", "apigw.elastic-expansion.components.elastic-event.Status": "状态", "apigw.elastic-expansion.components.elastic-event.Initiator": "发起者", "apigw.elastic-expansion.components.elastic-event.Operation": "操作", "apigw.elastic-expansion.components.elastic-event.ViewDetails": "查看详情", "apigw.elastic-expansion.components.elastic-event.Time.1": "时间：", "apigw.elastic-expansion.components.elastic-event.EventSource": "事件来源：", "apigw.elastic-expansion.components.elastic-event.EventSummary": "事件摘要：", "apigw.elastic-expansion.components.elastic-event.Details": "详细内容：", "apigw.elastic-expansion.components.elastic-form.TotalTargetNodesUnit": "目标总节点（单位：Unit）", "apigw.elastic-expansion.components.elastic-form.ExpansionStep": "扩容步长", "apigw.elastic-expansion.components.elastic-form.ElasticExpansionRange": "弹性扩容范围", "apigw.elastic-expansion.components.elastic-form.TheElasticScaleOutRange": "弹性扩容范围含当前实例规格已有的 Unit 数", "apigw.elastic-expansion.components.elastic-form.ForMoreInformationAboutThe": "当前弹性节点性能，请查看", "apigw.elastic-expansion.components.elastic-form.PerformanceDescription": "性能说明", "apigw.elastic-expansion.components.elastic-form.SingleUnitPerformanceIndicatorEstimation": "单个Unit性能指标预估", "apigw.elastic-expansion.components.elastic-form.MaximumNumberOfClientConnections": "最大客户端连接数：24000", "apigw.elastic-expansion.components.elastic-form.MaximumHttpsConnectionsPerSecond": "最大HTTPS每秒新建连接数：800", "apigw.elastic-expansion.components.elastic-form.MaximumShortConnectionQps": "最大短连接QPS：3400", "apigw.elastic-expansion.components.elastic-form.MaximumLongConnectionQps": "最大长连接QPS：4400", "apigw.elastic-expansion.components.elastic-form.EstimatedOverallPerformanceIndexRange": "弹性扩容后整体性能指标范围预估", "apigw.elastic-expansion.components.elastic-form.MaximumNumberOfClientConnections.1": "最大客户端连接数：", "apigw.elastic-expansion.components.elastic-form.MaximumHttpsConnectionsPerSecond.1": "最大HTTPS每秒新建连接数：", "apigw.elastic-expansion.components.elastic-form.MaximumShortConnectionQps.1": "最大短连接QPS：", "apigw.elastic-expansion.components.elastic-form.MaximumLongConnectionQps.1": "最大长连接QPS：", "apigw.elastic-expansion.components.elastic-form.ForMorePerformanceMetricsSee": "。 更多性能指标，请查看", "apigw.elastic-expansion.components.elastic-form.CapacityDescription": "容量说明", "apigw.elastic-expansion.components.elastic-form.AutomaticScalingTakesEffect": "自动扩缩容生效", "apigw.elastic-expansion.components.elastic-form.Enable": "开启", "apigw.elastic-expansion.components.elastic-form.Close": "关闭", "apigw.elastic-expansion.components.elastic-form.ExpansionMethod": "扩缩容方式", "apigw.elastic-expansion.components.elastic-form.SelectAScalingMethod": "请选择扩缩容方式", "apigw.elastic-expansion.components.elastic-form.ScaleInByTime": "按时间扩缩容", "apigw.elastic-expansion.components.elastic-form.ForExampleECommerceAnd": "如电商等突发大流量场景，建议使用按时间扩缩容+限流。", "apigw.elastic-expansion.components.elastic-form.MaximumNumberOfNodesAllowed": "允许最大节点数（单位：Unit）", "apigw.elastic-expansion.components.elastic-form.ExpansionAndContractionWaterLevel": "扩缩容水位", "apigw.elastic-expansion.components.elastic-form.SafeWaterLevel": "安全水位", "apigw.elastic-expansion.components.elastic-form.WhenTheBurstTrafficIncreases": "面临突发流量增长至当前流量两倍的情况下，仍能确保系统的高吞吐量和低延迟表现。", "apigw.elastic-expansion.components.elastic-form.LearnMore": "了解详情", "apigw.elastic-expansion.components.elastic-form.ExpansionAndContractionByWater": "按水位扩缩容", "apigw.elastic-expansion.components.elastic-form.WhenTheAlertLevelIs": "达到警戒水位时，网关的延迟可能会增加。", "apigw.elastic-expansion.components.elastic-form.TimePeriodConfiguration": "时间段配置", "apigw.elastic-expansion.components.elastic-form.IfTheStartTimeOf": "若时间段的起始时间早于当前时间，则该时间段的弹性策略将在下个周期生效", "apigw.elastic-expansion.components.elastic-form.SelectATimePeriod": "请选择时间段", "apigw.elastic-expansion.components.elastic-form.PleaseFillInTheComplete": "请填写完整信息", "apigw.elastic-expansion.components.elastic-form.TheTimePeriodIsRepeated": "时间段重复", "apigw.elastic-expansion.components.elastic-form.TheIntervalMustBeNo": "时间段间隔须不低于2小时", "apigw.elastic-expansion.components.elastic-form.TargetTotalNodeScaleOut": "（目标总节点/扩容步长）必须为整数，请检查", "apigw.elastic-expansion.components.time-period.TimePeriodBeijingTimeZone": "时间段（北京时区）", "apigw.elastic-expansion.components.time-period.TotalTargetNodesUnit": "目标总节点（单位：Unit）", "apigw.elastic-expansion.components.time-period.AddTimePeriod": "添加时间段", "apigw.elastic-expansion.components.time-period.YouCanAddUpTo": "最多可添加3个时间段，单条时间段最高节点为", "apigw.components.elastic-expansion.New": "新建成功", "apigw.components.elastic-expansion.ChangedSuccessfully": "变更成功", "apigw.components.elastic-expansion.ClosedSuccessfully": "关闭成功", "apigw.components.elastic-expansion.Save": "保存", "apigw.components.elastic-expansion.Cancel": "取消", "apigw.components.elastic-expansion.DuringTheAutomaticExpansionPeriod": "自动扩容生效期间，系统将对扩容出来的节点按节点数和使用时长进行按量计费。", "apigw.components.elastic-expansion.ViewDetails": "查看详情", "apigw.components.elastic-expansion.ScalingConfiguration": "扩缩容配置", "apigw.components.elastic-expansion.ScalingEvent": "扩缩容事件", "apigw.components.gateway.GatewayListTable.AutomaticScalingIsEnabledFor": "当前实例开启了自动扩容，请先关闭自动扩容再操作变配。", "apigw.shared.CodeMirrorEditor.BasicCodeMirror.CopyCode": "复制代码", "apigw.shared.CustomDragged.Add": "添加", "apigw.src.constants.apiManage.ByModelNameInThe": "按请求Body中的模型名称", "apigw.src.constants.apiManage.Proportional": "按比例", "apigw.src.constants.Information": "信息", "apigw.src.constants.Warning": "警告", "apigw.src.constants.Serious": "严重", "apigw.src.constants.PassThrough": "透传", "apigw.src.constants.AlibabaCloudBailian": "阿里云百炼", "apigw.src.constants.DarkSideOfTheMoon": "月之暗面", "apigw.src.constants.BaichuanIntelligence": "百川智能", "apigw.src.constants.ZeroOneEverything": "零一万物", "apigw.src.constants.IntelligentSpectrumAi": "智谱AI", "apigw.src.constants.Brain": "360智脑", "apigw.src.constants.MixedElement": "混元", "apigw.src.constants.StepStar": "阶跃星辰", "apigw.src.constants.Spark": "星火", "apigw.api-ai.$apiId.NotPublished": "未发布", "apigw.api-ai.$apiId.Publishing": "发布中", "apigw.api-ai.$apiId.Published": "已发布", "apigw.api-ai.$apiId.PublishedButModified": "已发布但有修改", "apigw.api-ai.$apiId.PublishingFailed": "发布失败", "apigw.api-ai.$apiId.AiApiPublishingFailedPlease": "AI API发布失败，请再次重新发布。", "apigw.api-ai.$apiId.Offline": "下线中", "apigw.api-ai.$apiId.OfflineFailed": "下线失败", "apigw.api-ai.$apiId.AiApiFailedToBe": "AI API下线失败，请再次删除重试。", "apigw.api-ai.$apiId.EnvironmentException": "环境异常", "apigw.api-ai.$apiId.Publish": "发布", "apigw.api-ai.$apiId.Edit": "编辑", "apigw.api-ai.$apiId.ApiDetails": "API详情", "apigw.components.BasicInfo.AutomaticExpansion": "自动扩容", "apigw.components.BasicInfo.Enable": "开启", "apigw.components.BasicInfo.ChangeConfiguration": "更改配置", "apigw.components.BasicInfo.NotEnabled": "未开启", "apigw.components.AiModel.ApiKeys.TheCredentialCannotBeEmpty": "凭证不能为空", "apigw.components.AiModel.ApiKeys.Add": "添加", "apigw.components.AiModel.ApiKeys.Hide": "隐藏", "apigw.components.AiModel.ApiKeys.Display": "显示", "apigw.AiModel.MapConfig.SourceModel": "源模型", "apigw.AiModel.MapConfig.TargetModel": "目标模型", "apigw.AiModel.MapConfig.Add": "添加", "apigw.components.AiModel.TheNameIsUniqueAnd": "名称唯一，支持小写字母、数字和短横线（-），以英文字母开头，不超过64个字符", "apigw.components.AiModel.ALargeModelSuppliers": "大模型供应商", "apigw.components.AiModel.ALargeModelSupplierCannot": "大模型供应商不能为空", "apigw.components.AiModel.Agreement": "协议", "apigw.components.AiModel.TheProtocolCannotBeEmpty": "协议不能为空", "apigw.components.AiModel.ServiceAddress": "服务地址", "apigw.components.AiModel.TheServiceAddressCannotBe": "服务地址不能为空", "apigw.components.AiModel.EnterAServiceAddress": "请输入服务地址", "apigw.components.AiModel.ALargeModelConfiguration": "大模型配置", "apigw.components.AiModel.Credential": "凭证", "apigw.components.AiModel.ThisCredentialIsUsedTo": "该凭证用于在AI网关与大模型服务之间进行身份验证。一旦配置好凭证，AI网关将随机选择一个凭证与大模型服务建立通信。", "apigw.components.AiModel.TheCredentialCannotBeEmpty": "凭证不能为空", "apigw.components.AiModel.HealthCheck": "健康检查", "apigw.components.EditForm.AiServices": "AI服务", "apigw.components.EditForm.SaeApplicationsNeedToBe": "SAE应用需要开启", "apigw.components.EditForm.KSServiceRegistrationDiscovery": "K8s Service服务注册发现", "apigw.apimanage.createaidialog.apiprecheck.item.unit": "项", "apigw.apiManage.interfaceList.components.apiOperationSlide.apiBasicInfo.parameterDefineTip": "请在请求定义Parameter Path中定义 {param} 参数。", "apigw.apiManage.interfaceList.components.apiOperationSlide.apiBasicInfo.pathValidateTip": "接口Path中需以 []、{}、:xx 的方式包含Parameter Path中 {param} 参数。", "apigw.components.pluginManage.gatewayPluginPanel.ruleLevel": "级别规则", "apigw.components.shared.consumerTransfer.transferList.selectedCount": "选中{count}项（当前页）", "apigw.components.api-operations-debug.OperationDebug.CopyUrl": "复制URL", "apigw.components.api-operations-debug.OperationDebug.CopyCurlCommand": "复制Curl命令", "apigw.components.LeftIcon.CopyUrl": "复制URL", "apigw.components.LeftIcon.CopyCurlCommand": "复制Curl命令", "apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.AuthorizationRemovedSuccessfully": "解除授权成功", "apigw.components.overview.ApiTypes.RoutingAsTheCore": "以路由为核心", "apigw.components.overview.ApiTypes.QuickInterSystemConnection": "系统间快速打通", "apigw.components.overview.ApiTypes.NoInterfaceLevelControlIs": "无需进行接口级别的管控", "apigw.components.overview.ApiTypes.KSIngressCompliant": "符合K8s Ingress 标准", "apigw.components.overview.ApiTypes.MeetsNginxIngress": "符合<PERSON><PERSON><PERSON>ress", "apigw.components.overview.ApiTypes.PowerfulTrafficControlAndPerformance": "强大的流量管控能力与性能", "apigw.components.overview.ApiTypes.ForApiFirstCollaborationAnd": "面向API first协作、开放场景", "apigw.components.overview.ApiTypes.UnifiedStandardReusableAndScalable": "统一标准、可复用、可拓展", "apigw.components.overview.ApiTypes.TrafficManagementInFineGrained": "细粒度接口维度的流量管理", "apigw.components.overview.ApiTypes.LongLinkTwoWayReal": "长链接，双向实时通信", "apigw.components.overview.ApiTypes.EfficientDataTransmissionWithLow": "数据高效传输，低延迟", "apigw.components.overview.ApiTypes.ApplicableToFinancialScenariosSuch": "适用AI、IM、IoT等金融等场景", "apigw.plugin-manage.$id.Installation": "安装", "apigw.plugin-manage.$id.ReleaseVersion": "发布版本", "apigw.apiName.publish.domain.has.already.been.published.in.the.environment1": "域名 {domainName} 在当前API中已发布在环境 {envName} 所属实例 {name} / {gatewayId} 中，当前API不可再使用该域名发布到实例 {name}的其他环境中。", "apigw.apiAi.AIModelDebug.AIDialogs.User": "用户", "apigw.apiAi.AIModelDebug.AIDialogs.Model": "模型", "apigw.apiAi.AIModelDebug.AIDialogs.ServiceResponseError": "服务响应错误", "apigw.apiAi.AIModelDebug.AIDialogs.WordCount": "字数：", "apigw.apiAi.AIModelDebug.AIDialogs.EnterTokens": "| 输入tokens：", "apigw.apiAi.AIModelDebug.AIDialogs.OutputTokens": "| 输出tokens：", "apigw.apiAi.AIModelDebug.AIDialogs.TheFollowingIsANew": "以下为新对话", "apigw.apiAi.AIModelDebug.Curl.WhoAreYou": "你是谁？", "apigw.apiAi.AIModelDebug.Curl.CopyCurl": "复制curl", "apigw.apiAi.AIModelDebug.Curl.RefreshCurl": "刷新curl", "apigw.apiAi.AIModelDebug.Output.Copy": "复制", "apigw.apiAi.AIModelDebug.ParamsSetting.TheSystemOwnerTellsIt": "系统人设，告诉它在生成响应时应该如何处理以及需要参考的上下文。你可以描述助手的个性，告诉它应该回答什么和不应该回答什么，并告诉它按什么格式响应。注意：此部分将包含在每次\n              API 调用中。", "apigw.apiAi.AIModelDebug.ParamsSetting.PleaseEnterTheRolePrompt": "请输入角色 Prompt提示词", "apigw.apiAi.AIModelDebug.ParamsSetting.ItHasAGreatImpact": "对结果影响较大，请谨慎调整", "apigw.apiAi.AIModelDebug.Request.DomainNameSelection": "域名选择", "apigw.apiAi.AIModelDebug.Request.ModelSelection": "模型选择", "apigw.apiAi.AIModelDebug.Request.SelectOrEnterAModel": "请选择或填写模型名称", "apigw.apiAi.AIModelDebug.Request.StreamingRequest": "流式请求", "apigw.apiAi.AIModelDebug.Request.ParameterSettings": "参数设置", "apigw.apiAi.AIModelDebug.Request.MaxTokensControlsTheMaximum": "max_tokens：控制生成文本的最大长度。值越大，文本越长。", "apigw.apiAi.AIModelDebug.Request.TopPControlsTheDiversity": "top_p：控制生成文本的多样性。值越小，内容越集中；值越大，内容越多样。", "apigw.apiAi.AIModelDebug.Request.TemperatureControlsTheRandomnessOf": "temperature：控制生成文本的随机性。值越小，内容越确定；值越大，内容越随机。", "apigw.apiAi.AIModelDebug.Request.Reset": "重置", "apigw.apiAi.AIModelDebug.Request.CustomParameters": "自定义参数", "apigw.apiAi.AIModelDebug.Response.ModelReturn": "模型返回", "apigw.apiAi.AIModelDebug.Response.CurlCommand": "CURL命令", "apigw.apiAi.AIModelDebug.Response.RawOutput": "原始输出", "apigw.apiAi.AIModelDebug.Response.InTheRequest": "请求中...", "apigw.apiAi.AIModelDebug.Response.UseEnterToSendUse": "使用回车发送，使用command+回车/ctrl+回车换行", "apigw.apiAi.AIModelDebug.Response.DoItAgain": "再来一次", "apigw.apiAi.AIModelDebug.Response.ClearMemory": "清除记忆", "apigw.apiAi.AIModelDebug.Response.Send": "发送", "apigw.apiAi.AIModelDebug.Debugging": "调试", "apigw.apiAi.AIModelDebug.Close": "关闭", "apigw.apiAi.detail.ResourceGroupNameId": "资源组名称/ID", "apigw.apiAi.detail.ServiceModel": "服务模型", "apigw.apiAi.detail.ServiceList": "服务列表", "apigw.apiAi.detail.FallbackService": "Fallback服务", "apigw.apiAi.detail.FallbackDescendingExecution": "fallback降序执行", "apigw.api-manage.apiList.ApiListTableProps.EnvironmentLevelDomainNameFor": "环境二级域名 (仅供调试)", "apigw.api-manage.apiList.ApiListTableProps.TheEnvironmentLevelDomainName": "环境二级域名仅供调试使用，若多个AI API共用同一个环境二级域名，则无法保证AI\n              API访问正常。请使用自定义域名。", "apigw.api-manage.apiList.ApiListTableProps.Debugging": "调试", "apigw.api-manage.components.apiBasic.ResourceGroupNameId": "资源组名称/ID", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.UseGlobSyntaxToMatch": "使用 Glob 语法匹配模型, 例如：model-*", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.ModelNameMatchingRulesCannot": "模型名称匹配规则不可重复设置", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.TheALargeModelService.1": "该大模型服务尚未配置API-KEY，请", "apigw.CreateAiApiSidePanel.ServiceConfigs.Item.ConfigureApiKey": "配置API-KEY", "apigw.CreateAiApiSidePanel.ServiceConfigs.LearnAboutGlobSyntaxRules": "了解 Glob 语法规则", "apigw.CreateAiApiSidePanel.ServiceConfigs.GlobSyntaxRules": "Glob 语法规则", "apigw.CreateAiApiSidePanel.ServiceConfigs.GlobSyntaxAllowsYouTo": "Glob语法允许使用通配符来匹配来进行灵活的模式匹配。此处支持两种最常用的通配符：*（星号）和?（问号）", "apigw.CreateAiApiSidePanel.ServiceConfigs.AsteriskRepresentsZeroOrMore": "* (星号) - 代表零个或多个任意字符。 例如qwen-*\n                    可以用来匹配所有以qwen-开头的模型名称，如qwen-plus、qwen-turbo等。", "apigw.CreateAiApiSidePanel.ServiceConfigs.QuestionMarkIndicatesAnArbitrary": "? (问号) - 代表恰好一个任意字符。 例如，gpt-? 可以用来匹配\n                    gpt-3、gpt-4，但不能匹配gpt-4o。", "apigw.CreateAiApiSidePanel.components.ServiceSelect.YouMustFirstSelectThe": "需要先选择所属环境", "apigw.create-actions.CreateAiApiSidePanel.Instance.1": "所属实例：", "apigw.create-actions.CreateAiApiSidePanel.WhenYouEnableTheAi": "当您启用AI请求观测功能时，系统网关将自动记录所有AI请求的内容（包括请求体body）至访问日志中。请您务必确保所使用的SLS（日志服务）配置恰当，并采取必要的安全措施以保护日志内容的安全性，防止任何潜在的信息泄露或其他形式的信息安全风险。", "apigw.create-actions.CreateAiApiSidePanel.ServiceModel": "服务模型", "apigw.create-actions.CreateAiApiSidePanel.ThisFeatureRequiresUpgradingThe": "该功能需要升级网关实例版本到最新版本。", "apigw.create-actions.CreateAiApiSidePanel.FallbackList": "Fallback列表", "apigw.createApi.create-actions.CreateIngressApiSidePanel.Environment": "所属环境", "apigw.createApi.create-actions.CreateIngressApiSidePanel.Instance": "所属实例", "apigw.createApi.create-actions.CreateIngressApiSidePanel.Vpc": "所属VPC", "apigw.createApi.create-actions.CreateRouterApiSidePanel.EditHttpApi": "编辑HTTP API", "apigw.createApi.create-actions.CreateSourceDialog.Instance": "所属实例", "apigw.createApi.create-actions.CreateSourceDialog.Vpc": "所属VPC", "apigw.components.envAndBackendServices.envSelect.Instance.1": "所属实例：", "apigw.components.envAndBackendServices.envSelect.NoOption": "无选项", "apigw.components.envAndBackendServices.Environment": "所属环境", "apigw.components.envAndBackendServices.Instance": "所属实例", "apigw.components.envAndBackendServices.Vpc": "所属VPC", "apigw.components.envFilter.Instance": "所属实例：", "apigw.detail.components.RouteOverview.ResourceGroupNameId": "资源组名称/ID", "apigw.components.create-edit-route.Prompt": "提示", "apigw.components.create-edit-route.FcServiceDoesNotSupport": "FC服务暂不支持Fallback策略，请先关闭Fallback策略后重试", "apigw.create-service.SourceStep.AiServices": "AI服务", "apigw.create-service.SourceStep.AiServices.1": "AI 服务", "apigw.components.domain-certificate.DomainInfoSlide.ResourceGroupNameId": "资源组名称/ID", "apigw.components.domain-certificate.DomainSlide.EncryptionAlgorithmSuite": "加密算法套件", "apigw.components.domain-certificate.DomainSlide.Default": "默认", "apigw.components.domain-certificate.DomainSlide.Custom": "自定义", "apigw.components.domain-certificate.DomainSlide.TheSpecifiedEncryptionSuiteIs": "指定加密套件只会在协商结果为TLS 1.0-1.2时有效，协商结果为TLS1.3时不生效", "apigw.components.domain-certificate.DomainSlide.SelectAnEncryptionSuite": "请选择加密套件", "apigw.components.domain-certificate.EncryptionSuiteTransfer.SupportedVersions": "(支持版本：", "apigw.components.domain-certificate.EncryptionSuiteTransfer.EnterAnEncryptionAlgorithmName": "请输入加密算法名称进行搜索", "apigw.components.domain-certificate.EncryptionSuiteTransfer.OptionalAlgorithm": "可选算法", "apigw.components.domain-certificate.EncryptionSuiteTransfer.SelectedAlgorithmDragOptionsCan": "已选算法（拖动选项可排序）", "apigw.domain-certificate.details.ForceHttps": "强制HTTPS", "apigw.domain-certificate.details.ResourceGroupNameId": "资源组名称/ID", "apigw.domain-certificate.details.AdvancedOptions": "高级选项", "apigw.domain-certificate.details.HttpProtocol": "HTTP/2协议", "apigw.domain-certificate.details.Close": "关闭", "apigw.domain-certificate.details.Enable": "开启", "apigw.domain-certificate.details.TlsVersion": "TLS版本", "apigw.domain-certificate.details.EncryptionAlgorithmSuite": "加密算法套件", "apigw.domain-certificate.details.Custom": "自定义", "apigw.domain-certificate.details.Default": "默认", "apigw.domain-certificate.plugin.InstanceNameId": "实例名称/ID", "apigw.domain-certificate.plugin.TheFollowingListIsArranged": "以下列表按插件执行顺序排列，越靠上的插件越优先执行。", "apigw.components.env-manage.EnvManageTableProps.DefaultEnvironment": "默认环境", "apigw.components.gateway.GatewayListColumns.Label": "标签", "apigw.components.gateway.GatewayListTable.EnableWafProtection": "开启WAF防护", "apigw.components.gateway.GatewayListTable.ThereAreNoInstancesIn": "当前地域或资源组下暂无实例，请切换到有实例的地域或者尝试选择账号全部资源", "apigw.components.gateway.WafDialog.Close": "关闭", "apigw.components.gateway.WafDialog.YouHaveNotActivatedWaf": "您未开通WAF，请前往", "apigw.components.gateway.WafDialog.AlibabaCloudWafConsole": "阿里云WAF控制台", "apigw.components.gateway.WafDialog.Enable": "进行开通。", "apigw.gateway.gateway-action.BatchTagEditorAction.EditTagsInBatches": "批量编辑标签", "apigw.gateway.gateway-action.BatchUntagEditorAction.BulkUnbindTags": "批量解绑标签", "apigw.components.plugin-manage.APIPluginConfigBasic.DomainLevelPlugInRules": "域名级插件规则", "apigw.components.plugin-manage.APIPluginConfigBasic.InstanceLevelPlugInRules": "实例级插件规则", "apigw.components.plugin-manage.APIPluginConfigBasic.RoutingPlugInRules": "路由级插件规则", "apigw.components.plugin-manage.APIPluginConfigBasic.ApiLevelPlugInRules": "API级插件规则", "apigw.components.plugin-manage.APIPluginConfigBasic.InterfaceLevelPlugInRules": "接口级插件规则", "apigw.components.plugin-manage.GatewayWithPlugin.TheFollowingListIsArranged": "以下列表按插件执行顺序排列，越靠上的插件越优先执行。", "apigw.components.plugin-manage.PluginTableProps.Configured": "已配置", "apigw.components.plugin-manage.PluginTableProps.NotConfigured": "未配置", "apigw.components.plugin-manage.PluginTableProps.DomainLevelRules": "域名级规则", "apigw.components.plugin-manage.PluginTableProps.TheListDisplaysAllPlug": "列表中展示所选实例上安装的全部插件。域名级规则「已配置」代表该插件已在域名级别配置了一条插件规则。", "apigw.components.plugin-manage.PluginTableProps.Enabled": "已启用", "apigw.components.plugin-manage.PluginTableProps.NotEnabled": "未启用", "apigw.components.plugin-manage.PluginTableProps.EnableStatus": "启用状态", "apigw.plugin-manage.plugin-rule-config.PluginApiSelect.Api": "所属API", "apigw.plugin-manage.plugin-rule-config.PluginApiSelect.SelectTheApiToWhich": "请选择所属API", "apigw.plugin-manage.plugin-rule-config.PluginApiSelect.ApiVersion": "API 版本", "apigw.plugin-manage.plugin-rule-config.PluginAttachExtendName.ViewAll": "查看全部", "apigw.plugin-manage.plugin-rule-config.PluginAttachExtendName.All": "全部", "apigw.plugin-manage.plugin-rule-config.PluginAttachExtendName.PleaseEnterContent": "请输入内容", "apigw.plugin-manage.plugin-rule-config.PluginAttachTableProps.Api": "所属 API", "apigw.plugin-manage.plugin-rule-config.PluginAttachTableProps.EnvironmentNameId": "所属环境名称/ID", "apigw.plugin-manage.plugin-rule-config.PluginEnvSelect.Environment": "所属环境", "apigw.plugin-manage.plugin-rule-config.RuleScopeMenu.Strip": "条", "apigw.plugin-manage.plugin-rule-config.RuleScopeMenu.TheEffectivePriorityDecreasesFrom": "生效优先级自接口/路由级向下递减", "apigw.plugin-manage.plugin-transfer.ApiTransfer.InitializeVersion": "初始化版本", "apigw.plugin-manage.plugin-transfer.ApiTransfer.TheApiAlreadyHasA": "该API已存在规则配置", "apigw.plugin-manage.plugin-transfer.ApiTransfer.EnterAnApiName": "请输入API名称", "apigw.plugin-manage.plugin-transfer.ApiTransfer.OptionalApi": "可选API", "apigw.plugin-manage.plugin-transfer.ApiTransfer.SelectedApi": "已选API", "apigw.plugin-manage.plugin-transfer.OperationAndRouteTransfer.OptionalInterfaceRoute": "可选接口/路由", "apigw.plugin-manage.plugin-transfer.OperationAndRouteTransfer.SelectedInterfaceRoute": "已选接口/路由", "apigw.policy.fallback.WhenARouteIsPublished": "路由发布时，若后端服务为 FC 函数，则fallback策略将会失效", "apigw.policy.fallback.IfTheBackendServiceIs": "API发布时，若后端服务为 FC 函数，则fallback策略将会失效", "apigw.shared.ErrorFallback.SorrySomeErrorsHaveOccurred": "抱歉，发生了一些错误。请", "apigw.shared.ErrorFallback.Refresh": "刷新", "apigw.shared.ErrorFallback.PageOrRetry": "页面或重试。", "apigw.shared.PluginCardList.PluginConfig.InterfaceRoute": "接口/路由", "apigw.shared.PluginCardList.PluginConfig.InterfaceRouteLevel": "接口/路由级别", "apigw.shared.PluginCardList.PluginConfig.ApiLevel": "API级别", "apigw.shared.PluginCardList.PluginConfig.InitializeVersion": "初始化版本", "apigw.shared.PluginCardList.configHook.InterfaceRoutingPlugInRules": "接口/路由级插件规则", "apigw.shared.PluginCardList.configHook.ApiLevelPlugInRules": "REST API级插件规则", "apigw.shared.PluginCardList.configHook.TheRuleTakesEffectWhen": "作用在REST API上；当请求匹配到指定REST API的任意接口时，规则生效。", "apigw.shared.ResourceGroupInfo.DefaultResourceGroup": "默认资源组", "apigw.shared.ResourceGroupSelect.DefaultResourceGroup": "默认资源组", "apigw.shared.ResourceGroupSelect.ResourceGroupNameId": "资源组名称/ID", "apigw.shared.ResourceGroupSelect.ResourceGroup": "资源组", "apigw.shared.ResourceGroupSelect.SelectAResourceGroup": "请选择资源组", "apigw.shared.ResourceGroupSelect.CreateAResourceGroup": "创建资源组", "apigw.shared.ResourceGroupSelect.AdvancedConfiguration": "高级配置", "apigw.src.constants.apiManage.SingleModelService": "单模型服务", "apigw.src.constants.apiManage.MultiModelServiceByModel": "多模型服务（按模型名称）", "apigw.src.constants.apiManage.MultiModelServicesProportional": "多模型服务（按比例）", "apigw.src.constants.TongyiQianwen": "阿里云百炼", "apigw.src.constants.BeanBag": "豆包", "apigw.$apiId.router.ResourceGroupNameId": "资源组名称/ID", "apigw.$regionId.architecture.FullLinkArchitectureDiagram": "全链路架构图", "apigw.domain-certificate.$domainId.Details": "详情", "apigw.domain-certificate.$domainId.PlugIn": "插件", "apigw.components.BasicInfo.ResourceGroupNameId": "资源组名称/ID", "apigw.components.BasicInfo.AutomaticScalingIsEnabledFor": "当前实例开启了自动扩容，请先关闭自动扩容再操作。", "apigw.components.EntranceSet.AutomaticScalingIsEnabledFor": "当前实例开启了自动扩容，请先关闭自动扩容再操作。", "apigw.components.EngineParams.AutomaticScalingIsEnabledFor": "当前实例开启了自动扩容，请先关闭自动扩容再操作。", "apigw.components.AiModel.ApiKeys.IfYouUseAlibabaCloud": "如果使用阿里云百炼，请在阿里云百炼", "apigw.components.AiModel.ApiKeys.ApiKey": "API-KEY 获取", "apigw.components.AiModel.ApiKeys.ForOtherALargeModel": "。其他大模型供应商，请咨询对应的服务供应商。", "apigw.components.AiModel.NoteThatTheLargeModel": "请注意，本AI服务所提供的大模型能力并非由云原生API网关直接提供。在使用前，请您自行评估该服务的适用性和可靠性，并确保您的使用行为完全符合相关法律法规及产品使用协议的要求。对于因违反上述规定而产生的一切后果，我们将不承担任何责任。", "apigw.components.AiModel.ServiceAddressBaseUrl": "服务地址（base_url）", "apigw.components.AiModel.ThisApiKeyIsUsed": "该API-KEY用于在AI网关与大模型服务之间进行身份验证。一旦配置好API-KEY，AI网关将随机选择一个API-KEY与大模型服务建立通信。", "apigw.components.AiModel.ApiKeyCannotBeEmpty": "API-KEY不能为空", "apigw.src.sidebar.ArchitectureDiagram": "架构图", "apigw.apiAi.policiesPlugins.PresetPoliciesAndPlugIns": "预置策略与插件", "apigw.policiesPlugins.policy.RedisConfig.RedisServiceAddress": "Redis服务地址", "apigw.policiesPlugins.policy.RedisConfig.EnterTheRedisServiceAddress": "请输入Redis服务地址", "apigw.policiesPlugins.policy.RedisConfig.PortNumber": "端口号（Port）", "apigw.policiesPlugins.policy.RedisConfig.EnterAPortNumber": "请输入端口号", "apigw.policiesPlugins.policy.RedisConfig.AccessMethod": "访问方式", "apigw.policiesPlugins.policy.RedisConfig.AccountPasswordLogin": "账号+密码登录", "apigw.policiesPlugins.policy.RedisConfig.PasswordLogon": "密码登录", "apigw.policiesPlugins.policy.RedisConfig.PasswordFreeLogin": "免密登录", "apigw.policiesPlugins.policy.RedisConfig.DatabaseAccount": "数据库账号", "apigw.policiesPlugins.policy.RedisConfig.EnterADatabaseAccount": "请输入数据库账号", "apigw.policiesPlugins.policy.RedisConfig.DatabasePassword": "数据库密码", "apigw.policiesPlugins.policy.RedisConfig.EnterADatabasePassword": "请输入数据库密码", "apigw.policiesPlugins.policy.RedisConfig.YouNeedToGo": "您需要前往", "apigw.policiesPlugins.policy.RedisConfig.RedisConsole": "redis控制台", "apigw.policiesPlugins.policy.RedisConfig.AddAWhitelistInThe": "添加白名单。在redis控制台中，您需要将网关实例", "apigw.policiesPlugins.policy.RedisConfig.VpcSegment": "的VPC网段", "apigw.policiesPlugins.policy.RedisConfig.AddToTheWhitelist": "添加到白名单。", "apigw.policy.cache-policy.PolicyContent.CacheDurationSeconds": "缓存时长（秒)", "apigw.policy.cache-policy.PolicyContent.PleaseEnterTheCacheDuration": "请输入缓存时长", "apigw.policy.cache-policy.Cache": "缓存", "apigw.policy.rate-limit-policy.PolicyContent.ThrottlingPolicy": "限流策略", "apigw.policy.rate-limit-policy.ReteLimitRules.ByRequestHeader": "按请求header", "apigw.policy.rate-limit-policy.ReteLimitRules.QueryParametersByRequest": "按请求query参数", "apigw.policy.rate-limit-policy.ReteLimitRules.CookieByRequest": "按请求cookie", "apigw.policy.rate-limit-policy.ReteLimitRules.ByClientIp": "按客户端IP", "apigw.policy.rate-limit-policy.ReteLimitRules.EnterAParameterName": "请输入参数名称", "apigw.policy.rate-limit-policy.ReteLimitRules.ExactMatch": "精确匹配", "apigw.policy.rate-limit-policy.ReteLimitRules.PrefixMatching": "前缀匹配", "apigw.policy.rate-limit-policy.ReteLimitRules.RegularMatching": "正则匹配", "apigw.policy.rate-limit-policy.ReteLimitRules.ArbitraryMatch": "任意匹配", "apigw.policy.rate-limit-policy.ReteLimitRules.PleaseEnterMatchingContent": "请输入匹配内容", "apigw.policy.rate-limit-policy.ReteLimitRules.EnterAnIpAddressFor": "请输入IP地址（如：***********）或IP段（如：***********/24）", "apigw.policy.rate-limit-policy.ReteLimitRules.EnterInTheFollowingFormat": "请按以下格式输入：", "apigw.policy.rate-limit-policy.ReteLimitRules.SingleIpAddressForExample": "单一IP地址：如 ***********", "apigw.policy.rate-limit-policy.ReteLimitRules.IpSegmentCidrNotationFor": "IP段（CIDR表示法）：如 ***********/24", "apigw.policy.rate-limit-policy.ReteLimitRules.NumberInTheIpSegment": "IP段格式中的 “/数字” 表示网络前缀，例如 ***********/24 代表该子网包含 ***********\n                  到 ************* 的地址。", "apigw.policy.rate-limit-policy.ReteLimitRules.PerSecond": "每秒", "apigw.policy.rate-limit-policy.ReteLimitRules.EveryMinute": "每分钟", "apigw.policy.rate-limit-policy.ReteLimitRules.Hourly": "每小时", "apigw.policy.rate-limit-policy.ReteLimitRules.EveryDay": "每天", "apigw.policy.rate-limit-policy.ReteLimitRules.JudgmentCondition": "判断条件", "apigw.policy.rate-limit-policy.ReteLimitRules.ThrottlingRules": "限流规则", "apigw.policy.rate-limit-policy.ReteLimitRules.CurrentLimitingRange": "限流范围", "apigw.policy.rate-limit-policy.ReteLimitRules.AddAtLeastOneThrottling": "请至少添加一条限流策略", "apigw.policy.rate-limit-policy.ReteLimitRules.PleaseImproveTheCurrentLimiting": "请完善限流策略", "apigw.policy.rate-limit-policy.CurrentLimiting": "限流", "apigw.policy.security-guard-policy.PolicyContent.ProtectionServices": "防护服务", "apigw.policy.security-guard-policy.PolicyContent.TheContentSafeUrlIs": "默认填充内容安全的URL，您可以", "apigw.policy.security-guard-policy.PolicyContent.ReferenceDocumentation": "参考文档", "apigw.policy.security-guard-policy.PolicyContent.EnterAProtectionServiceAddress": "请输入防护服务地址", "apigw.policy.security-guard-policy.PolicyContent.CheckRequest": "检查请求", "apigw.policy.security-guard-policy.PolicyContent.Enable": "开启", "apigw.policy.security-guard-policy.PolicyContent.Close": "关闭", "apigw.policy.security-guard-policy.PolicyContent.CheckResponse": "检查响应", "apigw.policy.security-guard-policy.PolicyContent.TheResponseIsUsedTo": "检查响应用于检查大模型的回答内容是否合规，生效时会使流式响应变为非流式", "apigw.policy.security-guard-policy.PolicyContent.ProtectionLevel": "防护等级", "apigw.policy.security-guard-policy.PolicyContent.Low": "低", "apigw.policy.security-guard-policy.PolicyContent.Medium": "中", "apigw.policy.security-guard-policy.PolicyContent.High": "高", "apigw.policy.security-guard-policy.PolicyContent.ObservationMode": "观察模式", "apigw.policy.security-guard-policy.PolicyContent.BlockOnlyHighRiskRequests": "仅拦截高风险的请求", "apigw.policy.security-guard-policy.PolicyContent.BlockOnlyHighRiskRequests.1": "仅拦截中高风险的请求", "apigw.policy.security-guard-policy.PolicyContent.InterceptAllRiskyRequests": "拦截所有具有风险的请求", "apigw.policy.security-guard-policy.PolicyContent.TheRequestResponseContentIs": "检测请求/响应内容，但是不会产生拦截行为", "apigw.policy.security-guard-policy.SecurityGuardAuth.YouHaveNotEnabledContent": "您尚未开通内容安全，请先开通才可以配置内容安全防护。注意，开通后会产生相应费用。", "apigw.policy.security-guard-policy.SecurityGuardAuth.GoToActivate": "前往开通", "apigw.policy.security-guard-policy.SecurityGuardAuth.EnableVerification": "开通校验", "apigw.policy.security-guard-policy.SecurityGuardAuth.AuthorizationSucceeded": "授权成功", "apigw.policy.security-guard-policy.SecurityGuardAuth.TheServiceAssociatedRoleIs": "服务关联角色未授权，请前往授权。", "apigw.policy.security-guard-policy.SecurityGuardAuth.GoToAuthorization": "前往授权", "apigw.policy.security-guard-policy.SecurityGuardAuth.AuthorizationVerification": "授权校验", "apigw.policy.security-guard-policy.SecurityGuardAuth.RoleAuthorization": "角色授权", "apigw.policy.security-guard-policy.SecurityGuardAuth.ClickAuthorize": "点击授权", "apigw.policy.security-guard-policy.SecurityGuardAuth.CloudNativeApiGatewayUses": "云原生API网关使用此服务关联角色访问内容安全服务中的资源。", "apigw.policy.security-guard-policy.ContentSecurityProtection": "内容安全防护", "apigw.policiesPlugins.policy.useAiPolicyHook.SavedSuccessfully": "保存成功", "apigw.policiesPlugins.policy.useAiPolicyHook.SaveFailed": "保存失败", "apigw.apiAi.share.SnatCheck.TheVpcidOfYourCurrent": "您当前网关的vpcId为:", "apigw.apiAi.share.SnatCheck.Vswitchid": ",vSwitchId为:", "apigw.apiAi.userGuide.CurlLocationDomainVChat.1": "curl --location '{domain}/v1/chat/completions' \\\\\n    --header 'Content-Type: application/json' \\\\\n    --data '{\n       \"stream\": false,\n       \"model\": \"qwen-max\",\n       \"messages\": [\n           {\n               \"role\": \"system\",\n               \"content\": \"You are a helpful assistant.\"\n           },\n           {\n               \"role\": \"user\",\n               \"content\": \"你是谁？\"\n           }\n       ],\n       \"temperature\": 0.7,\n       \"top_p\": 1.0,\n       \"max_tokens\": 100,\n       \"presence_penalty\": 0,\n       \"frequency_penalty\": 0\n   }'", "apigw.components.gateway.GatewayListColumns.PleaseUseUppercaseAndLowercase": "请以大小写字母、数字、中文、短划线（-）、下划线（_），且不以短划线（-）和下划线（_）开头结尾，不超过45个字符的", "apigw.shared.ExtendCollapse.Save": "保存", "apigw.shared.ExtendCollapse.Cancel": "取消", "apigw.src.constants.CustomAiServices": "OpenAI兼容（OpenAI Compatible）", "apigw.components.AiModel.OnlyAlServicesThatMeet": "仅支持满足 OpenAl 接口标准的 Al 服务", "apigw.apiAi.statistics.YouHaveNotEnabledGateway": "您尚未开启网关日志投递功能", "apigw.apiAi.statistics.OnlyWhenThisFeatureIs": "只有开启此功能，API日志才会被投递到指定的日志服务 Project 中。", "apigw.api-manage.apiList.ApiListTableProps.More": "更多", "apigw.router.components.Configs.More": "更多", "apigw.components.gateway.GatewayListColumns.More": "更多", "apigw.gateway.gateway-entrance.GatewayIp.PublicIpAddress": "公网IP", "apigw.gateway.gateway-entrance.GatewayIp.IntranetIp": "内网IP", "apigw.gateway.gateway-entrance.GatewayIp.PublicPort": "公网端口", "apigw.gateway.gateway-entrance.GatewayIp.PrivatePort": "私网端口", "apigw.gateway.gateway-entrance.GatewayIp.InstanceIpItIsThe": "实例 IP\n        是网关对外提供服务的IP地址，但不建议您直接将业务域名映射到实例IP上。推荐您采用业务域名CNAME至访问域名的使用方式，访问域名会在多个IP间负载均衡，避免单IP单可用区故障。实例IP仅建议您在申请域名注册或配置IP黑白名单时使用。", "apigw.src.constants.Custom": "自定义", "apigw.src.constants.MigrateToTheCloud": "迁移上云", "apigw.components.CollocateInfo.AccessPortal": "访问入口", "apigw.components.EntranceSet.Configs.InstanceSource": "实例来源", "apigw.components.EntranceSet.CurrentlyCustomPortalsAreOnly": "自定义入口目前仅针对从已有网关（如Nginx\n        Ingress）迁移至云原生API网关时，需要保留原网关SLB入口的场景，网关迁移请前往", "apigw.components.EntranceSet.IfYouNeedToUse": "。如果您有其他场景需要用到原有的SLB，而不使用产品提供的访问域名，请", "apigw.components.EntranceSet.SubmitATicket": "提工单", "apigw.components.EntranceSet.ContactUs": "联系我们。", "apigw.components.custom-entry.EnvironmentLevelDomainName": "环境二级域名", "apigw.components.custom-entry.InstanceIp": "实例IP", "apigw.components.custom-entry.CustomEntry": "自定义入口", "apigw.create-task.components.ip-server.PortListener": "端口监听", "apigw.create-task.components.ip-server.GatewayProtocol": "网关协议", "apigw.create-task.components.ip-server.VirtualServerGroup": "虚拟服务器组", "apigw.create-task.components.ip-server.Operation": "操作", "apigw.create-task.components.ip-server.Delete": "删除", "apigw.create-task.components.ip-server.Add": "添加", "apigw.create-task.components.step1.ClickNextCloudNativeApi": "点击下一步，云原生API网关将自动监听所选容器集群内，关联到 IngressClass 的所有 Ingress\n        资源的变化，并生效 Ingress 资源中域名、路由的相关配置。", "apigw.create-task.components.step1.SourceInstanceType": "源实例类型", "apigw.create-task.components.step1.SelectTheSourceInstanceType": "请选择源实例类型", "apigw.create-task.components.step1.Environment": "所属环境", "apigw.create-task.components.step1.Instance": "所属实例", "apigw.create-task.components.step1.Vpc": "所属VPC", "apigw.create-task.components.step1.SourceCluster": "来源集群", "apigw.create-task.components.step1.SelectASourceCluster": "请选择来源集群", "apigw.create-task.components.step1.CreateTheAboveRoutesUnder": "将上述路由统一创建在云原生API网关的HTTP API（Ingress）下。", "apigw.create-task.components.step1.ResourceGroup": "资源组", "apigw.create-task.components.step1.SelectAResourceGroup": "请选择资源组", "apigw.create-task.components.step1.Description": "描述", "apigw.create-task.components.step1.EnterADescription": "请输入描述内容", "apigw.create-task.components.step2.Name": "名称", "apigw.create-task.components.step2.Rules": "规则", "apigw.create-task.components.step2.IncompatibleAnnotations": "不兼容注解", "apigw.create-task.components.step2.RouteVerification": "路由校验", "apigw.create-task.components.step2.ClickOkToGoTo": "点击「确定」进入下一步的流量切换操作。对于列表中的 Ingress 资源，云原生API网关仍会解析 Ingress Spec 中定义的路由规则，但会自动忽略不兼容的注解，请确保不影响业务。", "apigw.create-task.components.step2.IngressResourcesInTheList": "列表中的 Ingress 资源，云原生API网关可解析 Ingress Spec\n        中定义的路由规则，但会自动忽略不兼容的注解，您可以提工单咨询解决方案。", "apigw.create-task.components.step2.TheRouteCompatibilityTestIs": "正在进行路由兼容性测试，请稍等", "apigw.create-task.components.step2.IngressOfTheResourcesYou": "您选择的Ingress资源都已在云原生API网关侧生效，未检测到不兼容的规则。请进行下一步的流量切换操作。", "apigw.create-task.components.step2.ReVerify": "重新校验", "apigw.create-task.components.step3.MakeSureThatTheLocal": "正式切流前，确保已完成本地测试：修改本地 hosts 文件，为业务域名添加云原生API网关的 IP\n        解析，通过 curl 或者 postman 等工具验证流量是否符合预期。", "apigw.create-task.components.step3.SwitchingMode": "切流方式", "apigw.create-task.components.step3.PleaseSelectTheFlowSwitching": "请选择切流方式", "apigw.create-task.components.step3.DnsResolvesToTheSecond": "DNS解析至云原生API网关环境二级域名", "apigw.create-task.components.step3.InDnsServiceTransferThe": "在DNS服务中，将原来的业务域名 CNAME 至目标网关实例的环境二级域名。", "apigw.create-task.components.step3.ReuseTheOriginalClusterSlb": "复用原集群SLB", "apigw.create-task.components.step3.OnlyTraditionalClbReuseMigration": "只支持传统型负载均衡 CLB 的复用迁移。如要复用多个 SLB，请依次创建多个迁移任务。", "apigw.create-task.components.step3.ContainerClusterNamespace": "容器集群命名空间", "apigw.create-task.components.step3.SelectAContainerClusterNamespace": "请选择容器集群命名空间", "apigw.create-task.components.step3.SelectTheNamespaceWhereNginx": "请选择nginx-ingress-controller所在的命名空间", "apigw.create-task.components.step3.ContainerClusterSlbService": "容器集群SLB服务", "apigw.create-task.components.step3.SelectContainerClusterSlbService": "请选择容器集群SLB服务", "apigw.create-task.components.step3.SelectTheLoadbalancerServiceType": "请选择要迁移的SLB对应的LoadBalancer类型服务", "apigw.create-task.components.step3.PortsAndBackendServers": "端口及后端服务器", "apigw.create-task.components.step3.AddPortAndBackendServer": "请添加端口及后端服务器信息", "apigw.create-task.components.step3.CheckTheRelevantInformation": "请检查相关信息", "apigw.create-task.components.step4.MakeSureThatTheLocal": "正式切流前，确保已完成本地测试：修改本地hosts文件，为业务域名添加云原生API网关的 IP\n        解析，通过curl或者postman等工具验证流量是否符合预期。", "apigw.create-task.components.step4.FlowSwitchingGoToThe": "切流操作：请前往DNS供应商的域名解析服务，为迁移路由涉及的所有域名添加云原生API网关环境二级域名地址的CNAME映射。建议使用DNS的权重解析方式逐步切流。\n            操作完成后请点击", "apigw.create-task.components.step4.CompleteMigration": "完成迁移", "apigw.create-task.components.step4.EnvironmentNameId": "环境名称/ID", "apigw.create-task.components.step4.EnvironmentLevelDomainName": "环境二级域名", "apigw.create-task.components.step4.GatewayInstanceIp": "网关实例IP", "apigw.create-task.components.step4.PublicIpAddress": "公网IP：", "apigw.create-task.components.step4.PrivateIpAddress": "私网IP：", "apigw.create-task.components.step4.NoteTheIngressIpAddress": "请注意：网关实例入口IP仅推荐您本地测试使用，生产使用建议将自定义域名CNAME到上述环境的二级域名。环境二级域名会在多个IP间实现负载均衡，避免单IP单可用区故障。", "apigw.create-task.components.step4.BestPractices": "最佳实践", "apigw.create-task.components.step4.Click": "点击", "apigw.create-task.components.step4.ChangeSlb": "变更SLB", "apigw.create-task.components.step4.AutomaticallyDishostTheSlbFrom": "，自动将SLB脱离容器托管并修改监听调度算法为“加权轮询”，进入下一步，", "apigw.create-task.components.step4.NoteThatSlbCannotPerceive": "注意变更后SLB无法感知和Nginx Ingress Controller 的Pod IP\n                  变化。请尽快完成STEP2，重新将 K8s Service关联至SLB。", "apigw.create-task.components.step4.In": "在", "apigw.create-task.components.step4.ContainerServiceConsole": "容器服务控制台", "apigw.create-task.components.step4.Cluster": "中将集群", "apigw.create-task.components.step4.LoadbalancerServicesIn": "中的LoadBalancer服务", "apigw.create-task.components.step4.DeleteAllTheCurrentAnnotations": "的当前注解全部删除，添加以下注解", "apigw.create-task.components.step4.ClickAfterCompletion": "完成后点击", "apigw.create-task.components.step4.PreCheck": "前置检查", "apigw.create-task.components.step4.GoToTheNextStep": "，进入下一步。", "apigw.create-task.components.step4.PleaseEnterAWeight": "请输入权重", "apigw.create-task.components.step4.ThisValueIsTheSum": "1. 该值为云原生API网关各节点的权重值总和，SLB\n                      会根据虚拟服务器组中云原生API网关和原网关各节点的权重比值分配流量。其中Nginx\n                      Ingress 节点的权重总和默认为100；当云原生API网关权重设为50，则承接 1/3\n                      流量，当云原生网关权重设为100，则承接 1/2 流量。", "apigw.create-task.components.step4.YouCanCDC": "2. 您可以将集群 c85d8c48495054f51beb7a73e444c9dc8 中 LoadBalancer 服务\n                      kube-system/nginx-ingress-lb\n                      的注解service.beta.kubernetes.io/alibaba-cloud-loadbalancer-weight\n                      的值调低，间接调大云原生API网关的权重。特别的，当该注解的值为0时，流量则全部导入云原生网关。", "apigw.create-task.components.step4.SlbIsALayerLoad": "3.\n                      SLB作为四层负载均衡，流量控制粒度为连接级别，该权重值无法精确控制请求级别的分发比例。", "apigw.create-task.components.step4.IfTheSuccessRateDrops": "4. 切流后如果出现成功率下跌，可将该权重设为0进行流量回滚。", "apigw.create-task.components.step4.Effective": "生效", "apigw.create-task.components.step4.SetTheWeightOfThe": "设置网关在虚拟服务器组中的权重并点击确定按钮，将流量导入网关。权重流量分配依赖SLB虚拟服务组的数量，如原来存量已有两个虚拟服务组，迁移后新增网关的两个虚拟服务组，当权重设为100，则云原生API网关和原网关各承接\n                  SLB 50%的流量。业务测试通过后，点击", "apigw.create-task.components.step4.CompleteTrafficVerification": "完成流量验证", "apigw.create-task.components.step4.GoToTheNextStep.1": "进入下一步。", "apigw.create-task.components.step4.ManuallyCluster": "中手工将集群", "apigw.create-task.components.step4.MediumLoadbalancerService": "中 LoadBalancer 服务", "apigw.create-task.components.step4.AnnotationServiceBetaKubernetesIo": "的注解\n                service.beta.kubernetes.io/alibaba-cloud-loadbalancer-weight的值设置为0，或者直接删除该Service资源，则\n                SLB 的全部流量切换至云原生API网关。确定业务正常后，点击", "apigw.create-task.components.step4.Button": "按钮", "apigw.components.create-task.NextStep": "下一步", "apigw.components.create-task.Cancel": "取消", "apigw.components.create-task.PreviousStep": "上一步", "apigw.components.create-task.CreateAMigrationConfiguration": "创建迁移配置", "apigw.components.create-task.RouteMigrationRules": "路由迁移规则", "apigw.components.create-task.RouteVerification": "路由校验", "apigw.components.create-task.SwitchoverSelection": "切流选择", "apigw.components.create-task.TrafficSwitching": "流量切换", "apigw.$regionId.migrate.TargetInstanceIdName": "目标实例ID/名称", "apigw.$regionId.migrate.SourceInstanceIdName": "源实例ID/名称", "apigw.$regionId.migrate.SourceInstanceType": "源实例类型", "apigw.$regionId.migrate.SourceIngressclass": "源IngressClass", "apigw.$regionId.migrate.SwitchingMode": "切换方式", "apigw.$regionId.migrate.MigrationProgress": "迁移进度", "apigw.$regionId.migrate.RouteMigrationIndicatesThatIngress": "1.“路由迁移” 表示ingress路由规则已被云原生API网关监听并生效；", "apigw.$regionId.migrate.TrafficSwitchingIndicatesThatTraffic": "2.“流量切换”表示正在将业务流量从源网关切换至云原生API网关；", "apigw.$regionId.migrate.CompleteMigrationIndicatesThatThe": "3.“完成迁移” 表示迁移过程结束", "apigw.$regionId.migrate.RouteMigration": "路由迁移", "apigw.$regionId.migrate.TrafficSwitching": "流量切换", "apigw.$regionId.migrate.CompleteMigration": "完成迁移", "apigw.$regionId.migrate.MigrationFailed": "迁移失败", "apigw.$regionId.migrate.Description": "描述", "apigw.$regionId.migrate.CreationTime": "创建时间", "apigw.$regionId.migrate.Operation": "操作", "apigw.$regionId.migrate.Edit": "编辑", "apigw.$regionId.migrate.Delete": "删除", "apigw.$regionId.migrate.Prompt": "提示", "apigw.$regionId.migrate.AreYouSureYouWant": "你确定要删除该迁移配置吗?", "apigw.$regionId.migrate.DeletedSuccessfully": "删除成功", "apigw.$regionId.migrate.MigrateToTheCloud": "迁移上云", "apigw.$regionId.migrate.CreateATask": "创建任务", "apigw.src.sidebar.MigrateToTheCloud": "迁移上云", "apigw.shared.ExtendSelect.AMaximumOfPiecesOf": "本次最多显示200条数据，超出请搜索", "apigw.shared.SlsLog.TheGatewayNeedsToUse": "网关需要使用日志服务 SLS 进行日志的采集、分析与展示。", "apigw.shared.SlsLog.LogstoreIsBeingCreatedPlease": "LogStore创建中，请稍等...", "apigw.shared.SlsLog.QueryingTheCreationProgressOf": "正在查询logStore创建进度", "apigw.shared.SlsLog.YouCanManuallyRefreshThe": "您可手动刷新查询创建进度", "apigw.shared.SlsLog.Refresh": "刷新", "apigw.shared.SlsLog.YouHaveEnabledLogShipping": "您已开启SLS日志投递", "apigw.shared.SlsLog.GoToLogAnalysis": "前往日志分析", "apigw.observe.components.TracePermission.GoToLinkTracking": "前往链路追踪", "apigw.policy.consumer-auth-policy.PolicyContent.AuthenticationMethod": "认证方式", "apigw.policy.consumer-auth-policy.PolicyContent.AfterYouEnableConsumerAuthentication": "开启消费者认证后，可前往", "apigw.policy.consumer-auth-policy.PolicyContent.ConsumerManagement": "消费者管理", "apigw.policy.consumer-auth-policy.PolicyContent.ConfigureTheAuthorizationRelationshipIn": "中配置授权关系, 否则将无法访问当前 API", "apigw.policy.consumer-auth-policy.ConsumerAuthorization": "消费者授权", "apigw.policiesPlugins.policy.useAiPolicyHook.Effective": "已生效", "apigw.policiesPlugins.policy.useAiPolicyHook.Disabled": "已停用", "apigw.policiesPlugins.policy.useAiPolicyHook.ConsumerAuthorizationHasTakenEffect": "消费者授权已生效", "apigw.policiesPlugins.policy.useAiPolicyHook.ConsumerAuthorizationDisabled": "消费者授权已停用", "apigw.api-manage.interfaceList.ApiOperationsContent.ConsumerCertification": "消费者认证", "apigw.components.api-operations-consumer.Consumers": "消费者", "apigw.components.api-operations-slide.ApiBasicInfo.ConsumerCertification": "消费者认证", "apigw.components.api-operations-slide.ApiBasicInfo.AuthenticationMethod": "认证方式", "apigw.components.CreateConsumerSlide.SlideContent.Status": "状态", "apigw.components.CreateConsumerSlide.SlideContent.Enable": "启用", "apigw.components.CreateConsumerSlide.SlideContent.Disable": "停用", "apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.BulkDeauthorization": "批量解除授权", "apigw.consumer-manage.consumer-auth.DeleteConsumerAuth.ConfirmBulkDeauthorization": "确认批量解除授权？", "apigw.consumer-auth.api-consumer-auth.ApiConsumerTable.Authorization": "授权", "apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Status": "状态", "apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Enabled": "已启用", "apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Disabled": "已停用", "apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.EnterAConsumerNameTo": "请输入消费者名称进行搜索", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.AkSkHmac": "AK/SK（HMAC）", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.ConfigurationInformation": "配置信息", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.Edit": "编辑", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.EnableStatus": "启用状态", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.Enabled": "已开启", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.NotEnabled": "未开启", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.ConsumerAuthenticationIsNotEnabled": "消费者认证未开启，消费者授权关系暂不生效。为提升API访问安全性，建议您开启消费者认证。", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.AuthenticationMethod": "认证方式", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.InterfaceLevelConsumerAuthentication": "接口级消费者认证", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.AfterTheConsumerAuthenticationConfiguration": "变更消费者认证配置后，API需重新发布才可生效。", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.SelectYourEnvironment": "请选择所属环境", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Consumers": "消费者", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.AddConsumerAuthorization": "添加消费者授权", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Add": "添加", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.EditConsumerAuthorization": "编辑消费者授权", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Ok": "确定", "apigw.consumer-auth.api-consumer-auth.ConsumerSelect.SelectAConsumer": "请选择消费者", "apigw.consumer-auth.api-consumer-auth.ConsumerSelect.CreateAConsumer": "创建消费者", "apigw.consumer-auth.api-consumer-auth.ConsumerSelect.ConsumerAuthorizationAlreadyExistsAnd": "消费者授权已存在，无法再次选择", "apigw.consumer-manage.consumer-auth.tableProps.ConsumerAuthenticationIsNotEnabled": "当前 AI API 未开启消费者认证，授权暂不生效", "apigw.consumer-manage.consumer-auth.tableProps.ConsumerCertification": "消费者认证", "apigw.consumer-manage.consumer-auth.tableProps.NotEnabled": "未开启", "apigw.consumer-manage.consumer-auth.tableProps.TheCurrentInterfaceDoesNot": "当前接口未开启消费者认证，授权暂不生效", "apigw.consumer-manage.consumer-auth.tableProps.ConsumerAuthenticationIsNotEnabled.1": "当前路由未开启消费者认证，授权暂不生效", "apigw.consumer-manage.consumer-basic-infor.Authentication.CurrentlyAkSkAuthenticationIs": "当前暂未配置 AK/SK 认证方式，请", "apigw.consumer-manage.consumer-basic-infor.Authentication.GoToConfiguration": "前往配置", "apigw.consumer-manage.consumer-basic-infor.Authentication.Add": "添加", "apigw.consumer-manage.consumer-basic-infor.Authentication.Edit": "编辑", "apigw.consumer-manage.consumer-basic-infor.Authentication.YouMustConfigureTheCredential": "需要您先配置凭证来源，才可以添加凭证", "apigw.consumer-manage.consumer-basic-infor.Authentication.CurrentlyTheApiKeyAuthentication": "当前暂未配置 API Key 认证方式，请", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.CurrentlyJwtAuthenticationIsNot": "当前暂未配置 JWT 认证方式，请", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.AddJwt": "添加 JWT", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.AddJwt.1": "添加JWT", "apigw.consumer-manage.consumer-basic-infor.JwtBasicInfo.GoToConfiguration": "前往配置", "apigw.consumer-manage.consumer-basic-infor.SourceDialog.EditCredentialSource": "编辑凭证来源", "apigw.consumer-manage.consumer-basic-infor.Status": "状态", "apigw.consumer-manage.consumer-basic-infor.Enabled": "已启用", "apigw.consumer-manage.consumer-basic-infor.Disabled": "已停用", "apigw.consumer-manage.consumer-list.ConsumerEnableAction.Disable": "停用", "apigw.consumer-manage.consumer-list.ConsumerEnableAction.Enable": "启用", "apigw.consumer-manage.consumer-list.ConsumerEnableAction.AreYouSureText": "确认{text}吗？", "apigw.consumer-manage.consumer-list.ConsumerEnableAction.AfterDisablingTheConsumerAuthorization": "停用后，消费者授权立即失效，已授权的消费者将无法调用API，请谨慎操作。", "apigw.consumer-manage.consumer-list.ConsumerEnableAction.ConsumerAuthorizationTakesEffectImmediately": "启用后，消费者授权立即生效", "apigw.consumer-manage.consumer-list.ConsumerEnableAction.ConsumerHasText": "消费者已{text}", "apigw.consumer-manage.consumer-list.ConsumerTableProps.Status": "状态", "apigw.consumer-manage.consumer-list.ConsumerTableProps.Enabled": "已启用", "apigw.consumer-manage.consumer-list.ConsumerTableProps.Disabled": "已停用", "apigw.policy.consumer.ConsumerAuth.AfterEnablingConsumerAuthenticationYou": "开启消费者认证后，需为当前路由绑定消费者授权关系，否则无法访问", "apigw.policy.consumer.ConsumerAuth.AfterYouEnableConsumerAuthentication": "开启消费者认证后，需为当前接口绑定消费者授权关系，否则无法访问", "apigw.policy.consumer.ConsumerAuth.Consumers": "消费者", "apigw.policy.consumer.EnableStatus": "启用状态", "apigw.consumer-manage.$id.Enabled": "已启用", "apigw.consumer-manage.$id.Disabled": "已停用", "apigw.publish.switch.current.version.in.the.instances.After.version.switching.it.will.be.directly.released.and.take.effect.Please.confirm": "您将在「 {name} 」实例，切换当前API版本。版本切换后将直接发布生效，请确认", "apigw.components.AIDialogs.AiDiagnosis": "AI 诊断", "apigw.components.ai-diagnose.ErrorCode": "错误码", "apigw.components.ai-diagnose.AskAi": "问 AI", "apigw.components.ai-diagnose.EnterARouteName": "请输入路由名称", "apigw.components.ai-diagnose.SelectTheInstanceToDiagnose": "请先选择要诊断的实例", "apigw.components.ai-diagnose.ThereIsNoDataIn": "当前筛选条件下暂无数据，请更改条件或重试", "apigw.components.ai-diagnose.NoDataAvailable": "暂无数据", "apigw.apiDetails.publishDetail.TheInstanceAccessPortalIs": "实例访问入口仅供测试使用，客户端直接调用时，每天有100次访问限制。生产使用需为API绑定自定义域名，并将自定义域名CNAME至实例访问入口上。", "apigw.apiDetails.publishDetail.offline.AfterTheApiIsOffline.1": "API下线后，该API在指定实例无法访问，请确保已告知用户。", "apigw.apiDetails.publishDetail.tableProps.InstanceAccessPortal": "实例访问入口", "apigw.api-manage.apiList.ApiListTable.SelectAnInstance": "选择实例", "apigw.api-manage.apiList.ApiListTableProps.InstanceAccessPortalDebuggingOnly": "实例访问入口 (仅供调试)", "apigw.api-manage.apiList.ApiListTableProps.TheInstanceAccessPortalIs": "实例访问入口仅供调试使用，若多个AI API共用同一个实例访问入口，则无法保证AI API访问正常。请使用自定义域名。", "apigw.api-manage.apiList.DeleteApiAction.TheCurrentApiHasPublished.1": "当前API存在已发布的实例，需所有实例下线后再进行删除操作。", "apigw.components.conflictDetection.InstanceId": "实例ID", "apigw.createApiDialog.ai.TheNameIsUniqueAnd.1": "名称唯一，支持英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.components.createApiDialog.TheNameIsUniqueAnd.1": "名称唯一，支持英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.components.pressureAction.PressureTest": "压测", "apigw.create-actions.CreateAiApiSidePanel.TheNameIsUniqueAnd.1": "名称唯一，支持英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.create-actions.CreateAiApiSidePanel.SelectAnInstance": "请选择实例", "apigw.create-actions.CreateAiApiSidePanel.SelectTheInstanceToWhich": "请选择所属实例", "apigw.createApi.create-actions.CreateApiBasicForm.TheNameIsUniqueAnd.2": "名称唯一，支持英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.createApi.create-actions.CreateIngressApiSidePanel.SelectAnInstance": "请选择实例", "apigw.api-manage.headBtn.TheCurrentApiHasPublished": "当前API存在已发布的实例，需所有实例下线后再进行删除操作。", "apigw.components.envAndBackendServices.TheCurrentInstanceAlreadyHas": "当前实例已存在API发布，再次发布将覆盖原有发布信息，请谨慎操作。", "apigw.components.envAndBackendServices.SelectAnInstance": "请选择实例", "apigw.headBtn.publish.IfNoDomainNameIs.1": "未选择域名时，将仅支持通过实例访问入口访问（用于测试场景）", "apigw.components.api-operations-debug.OperationDebug.ChangesExistInThisInstance": "(该实例下存在变更，请重新发布)", "apigw.components.api-operations-debug.OperationDebug.Instance": "实例", "apigw.components.api-operations-debug.OperationDebug.AllowDebuggingOnlyForPublished.1": "仅允许已发布的实例调试", "apigw.components.api-operations-debug.OperationDebug.TheCurrentApiHasChanged": "当前API在该实例下存在变更，请", "apigw.components.api-operations-policy.PolicyEnvList.CurrentInstance": "当前实例", "apigw.components.api-operations-policy.PolicyEnvList.Instance": "实例", "apigw.api-manage.publishHistory.TheLastInstancesOfThe": "发布历史分实例保留最近10次。仅线上生效，接口列表中现有的API与接口定义并不会被覆盖，您可在现有定义的基础上继续更新与发布。", "apigw.router.components.Configs.Instance": "实例", "apigw.router.components.Configs.InstanceAccessPortal": "实例访问入口", "apigw.components.envFilter.SelectAnInstance": "请选择实例", "apigw.components.envFilter.Instance.1": "实例", "apigw.detail.components.RouteOverview.InstanceIdName": "实例ID/名称", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.ActiveGatewayInstance": "生效网关实例", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectAnInstance": "请选择实例", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.NoOptionalInstancesAreAvailable": "暂无可选的实例", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.SelectAnInstance": "请选择实例", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.NoOptionalInstancesAreAvailable": "暂无可选的实例", "apigw.consumer-manage.consumer-auth.tableProps.Instance": "实例", "apigw.components.create-edit-route.IfNoDomainNameIs": "未选择域名时，将仅支持通过实例访问入口访问（用于测试场景）", "apigw.components.create-edit-route.InstanceAndBackendService": "所属实例&后端服务", "apigw.components.domain-certificate.DomainSlide.YouMustCompleteTheDomain.1": "，您需要自行完成域名注册，以及到实例访问入口之间的解析设置。", "apigw.elastic-expansion.components.elastic-form.TheScaleInOperationIs": "扩缩容为分钟级的操作，无法瞬间完成。", "apigw.elastic-expansion.components.elastic-form.EnterTheMaximumNumberOf": "请填写最大节点数", "apigw.elastic-expansion.components.elastic-form.PleaseFillInTheExpansion": "请填写扩缩容水位", "apigw.elastic-expansion.components.elastic-form.WarningWaterLevel": "警戒水位", "apigw.elastic-expansion.components.time-period.TimePeriodAllowedForScaling": "允许缩容的时间段（北京时区）", "apigw.elastic-expansion.components.time-period.DuringTheAllowedScaleIn": "在允许缩容时间段内，实例资源符合缩容条件，且已低水位持续运行1小时及以上，在缩容时间段内才会触发缩容", "apigw.elastic-expansion.components.time-period.YouCanAddUpTo.1": "最多可添加3个时间段，缩容将使长连接断开，请避开业务高峰时间段", "apigw.components.gateway.GatewayListColumns.PleaseUseUppercaseAndLowercase.1": "请以大小写字母、数字、短划线（-）、下划线（_），且不以短划线（-）和下划线（_）开头结尾，不超过45个字符的", "apigw.components.gateway.GatewayListColumns.AccessPoint": "接入点", "apigw.gateway.gateway-entrance.GatewayIp.AccessDomainName": "访问域名", "apigw.gateway.gateway-entrance.GatewayIp.TheAccessDomainNameIs": "访问域名是网关实例的访问入口，在生产使用中，您需要将业务域名通过DNS服务CNAME至访问域名。直接通过访问域名访问每天有1000次访问限制，可用于测试，请勿直接生产使用。", "apigw.plugin-manage.plugin-rule-config.PluginEnvSelect.Instance": "所属实例", "apigw.plugin-manage.plugin-rule-config.PluginEnvSelect.SelectAnInstance": "请选择实例", "apigw.plugin-manage.plugin-rule-config.PluginEnvSelect.NoOptionalInstancesAreAvailable": "暂无可选的实例", "apigw.$regionId.ai-diagnose.AiDiagnosis": "AI 诊断", "apigw.$apiName.$apiId.TheCurrentApiHasChanged": "当前API在该实例下存在变更，请", "apigw.$regionId.event-center.Event": "事件", "apigw.components.BasicInfo.PleaseUseUppercaseAndLowercase": "请以大小写字母、数字、短划线（-）、下划线（_），且不以短划线（-）和下划线（_）开头结尾，不超过45个字符的", "apigw.components.BasicInfo.AccessPoint": "接入点", "apigw.components.CollocateInfo.AccessPoint": "接入点", "apigw.components.custom-entry.AccessDomainNameAndIp": "访问域名及IP", "apigw.gateway.$id.AiDiagnosis": "AI诊断", "apigw.components.EditForm.TheDomainNameLevelOf": "授权规则域名级生效，生效范围与Cookie Domain设置保持一致", "apigw.components.BasicInfo.JwtAuthentication": "JWT认证", "apigw.components.BasicInfo.OidcCertification": "OIDC认证", "apigw.components.BasicInfo.CustomAuthentication": "自定义鉴权", "apigw.create-task.components.step1.SelectAnInstance": "请选择实例", "apigw.create-task.components.step1.TheNameIsUniqueAnd": "名称唯一，支持英文、数字、下划线“_”、“-”，不超过64个字符", "apigw.create-task.components.step3.DnsResolutionToCloudNative": "DNS解析至云原生API网关实例访问入口", "apigw.create-task.components.step3.InDnsServiceTransferThe.1": "在DNS服务中，将原来的业务域名 CNAME 至目标网关实例访问入口。", "apigw.create-task.components.step4.FlowSwitchingGoToThe.1": "切流操作：请前往DNS供应商的域名解析服务，为迁移路由涉及的所有域名添加云原生API网关实例访问入口地址的CNAME映射。建议使用DNS的权重解析方式逐步切流。操作完成后请点击", "apigw.create-task.components.step4.InstanceNameId": "实例名称/ID", "apigw.create-task.components.step4.InstanceAccessPortal": "实例访问入口", "apigw.create-task.components.step4.NoteTheGatewayInstanceEntrance": "请注意：网关实例入口IP仅推荐您本地测试使用，生产使用建议将自定义域名CNAME到上述实例访问入口。实例访问入口会在多个IP间实现负载均衡，避免单IP单可用区故障。", "apigw.src.sidebar.Event": "事件", "apigw.src.sidebar.AiDiagnosis": "AI 诊断", "apigw.aiapi.tabs.policyplugin": "策略与插件", "apigw.aiapi.tabs.usage": "使用指南", "apigw.aiapi.tabs.statistics": "统计", "apigw.apiAi.policiesPlugins.MorePlugIns": "更多插件", "apigw.policiesPlugins.policy.RedisConfig.DatabaseNumber": "数据库编号", "apigw.policiesPlugins.policy.RedisConfig.EnterADatabaseNumber": "请输入数据库编号", "apigw.policiesPlugins.policy.RedisConfig.EnterTheDatabaseNumberIn": "数据库编号输入的范围是 0 - 15", "apigw.policy.rate-limit-policy.ReteLimitRules.ByConsumer": "按消费者", "apigw.envAndBackendServices.backendServices.LabelRoutingSupportsContainerService": "标签路由支持容器服务和MSE Nacos", "apigw.components.envAndBackendServices.LabelRoutingProportional": "标签路由（按比例）", "apigw.components.envAndBackendServices.DistributeRequestsToMultipleVersions": "将请求按比例分发到多个后端服务的多个版本。全链路灰度场景下推荐优先使用单服务路由，以获得更好的性能与体验。", "apigw.components.envAndBackendServices.LearnMore": "了解详情", "apigw.components.domain-certificate.DeployResourceTableProps.ClientTwoWayAuthenticationCa": "客户端双向认证 CA 证书", "apigw.components.domain-certificate.DomainSlide.ClientMutualAuthenticationMtls": "客户端双向认证 mTLS", "apigw.components.domain-certificate.DomainSlide.ClientTwoWayAuthenticationCa": "客户端双向认证 CA 证书", "apigw.components.domain-certificate.DomainSlide.CaCertificateUsedToVerify": "用于验证客户端身份的 CA 证书", "apigw.components.domain-certificate.DomainSlide.CannotBeEmpty": "不能为空", "apigw.components.domain-certificate.DomainSlide.EnterACaCertificateIn": "请输入PEM格式的CA证书。以下是一个示例：\n-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAKwH9nl9hdYwMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV BAYTAkNOMQswCQYDVQQIDAJCTzELMAkGA1UEBwwCQk8xCzAJBgNVBAoMAmNOMQww CgYDVQQDDANUZXN0MB4XDTE2MTAwNTE2Mjc1OVoXDTI2MTAwMzE2Mjc1OVowRTEL MAkGA1UEBhMCQ04xCzAJBgNVBAgMAkJPMQswCQYDVQQHDAJCTzELMAkGA1UECgwC Y04xDDAKBgNVBAMMA1Rlc3QwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB AQDNumJ4pRIUFqkT1CxJKo+SBz6yLWqQBCPZh6BB9p6KuKMF9ztGY8Gh2lWy9G9V Ye2dFJ2K1jg3XZG2QWzBG/JG+GdH6B2u1uVQYgKBqoe/fdCb9Z/aERhsxI72OIpx pOuZ8MTTWb1YPEy04aldLtuughbBWTE7YkAxkgXe7lj71ghA6XSFiKqngkBTn5yP lb/XmRtBB/TSSzhH5rUlQkeaNDicIr3Yw0KOdoj6TFoU5TT8/9oNrhyrxqhPrOEC O3CvDYimw2yEZK3FeYOnFP9wa//GvKIm8ZMdq1ZjeK6hJyIYFSj3bM4SK3mJMLIr SkO6+BmwSj/McUogULYZ7zp5AgMBAAGjUzBRMB0GA1UdDgQWBBSxZXvco+FqnSYc t/ahtk3Tknu+ajAfBgNVHSMEGDAWgBSxZXvco+FqnSYct/ahtk3Tknu+ajAPBgNV HRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBpeDDztJtS+MxUo0U3hunS ... \n-----END CERTIFICATE-----", "apigw.domain-certificate.details.ClientMutualAuthenticationMtls": "客户端双向认证 mTLS", "apigw.domain-certificate.details.ClientTwoWayAuthenticationCa": "客户端双向认证 CA 证书", "apigw.domain-certificate.details.CaCertificateUsedToVerify": "用于验证客户端身份的 CA 证书", "apigw.plugin-flow.components.AiPluginInfoContent.Configuration": "配置", "apigw.shared.DnsService.MoreSettings": "更多设置", "apigw.shared.DnsService.DnsServerIfEmptyThe": "DNS服务器（如果为空，则使用系统默认的DNS Server）", "apigw.shared.DnsService.FillInTheInternalDns": "一键填充内网DNS解析服务地址", "apigw.shared.DnsService.TheMaximumNumberOfDns": "DNS服务器列表最大限制5个。", "apigw.observe.logs.GatewayLogFieldDescription": "网关日志字段说明", "apigw.observe.logs.GoToSlsToView": "前往SLS查看", "apigw.service.components.HealthCheck.Times": "次", "apigw.components.api-operations-action-button.DeleteRoute.title": "删除 {name} 路由", "apigw.policy.search-engine-policy.PolicyContent.DataReference": "资料引用", "apigw.policy.search-engine-policy.PolicyContent.MarkdownFormat": "Markdown格式", "apigw.policy.search-engine-policy.PolicyContent.TextFormat": "文本格式", "apigw.policy.search-engine-policy.PolicyContent.NotActivated": "未开通", "apigw.policy.search-engine-policy.PolicyContent.Activated": "已开通", "apigw.policy.search-engine-policy.PolicyContent.Stopped": "已停止", "apigw.policy.search-engine-policy.PolicyContent.ActivationReviewFailed": "开通审核未通过", "apigw.policy.search-engine-policy.PolicyContent.InTrial": "试用中", "apigw.policy.search-engine-policy.PolicyContent.OfficialActivationProcessDescription": "正式开通流程说明", "apigw.policy.search-engine-policy.PolicyContent.SearchEngine": "搜索引擎", "apigw.policy.search-engine-policy.PolicyContent.Quark": "夸克", "apigw.policy.search-engine-policy.PolicyContent.Bing": "必应", "apigw.policy.search-engine-policy.PolicyContent.ServiceStatus": "服务状态", "apigw.policy.search-engine-policy.PolicyContent.SearchConfiguration": "搜索配置", "apigw.policy.search-engine-policy.PolicyContent.Hide": "隐藏", "apigw.policy.search-engine-policy.PolicyContent.Display": "显示", "apigw.policy.search-engine-policy.PolicyContent.ApplyForApiKey": "申请 API Key", "apigw.policy.search-engine-policy.PolicyContent.Offset": "偏移量", "apigw.policy.search-engine-policy.PolicyContent.NumberOfReturnedResults": "返回结果数量", "apigw.policy.search-engine-policy.PolicyContent.Timeout": "超时时间", "apigw.policy.search-engine-policy.PolicyContent.QueryTimeRange": "查询时间范围", "apigw.policy.search-engine-policy.PolicyContent.WithinDay": "1天内", "apigw.policy.search-engine-policy.PolicyContent.WithinWeek": "1周内", "apigw.policy.search-engine-policy.PolicyContent.WithinMonth": "1月内", "apigw.policy.search-engine-policy.PolicyContent.WithinYear": "1年内", "apigw.policy.search-engine-policy.PolicyContent.Unlimited": "无限制", "apigw.policy.search-engine-policy.PolicyContent.IndustryOptional": "行业 (可选)", "apigw.policy.search-engine-policy.PolicyContent.Finance": "金融", "apigw.policy.search-engine-policy.PolicyContent.Law": "法律", "apigw.policy.search-engine-policy.PolicyContent.MedicalTreatment": "医疗", "apigw.policy.search-engine-policy.PolicyContent.Internet": "互联网", "apigw.policy.search-engine-policy.PolicyContent.Tax": "税务", "apigw.policy.search-engine-policy.PolicyContent.NewsProvincial": "新闻省级", "apigw.policy.search-engine-policy.PolicyContent.NewsCenter": "新闻中央", "apigw.policy.search-engine-policy.PolicyContent.ResultRendering": "结果渲染", "apigw.policy.search-engine-policy.PolicyContent.DefaultLanguage": "默认语言", "apigw.policy.search-engine-policy.PolicyContent.Chinese": "中文", "apigw.policy.search-engine-policy.PolicyContent.English": "英文", "apigw.policy.search-engine-policy.PolicyContent.OutputReferenceSource": "输出引用来源", "apigw.policy.search-engine-policy.PolicyContent.WhenEnabledTheResponseFormat": "开启时，返回的响应格式如下：", "apigw.policy.search-engine-policy.PolicyContent.No": "否", "apigw.policy.search-engine-policy.PolicyContent.Yes": "是", "apigw.policy.search-engine-policy.PolicyContent.ContentType": "内容类型", "apigw.policy.search-engine-policy.PolicyContent.Summary": "摘要", "apigw.policy.search-engine-policy.PolicyContent.Text": "正文", "apigw.policy.search-engine-policy.PolicyContent.ReferenceFormat": "引用格式", "apigw.policy.search-engine-policy.PolicyContent.FillInTheSample": "填写示例", "apigw.policy.search-engine-policy.PolicyContent.ReferenceFormatExample": "引用格式示例", "apigw.policy.search-engine-policy.PolicyContent.FillIn": "填入", "apigw.policy.search-engine-policy.PolicyContent.FormatDescription": "格式说明：", "apigw.policy.search-engine-policy.PolicyContent.SPlaceholderIndicatingTheActual": "%s：占位符，表示实际的引用内容。", "apigw.policy.search-engine-policy.PolicyContent.NALineBreakThat": "\\n：换行符，用于在文本中插入换行。", "apigw.policy.search-engine-policy.SearchEngineAuth.YouHaveNotActivatedThe": "您尚未开通信息查询服务-通用搜索，请先开通。注意，开通后会产生相应费用。", "apigw.policy.search-engine-policy.InternetSearch": "联网搜索", "apigw.policy.search-engine-policy.TheCurrentServiceStatusIs": "当前服务状态暂未开通", "apigw.components.BasicInfo.Port": "端口", "apigw.components.BasicInfo.Agreement": "协议", "apigw.policy.search-engine-policy.PolicyContent.SelectASearchEngine": "请选择搜索引擎", "apigw.policy.search-engine-policy.PolicyContent.TheNumberOfReturnedResults": "返回结果数量不能超过 10", "apigw.policy.search-engine-policy.PolicyContent.TheNumberOfReturnedResults.1": "返回结果数量不能超过 50", "apigw.createApiDialog.ai.ResourceMappingAndOperations": "资源对应及操作", "apigw.plugin-manage.plugin-transfer.OperationAndRouteTransfer.TheRuleConfigurationAlreadyExists": "该接口已存在规则配置", "apigw.plugin-manage.plugin-transfer.OperationAndRouteTransfer.RuleConfigurationAlreadyExistsFor": "该路由已存在规则配置", "apigw.components.EditForm.TheAuthorizationRuleTakesEffect": "授权规则域名级生效，不支持泛域名", "apigw.components.EditForm.IfTheDomainNameMatches": "域名匹配的情况下，名单中的paths不需要校验即可访问，其余都需要校验", "apigw.components.EditForm.IfTheDomainNameMatches.1": "域名匹配的情况下，名单中的paths需要校验，其余可直接访问", "apigw.components.AuthContent.MaximumBytes": "最大字节数：", "apigw.components.domainCertificate.domainSlide.encryptionSuiteCallback": "您已启用最小版本 {min}, 最大版本 {max}，请至少选择一个支持 {res} 的加密算法套件", "apigw.policy.search-engine-policy.PolicyContent.EnterApiKey": "请输入 API Key", "apigw.API.release.is.expected.to.take.seconds": "发布API预计需要 {time} 秒，", "apigw.apiAi.AIModelDebug.Request.ThisApiDoesNotSupport": "该API不支持动态指定模型名称，如需放开可至API配置页面将模型名称修改为“透传”。", "apigw.apiAi.userGuide.ModelSelection": "模型选择", "apigw.components.plugin-manage.UploadCustomPlugin.TheInputMustBeTo": "输入长度需为 2 至 120 个字符，可包含小写字母、数字及字符 \"_\", \"-\", \".\", \"/\"，且分隔符不可在首或尾。", "apigw.components.plugin-manage.UploadCustomPlugin.TheUploadMustBeTo": "请上传长度需为 2 至 120 个字符，可包含小写字母、数字及字符 \"_\", \"-\", \".\", \"/\"，且分隔符不可在首或尾 的 WASM 文件", "apigw.detail.id.CloudNativeApiGateway": "云原生API网关", "apigw.detail.id.ScenarioTemplate": "场景模板", "apigw.regionId.scene-market.CloudNativeApiGateway": "云原生API网关", "apigw.regionId.scene-market.ScenarioTemplate": "场景模板", "apigw.src.sidebar.ScenarioTemplate": "场景模板", "apigw.policiesPlugins.policy.PluginLogGuide.TheServiceStatusIsAbnormal": "服务状态异常，请检查配置", "apigw.policiesPlugins.policy.PluginLogGuide.AnErrorOccurredWithinHours": "6小时内有错误发生", "apigw.policiesPlugins.policy.PluginLogGuide.AiCache": "Ai 缓存", "apigw.policiesPlugins.policy.PluginLogGuide.AiThrottling": "Ai 限流", "apigw.policiesPlugins.policy.PluginLogGuide.InternetSearch": "联网搜索", "apigw.policiesPlugins.policy.PluginLogGuide.ViewLogs": "查看日志", "apigw.policiesPlugins.policy.PluginLogGuide.ErrorContent": "错误内容", "apigw.policy.search-engine-policy.PolicyContent.ReferenceSourceLocation": "引用来源位置", "apigw.policy.search-engine-policy.PolicyContent.Head": "首部", "apigw.policy.search-engine-policy.PolicyContent.Tail": "尾部", "apigw.policy.search-engine-policy.PolicyContent.IntentRecognition": "意图识别", "apigw.policy.search-engine-policy.PolicyContent.WhetherToEnable": "是否开启", "apigw.policy.search-engine-policy.PolicyContent.Enable": "开启", "apigw.policy.search-engine-policy.PolicyContent.Close": "关闭", "apigw.policy.search-engine-policy.PolicyContent.defaultEnable": "未开启自动启用时，可进行参数控制手动启用", "apigw.policy.search-engine-policy.PolicyContent.SupportADetermineWhetherOnline": "支持：a. 判断是否需要进行联网搜索，b.\n                      对联网搜索的query进行改写、拓展，增强搜索能力", "apigw.policy.search-engine-policy.PolicyContent.AiServices": "AI 服务", "apigw.policy.search-engine-policy.PolicyContent.SelectAiService": "请选择 AI 服务", "apigw.policy.search-engine-policy.PolicyContent.ModelName": "模型名称", "apigw.policy.search-engine-policy.PolicyContent.SelectAModelName": "请选择模型名称", "apigw.policy.search-engine-policy.PolicyContent.MaximumNumberOfRegeneratedQueries": "搜索重新生成的最大查询次数", "apigw.policy.search-engine-policy.PolicyContent.TheMaximumNumberOfQueries": "最大查询次数不能超过5次", "apigw.policy.search-engine-policy.PolicyContent.TheMaximumNumberOfQueries.1": "最大查询次数不能低于0次", "apigw.policy.search-engine-policy.PolicyContent.NoteThisCapabilityAlsoConsumes": "注意：此能力也会消耗Token，并且 不会 在模型调用监控中被统计", "apigw.components.ObserveSide.AccessLogDeliveryConfiguration": "访问日志投递配置", "apigw.components.ObserveSide.PlugInLogDelivery": "插件日志投递", "apigw.components.ObserveSide.GatewayAccessLogAccesslog": "网关访问日志 (AccessLog)", "apigw.components.ObserveSide.TheOutputLogIsCompatible": "输出日志兼容 Nginx Ingress 格式", "apigw.components.ObserveSide.PlugInLogs": "插件日志", "apigw.apiAi.AIModelDebug.ReasoningOutput.Reasoning": "推理中...", "apigw.apiAi.AIModelDebug.ReasoningOutput.InferenceCompletedTimeDurationS": "推理完成（用时：{duration}s）", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.OnlyApisPublishedToThis": "仅展示发布至此网关的API", "apigw.consumer-manage.consumer-auth.tableProps.TheCurrentAiApiIs": "当前AI API 未发布至网关实例 {gatewayName} / {gatewayId}，此条授权规则暂不生效", "apigw.src.appConfig.EastChinaFinancialCloud": "华东 2 金融云", "apigw.security.authority.Configs.SelectAnAuthenticationType": "请选择鉴权类型", "apigw.security.authority.Configs.SelectAuthenticationStatus": "请选择鉴权状态", "apigw.CreateAiApiSidePanel.components.ServiceSelect.FunctionCalculation": "函数计算", "apigw.CreateAiApiSidePanel.components.ServiceSelect.SaeKSService": "SAE K8s 服务", "apigw.components.overview.ResourceListSync.NoDataYet": "暂未数据", "apigw.shared.AuthMessage.BaseAuthMessage.PleaseAssignThePrimaryAccount": "请主账号或当前资源所属人赋予该账号包含", "apigw.shared.AuthMessage.BaseAuthMessage.Permissions": "的权限", "apigw.api-manage.apiList.ApiListTableProps.ThisApiIsCreatedWith": "该API在实例外创建,请前往", "apigw.api-manage.apiList.ApiListTableProps.ListOfRealExceptionApis": "实例外API列表", "apigw.api-manage.apiList.DeleteApiAction.PleaseWaitAMomentIt": "请稍等，正在查验中", "apigw.api-manage.components.apiBasic.DomainName": "域名", "apigw.api-manage.components.constants.InterfaceLevelConfigurationOverwritesThe": "接口级配置会覆盖API默认后端服务配置", "apigw.api-manage.components.constants.RouteLevelConfigurationOverwritesThe": "路由级配置会覆盖API默认后端服务配置", "apigw.components.BackendServices.AdvancedSetPolicy.AdvancedConfiguration": "高级配置", "apigw.components.BackendServices.SceneAndService.ConfigureInterfacesSeparately": "单独为接口配置", "apigw.components.BackendServices.SceneAndService.InterfacesAreAssociatedWithBackend": "接口单独关联后端服务，接口配置的后端服务优先于API级", "apigw.components.BackendServices.SceneAndService.InheritFromApi": "继承自API", "apigw.components.BackendServices.SceneAndService.DoNotAssociateServicesSeparately": "不单独在接口处关联服务，继承API所关联的后端服务", "apigw.components.BackendServices.SceneAndService.YouHaveNotConfiguredApi": "该实例中您尚未配置API级后端服务，请", "apigw.components.BackendServices.SceneAndService.GoToConfiguration": "前往配置", "apigw.components.BackendServices.SceneAndService.NoDefaultServiceIsAvailable": "暂无默认服务", "apigw.components.BackendServices.SceneAndService.YouCanConfigureApiLevel": "您可以配置API级默认服务（注意：路由配置优先级高于API级）", "apigw.components.BackendServices.SceneAndService.GoToConfigureApiLevel": "前往配置API级默认服务", "apigw.components.BackendServices.SceneAndService.NoBackendServices": "暂无后端服务", "apigw.components.BackendServices.SceneAndService.NoServiceIsAvailablePlease": "暂无服务，请发布API并添加服务", "apigw.components.BackendServices.SceneAndService.NoBackendServicesAreAvailable": "暂无后端服务，在", "apigw.components.BackendServices.SceneAndService.EditApi": "编辑API", "apigw.components.BackendServices.SceneAndService.Or": "或", "apigw.components.BackendServices.SceneAndService.EditInterface": "编辑接口", "apigw.components.BackendServices.SceneAndService.EditRoute": "编辑路由", "apigw.components.BackendServices.SceneAndService.AddBackendServices": "中，添加后端服务", "apigw.components.BackendServices.SceneAndService.ConfigurationMethod": "配置方式", "apigw.components.BackendServices.PleaseNote": "请注意", "apigw.components.BackendServices.ModifyingTheDefaultBackendService": "修改API默认后端服务，会影响全部选择「继承自API」的接口。", "apigw.createApiDialog.components.DomainSelect.TheDomainNameCannotBe": "域名不能为空", "apigw.createApiDialog.components.GatewaySelect.TheInstanceToWhichThe": "所属实例不能为空", "apigw.createApiDialog.components.GatewaySelect.YouCanSelectMultipleInstances": "您可以选择多个所属实例", "apigw.createApiDialog.components.GatewaySelect.SelectTheInstanceToWhich": "选择API所属实例，可在下方配置API级默认后端服务。", "apigw.createApiDialog.components.GatewaySelect.ClickConfigureDefaultServiceFor": "点击为新增实例配置默认服务", "apigw.components.createApiDialog.AfterTheApiIsModified": "API修改后，需要进行一次API级别的发布，才可以进行路由级别发布。", "apigw.components.createApiDialog.ApiDefaultBackendService": "API 默认后端服务", "apigw.components.createApiDialog.YouCanConfigureDifferentDefault": "您可以为不同实例配置不同的默认后端服务", "apigw.components.publicDialogs.gatewayIn.ApiLevel": "API级", "apigw.components.publicDialogs.gatewayIn.ApiDefaultBackendServiceOptional": "API 默认后端服务(可选)", "apigw.components.publicDialogs.gatewayIn.TheBackendServiceHasNot": "当前所选实例尚未配置后端服务,请为关联API配置后端服务", "apigw.components.publicDialogs.gatewayIn.EnterAnInterfaceNameTo": "请输入接口名称进行搜索", "apigw.components.publicDialogs.gatewayIn.PublishInterface": "发布接口", "apigw.components.publicDialogs.gatewayIn.InterfaceLevel": "接口级", "apigw.components.publicDialogs.gatewayIn.Instance": "实例", "apigw.components.publicDialogs.gatewayIn.ReleaseScope": "发布范围", "apigw.components.publicDialogs.gatewayIn.AffectedInterface": "受影响接口", "apigw.components.publicDialogs.gatewayIn.TheFollowingInterfacesAreRepublished": "以下接口会被重新发布", "apigw.components.publicDialogs.gatewayOut.SelectADomainName": "请选择域名", "apigw.components.publicDialogs.Publish": "发布", "apigw.components.publicDialogs.Cancel": "取消", "apigw.api-manage.headBtn.PleaseWaitAMomentIt": "请稍等，正在查验中", "apigw.api-manage.headBtn.Publish": "发布", "apigw.api-manage.headBtn.offline.InterfaceName": "接口名称", "apigw.api-manage.headBtn.offline.RouteName": "路由名称", "apigw.api-manage.headBtn.offline.AfterTheInterfaceIsOffline": "接口下线后，该接口在指定实例无法访问，请确保已告知用户。", "apigw.api-manage.headBtn.offline.AfterTheRouteIsOffline": "路由下线后，该路由在指定实例无法访问，请确保已告知用户。", "apigw.components.api-manage.InterfaceList": "接口列表", "apigw.components.api-manage.RouteList": "路由列表", "apigw.components.api-manage.ApiPolicyConfiguration": "API策略配置", "apigw.components.api-manage.TheDefaultApiLevelPolicy": "API级默认策略配置，优先级低于接口级。", "apigw.components.api-manage.TheDefaultApiLevelPolicy.1": "API级默认策略配置，优先级低于路由级。", "apigw.components.api-manage.ApiMonitoring": "API监控", "apigw.api-manage.interfaceList.ApiOperationsContent.NoRouteIsCurrentlyAvailable": "当前暂无路由，请创建路由", "apigw.api-manage.interfaceList.ApiOperationsContent.NoRouteIsCurrentlyFound": "当前未查询到路由", "apigw.api-manage.interfaceList.ApiOperationsContent.NoInterfaceIsCurrentlyAvailable.1": "当前暂无接口，请添加接口", "apigw.api-manage.interfaceList.ApiOperationsContent.NoInterfaceIsCurrentlyQueried": "当前未查询到接口", "apigw.api-manage.interfaceList.ApiOperationsContent.InterfaceDetails": "接口详情", "apigw.api-manage.interfaceList.ApiOperationsContent.RouteDetails": "路由详情", "apigw.api-manage.interfaceList.ApiOperationsContent.PolicyConfiguration": "策略配置", "apigw.api-manage.interfaceList.ApiOperationsContent.RouteLog": "路由日志", "apigw.api-manage.interfaceList.ApiOperationsContent.InterfaceMonitoring": "接口监控", "apigw.api-manage.interfaceList.ApiOperationsContent.RouteMonitoring": "路由监控", "apigw.api-manage.interfaceList.ApiOperationsContent.AnApiRepresentsASet": "API 代表具有相同 Base Path 前缀的接口集合；接口代表特定的执行动作和路径。", "apigw.api-manage.interfaceList.ApiOperationsContent.HttpApiIsACollection": "HTTP API 是一组路由的集合，每个路由负责将特定请求映射到对应的后端服务。", "apigw.api-manage.interfaceList.ApiOperationsMenu.TheCurrentApiHasChanged": "当前API存在变更,请重新发布", "apigw.api-manage.interfaceList.ApiOperationsMenu.Modified": "已修改", "apigw.api-manage.interfaceList.ApiOperationsMenu.NotPublished": "未发布", "apigw.api-manage.interfaceList.ApiOperationsMenu.Published": "已发布", "apigw.api-manage.interfaceList.ApiOperationsMenu.Publishing": "发布中", "apigw.api-manage.interfaceList.ApiOperationsMenu.PublishingFailed": "发布失败", "apigw.api-manage.interfaceList.ApiOperationsMenu.Offline": "下线中", "apigw.api-manage.interfaceList.ApiOperationsMenu.Offline.1": "已下线", "apigw.api-manage.interfaceList.ApiOperationsMenu.OfflineFailed": "下线失败", "apigw.api-manage.interfaceList.ApiOperationsMenu.Routing": "路由", "apigw.api-manage.interfaceList.ApiOperationsMenu.NoInterface": "暂无接口", "apigw.api-manage.interfaceList.ApiOperationsMenu.NoRouteIsAvailable": "暂无路由", "apigw.api-manage.interfaceList.ApiOperationsSearch.SearchForRouteNames": "搜索路由名称", "apigw.components.api-operations-action-button.CreateOrEditRouterOperation.CreateARoute": "创建路由", "apigw.components.api-operations-action-button.DeleteOperation.AreYouSureYouWant.1": "确定要删除该路由吗？", "apigw.components.api-operations-action-button.RoutePublish.Ok": "确定在实例", "apigw.components.api-operations-action-button.RoutePublish.Publish": "发布", "apigw.components.api-operations-action-button.RoutePublish.IsIt": "吗？", "apigw.components.api-operations-policy.DeletePlugin.ApiLevelPoliciesCannotBe": "接口不允许删除API级策略", "apigw.components.api-operations-policy.DeletePlugin.ApiLevelPoliciesCannotBe.1": "路由不允许删除API级策略", "apigw.components.api-operations-policy.DeletePolicy.ApiLevelPoliciesCannotBe.1": "接口不允许删除API级策略", "apigw.components.api-operations-policy.DeletePolicy.ApiLevelPoliciesCannotBe.2": "路由不允许删除API级策略", "apigw.components.api-operations-policy.NoDefaultServiceIsAvailable": "暂无默认服务", "apigw.components.api-operations-policy.YouCanConfigureApiLevel": "您可以配置API级默认服务（注意：接口级配置优先级高于API级）", "apigw.components.api-operations-policy.GoToConfigureApiLevel": "前往配置API级默认服务", "apigw.components.api-operations-policy.NoBackendServices": "暂无后端服务", "apigw.components.api-operations-policy.PleasePublishTheApiFirst": "请先发布API", "apigw.components.api-operations-policy.HttpApiThereIsNo": "HTTP API 无默认服务，请前往路由中配置", "apigw.components.api-operations-policy.NoBackendServicesAreAvailable": "暂无后端服务，在", "apigw.components.api-operations-policy.EditApi": "编辑API", "apigw.components.api-operations-policy.OrAddABackendService": "或接口详情中，添加后端服务", "apigw.components.api-operations-slide.CreateOrEditSlide.ChangesToBasicAndDefinition": "基本信息和定义信息的变更会影响该接口在所有实例的状态", "apigw.components.api-operations-slide.ResourceDetails.ParameterDefinition": "参数定义", "apigw.components.api-operations-slide.ResourceDetails.MatchingRules": "匹配规则", "apigw.components.api-operations-slide.ResourceDetails.BackendServices": "后端服务", "apigw.components.api-operations-slide.ResourceDetails.InheritFromApi": "继承自API", "apigw.api-operations-slide.api-route-info.RouteMatchRuleInfo.DomainName": "域名", "apigw.api-operations-slide.api-route-info.RouteMatchRuleInfo.Path": "路径 (Path)", "apigw.components.api-resource-acitons.ResourceContentActions.Debugging": "调试", "apigw.components.api-resource-acitons.ResourceContentActions.MoreOperations": "更多操作", "apigw.components.api-resource-acitons.ResourceContentActions.Offline": "下线", "apigw.components.api-resource-acitons.ResourceContentActions.Delete": "删除", "apigw.components.api-resource-acitons.ResourceLIstActions.TheDefaultApiLevelAnd": "当前所属实例路由的默认API级及接口级未配置后端服务", "apigw.components.api-resource-acitons.ResourceLIstActions.AfterTheApiIsModified": "API修改后，需要进行一次API级别的发布，才可以进行路由级别发布。", "apigw.components.api-resource-acitons.ResourceLIstActions.Debugging": "调试", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.RouteLevelConsumerAuthentication": "路由级消费者认证", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.AfterYouChangeTheConsumer": "变更消费者认证配置后，需重新发布才可生效。", "apiw.apimanagement.components.gatewaySelect.offline.tip": "当前实例{gatewayName}已发布{apiType}，请先下线", "apigw.apimanagement.components.gatewaySelect.gateway.add.tip": "所有{apiType}在新增实例上，默认继承API级后端服务。", "apigw.api-manage.interfaceList.ApiOperationsMenu.TheCurrentApiHasChanged.1": "API/{apiType}定义信息全局唯一，变更会影响其在所有实例的状态，需重新发布才可生效", "apigw.components.api-resource-acitons.ResourceContentActions.Publish.noServiceTip": "API修改后，需要进行一次API级别的发布，才可以进行{apiType}级别发布。", "apigw.components.api-resource-acitons.ResourceContentActions.Publish.modifyTip": "API修改后，需要进行一次API级别的发布，才可以进行{apiType}级别发布。", "apigw.components.api-resource-acitons.ResourceContentActions.delete.offlineFirst.tip": "请先下线所有实例下的当前{apiType}", "apigw.api-manage.interfaceList.resourceStatus.processing": "当前所属实例{apiType}正在发布中", "apigw.api-manage.interfaceList.resourceStatus.offlining": "当前所属实例{apiType}正在下线中", "apigw.api-manage.interfaceList.resourceStatus.notPublish": "当前所属实例{apiType}暂未发布", "apigw.api-manage.interfaceList.resourceStatus.failed": "当前所属实例{apiType}发布失败", "apigw.ai-gateway.mcp-server.CreateAnMppService": "创建 MCP 服务", "apigw.ai-gateway.mcp-server.SearchForAServiceName": "搜索MCP服务名称", "apigw.ai-gateway.mcp-server.PleaseSelect": "请选择", "apigw.ai-gateway.mcp-server.PublishingStatus": "发布状态", "apigw.ai-gateway.mcp-server.All": "全部", "apigw.ai-gateway.mcp-server.Published": "已发布", "apigw.ai-gateway.mcp-server.NotPublished": "未发布", "apigw.mcp-server-action.create-edit.SlideContent.Name": "名称", "apigw.mcp-server-action.create-edit.SlideContent.TheNameIsUniqueAnd": "名称唯一，支持英文、数字、短横线“-”，不超过64个字符，且不能以“-”开头或结尾", "apigw.mcp-server-action.create-edit.SlideContent.TheNameOfTheMcp": "MCP名称不能为空", "apigw.mcp-server-action.create-edit.SlideContent.TheNameOfTheMcp.1": "MCP名称不符合规范", "apigw.mcp-server-action.create-edit.SlideContent.EnterAServiceName": "请输入MCP服务名称", "apigw.mcp-server-action.create-edit.SlideContent.TheDescriptionCannotBeEmpty": "描述不能为空", "apigw.mcp-server-action.create-edit.SlideContent.McpAccessPoint": "MCP接入点", "apigw.mcp-server-action.create-edit.SlideContent.DomainName": "域名", "apigw.mcp-server-action.create-edit.SlideContent.PathSse": "路径 (SSE)", "apigw.mcp-server-action.create-edit.SlideContent.PathSseRuleMcpServers": "路径 (SSE) 规则为：/mcp-servers/{服务名称}/sse", "apigw.mcp-server-action.create-edit.SlideContent.PathStreamableHttp": "路径 (Streamable HTTP)", "apigw.mcp-server-action.create-edit.SlideContent.ThePathStreamableHttpRule": "路径 (Streamable HTTP) 规则为：/mcp-servers/{服务名称}", "apigw.mcp-server-action.create-edit.SlideContent.PleaseCompleteTheBackendService": "请完善后端服务信息", "apigw.mcp-server-action.create-edit.SlideContent.ConsumerCertification": "消费者认证", "apigw.mcp-server-action.create-edit.CreateAnMppService": "创建 MCP 服务", "apigw.mcp-server-action.create-edit.SaveAndPublish": "保存并发布", "apigw.mcp-server-action.delete.Delete": "删除", "apigw.mcp-server-action.offline.Offline": "下线", "apigw.mcp-server-action.publish.Publish": "发布", "apigw.mcp-server.mcp-server-details.Swagger.FormatParsingFailedCheckWhether": "格式解析失败，请查看内容是否正确", "apigw.mcp-server.mcp-server-details.Swagger.GeneratedSuccessfully": "生成成功", "apigw.mcp-server.mcp-server-details.Swagger.StepImportSwaggerToCreate": "Step 1. 导入 Swagger，创建或增量更新 MCP 工具", "apigw.mcp-server.mcp-server-details.Swagger.GenerateNow": "立即生成", "apigw.mcp-server.mcp-server-details.Swagger.StepUpdateAndConfirmThe": "Step 2，更新及确认 MCP 工具列表", "apigw.mcp-server.mcp-server-details.Swagger.ListOfToolsCorrespondingTo": "当前 Swagger 文件对应的工具列表", "apigw.mcp-server.mcp-server-details.Swagger.TheFinalListOfMcp": "最终 MCP 工具列表（可手动修改）", "apigw.mcp-server.mcp-server-details.auth.BackendServiceAuthentication": "后端服务认证", "apigw.mcp-server.mcp-server-details.auth.AuthenticationType": "认证类型", "apigw.mcp-server.mcp-server-details.auth.Credential": "凭证", "apigw.mcp-server.mcp-server-details.auth.CredentialLocation": "凭证位置", "apigw.mcp-server.mcp-server-details.auth.Add": "添加", "apigw.mcp-server.mcp-server-details.custom-yaml.ListOfMcpsTools": "MCP 工具列表", "apigw.mcp-server.mcp-server-details.custom-yaml.YamlExample": "YAML 示例", "apigw.mcp-server.mcp-server-details.example.ServerNameRestAmapServer": "server:\n  name: rest-amap-server\n  config:\n    api<PERSON>ey: your-api-key-here\ntools:\n- name: maps-geo\n  description: \"将详细的结构化地址转换为经纬度坐标。支持对地标性名胜景区、建筑物名称解析为经纬度坐标\"\n  args:\n  - name: address\n    description: \"待解析的结构化地址信息\"\n    type: string\n    required: true\n  - name: city\n    description: \"指定查询的城市\"\n    type: string\n    required: false\n  - name: output\n    description: \"输出格式\"\n    type: string\n    enum: [\"json\", \"xml\"]\n    default: \"json\"\n  requestTemplate:\n    url: \"https://restapi.amap.com/v3/geocode/geo\"\n    method: GET\n    argsToUrlParam: true\n    headers:\n    - key: x-api-key\n      value: \"{{.config.apiKey}}\"\n  responseTemplate:\n    body: |\n      # 地理编码信息\n      {{- range $index, $geo := .geocodes }}\n      ## 地点 {{add $index 1}}\n\n      - **国家**: {{ $geo.country }}\n      - **省份**: {{ $geo.province }}\n      - **城市**: {{ $geo.city }}\n      - **城市代码**: {{ $geo.citycode }}\n      - **区/县**: {{ $geo.district }}\n      - **街道**: {{ $geo.street }}\n      - **门牌号**: {{ $geo.number }}\n      - **行政编码**: {{ $geo.adcode }}\n      - **坐标**: {{ $geo.location }}\n      - **级别**: {{ $geo.level }}\n      {{- end }}", "apigw.mcp-server.mcp-server-details.Edit": "编辑", "apigw.mcp-server.mcp-server-details.Tools": "工具", "apigw.mcp-server.mcp-server-details.EditingTools": "编辑工具", "apigw.mcp-server.mcp-server-details.CurrentlyNoToolsAreAvailable": "当前暂无工具，请添加", "apigw.mcp-server.mcp-server-details.AddTools": "添加工具", "apigw.mcp-server.mcp-server-details.Parameter": "参数", "apigw.mcp-server.mcp-server-details.ConsumerCertification": "消费者认证", "apigw.mcp-server.mcp-server-details.ConnectToTheMcpService": "连接 MCP 服务", "apigw.mcp-server.mcp-server-details.AgentClient": "Agent 客户端", "apigw.mcp-server.mcp-server-details.StepGenerateAUrl": "Step 1. 生成 URL", "apigw.mcp-server.mcp-server-details.DomainName": "域名", "apigw.mcp-server.mcp-server-details.ForTestingOnly": "仅供测试", "apigw.mcp-server.mcp-server-details.CopiedSuccessfully": "复制成功", "apigw.mcp-server.mcp-server-details.Copy": "复制", "apigw.mcp-server.mcp-server-details.StepDomainNameDnsMapping": "Step 2. 域名 DNS 映射", "apigw.mcp-server.mcp-server-details.TheServiceDomainNameNeeds": "需要将业务域名在 DNS 服务中 CNAME 至网关访问入口地址", "apigw.mcp-server.mcp-server-details.GatewayAccessAddress": "网关访问地址：", "apigw.mcp-server.mcp-server-details.PublicNetwork": "公网：", "apigw.mcp-server.mcp-server-details.PrivateNetwork": "私网：", "apigw.mcp-server.mcp-server-details.SwaggerBasedFiles": "基于Swagger文件", "apigw.mcp-server.mcp-server-details.UploadSwaggerFilesToAutomatically": "通过上传 Swagger 文件自动生成 MCP 工具", "apigw.mcp-server.mcp-server-details.CustomYaml": "自定义YAML", "apigw.mcp-server.mcp-server-details.ManuallyCreateAnMcpsTool": "手动创建 MCP 工具", "apigw.mcp-server.mcp-server-details.CreateTools": "创建工具", "apigw.mcp-server.mcp-server-details.SetCredentialInformation": "请设置凭证信息", "apigw.mcp-server.mcp-server-details.CreatedSuccessfully": "创建成功", "apigw.mcp-server.mcp-server-details.UpdatedSuccessfully": "更新成功", "apigw.mcp-server.mcp-server-details.EditMethod": "编辑方式", "apigw.mcp-server.mcp-server-details.request-dev.GenerateAnMppToolBased": "基于请求结果生成MCP工具", "apigw.mcp-server.mcp-server-details.request-dev.StepUpdateAndConfirmThe": "Step 2，更新及确认 MCP 工具列表", "apigw.mcp-server.mcp-server-details.request-dev.ListOfCurrentMcpsTools": "当前 MCP 工具列表", "apigw.mcp-server.mcp-server-details.request-dev.TheFinalListOfMcp": "最终 MCP 工具列表（可手动修改）", "apigw.mcp-server.mcp-server-list.McpServerCard.Modified": "已修改", "apigw.mcp-server.mcp-server-list.McpServerCard.NotPublished": "未发布", "apigw.mcp-server.mcp-server-list.McpServerCard.Published": "已发布", "apigw.mcp-server.mcp-server-list.McpServerCard.Publishing": "发布中", "apigw.mcp-server.mcp-server-list.McpServerCard.PublishingFailed": "发布失败", "apigw.mcp-server.mcp-server-list.McpServerCard.Offline": "下线中", "apigw.mcp-server.mcp-server-list.McpServerCard.Offline.1": "已下线", "apigw.mcp-server.mcp-server-list.McpServerCard.OfflineFailed": "下线失败", "apigw.mcp-server.mcp-server-list.McpServerCard.Details": "详情", "apigw.mcp-server.mcp-server-list.AllServices": "全部服务", "apigw.mcp-server.mcp-server-list.NoServiceAvailable": "暂无服务", "apigw.mcp-server.mcp-server-list.CurrentlyNoServiceIsAvailable": "当前暂无服务，请先创建服务", "apigw.mcp-server.mcp-server-list.CreateAnMppService": "创建 MCP 服务", "apigw.utils.hooks.useAiGatewayDetails.McpsManagement": "MCP 管理", "apigw.utils.hooks.useAiGatewayDetails.Service": "服务", "apigw.utils.hooks.useAiGatewayDetails.PlugIn": "插件", "apigw.api-manage.apiList.ApiListTable.CreateLlmApi": "创建LLM API", "apigw.api-manage.apiList.ApiListTableProps.TheInstanceAccessPortalIs.1": "实例访问入口仅供调试使用，若多个LLM API共用同一个实例访问入口，则无法保证LLM API访问正常。请使用自定义域名。", "apigw.create-actions.CreateAiApiSidePanel.BasicInfoForm.SelectADomainName": "请选择域名", "apigw.create-actions.CreateAiApiSidePanel.DomainListForm.SelectADomainName": "请选择域名", "apigw.CreateAiApiSidePanel.ServiceConfigs.ServiceQueryConfig.JudgmentCondition": "判断条件", "apigw.CreateAiApiSidePanel.ServiceConfigs.ServiceQueryConfig.JudgmentRules": "判断规则", "apigw.CreateAiApiSidePanel.ServiceConfigs.ServiceQueryConfig.ModelService": "模型服务", "apigw.create-actions.CreateAiApiSidePanel.CreateLlmApi": "创建LLM API", "apigw.create-actions.CreateAiApiSidePanel.EditLlmApi": "编辑LLM API", "apigw.components.api-operations-policy.PolicyEnvList.AllInstances": "全部实例", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectAServiceForMcps": "选择MCP服务", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.Mcps": "MCP 服务", "apigw.consumer-manage.consumer-auth.McpTransfer.EnterAServiceNameTo": "请输入MCP服务名称进行搜索", "apigw.consumer-manage.consumer-auth.McpTransfer.OptionalMcps": "可选MCP服务", "apigw.consumer-manage.consumer-auth.McpTransfer.SelectedMcps": "已选MCP服务", "apigw.consumer-auth.api-consumer-auth.ApiConsumerTableProps.Mcps": "MCP 服务", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.ApiLevelConsumerAuthentication": "API 级消费者认证", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthConfig.ConsumerCertificationForMcps": "MCP 服务消费者认证", "apigw.consumer-auth.api-consumer-auth.ConsumerAuthSlide.Mcps": "MCP 服务", "apigw.consumer-manage.consumer-auth.tableProps.ServiceName": "服务名称", "apigw.consumer-manage.consumer-auth.tableProps.Current": "当前", "apigw.consumer-manage.consumer-auth.tableProps.Mcps": "MCP 服务", "apigw.consumer-manage.consumer-auth.tableProps.ConsumerAuthenticationIsNotEnabled.2": "未开启消费者认证，授权暂不生效", "apigw.consumer-manage.consumer-auth.tableProps.TheCurrentLlmApiIs": "当前 LLM API 未发布至网关实例", "apigw.consumer-manage.consumer-auth.tableProps.ThisAuthorizationRuleDoesNot": "，此条授权规则暂不生效", "apigw.components.overview.ResourceListSync.LlmApiPublishedTotal": "LLM API（已发布/总数）", "apigw.components.overview.ResourceListSync.McpsPublishedTotal": "MCP 服务（已发布/总数）", "apigw.plugin-manage.plugin-rule-config.RuleScopeMenu.TheEffectivePriorityDecreasesFrom.1": "生效优先级自域名级向下递减", "apigw.plugin-manage.plugin-rule-config.RuleScopeMenu.TheEffectivePriorityDecreasesFrom.2": "生效优先级自路由级向下递减", "apigw.shared.GatewayTypeValidate.Prompt": "提示", "apigw.shared.GatewayTypeValidate.TheCurrentInstanceIsDetected": "检测到当前实例为 {gatewayType} 实例， 请前往 {gatewayType} 实例管理页进行操作", "apigw.shared.GatewayTypeValidate.GoToTheGatewaytypeInstance": "前往 {gatewayType} 实例管理页", "apigw.shared.hooks.useSetTitle.ApiGateway": "API 网关", "apigw.src.constants.apiManage.MultiModelServiceByRequest": "多模型服务  (按请求特征)", "apigw.containers.AppLayout.AiGateway": "AI 网关", "apigw.api-ai.apiId.ConsumerCertification": "消费者认证", "apigw.plugin-manage.id.AiGateway": "AI 网关", "apigw.src.sidebar.ApiGateway": "API 网关", "apigw.src.sidebar.CloudNativeApiGateway": "云原生API网关", "apigw.src.sidebar.AiGateway": "AI 网关", "apigw.mcp-server-action.edit.CreateAnMppService": "编辑 {name} 服务", "apigw.policy.security-guard-policy.PolicyContent.ContentSecurity": "内容安全", "apigw.policy.security-guard-policy.PolicyContent.AiSafetyFencePublicTest": "AI 安全护栏", "apigw.createApiDialog.apiPreCheck.DomainNamesAndBackendServices": "会覆盖域名和后端服务", "apigw.envAndBackendServices.backendServices.Item.YouMustFirstSelectThe": "需先选择所属实例", "apigw.consumer-manage.consumer-auth.ConsumerApiSidePanel.SelectTheMcpsToBe": "请选择要授权的MCP服务", "apigw.api-manage.apiList.ApiListTable.NoApiIsAvailable": "暂无API，请", "apigw.components.AiModel.Workspace": "工作空间", "apigw.components.AiModel.TheWorkspaceCannotBeEmpty": "工作空间不能为空", "apigw.components.AiModel.SelectAWorkspace": "请选择工作空间", "apigw.components.AiModel.EasService": "EAS服务", "apigw.components.AiModel.EasServiceCannotBeEmpty": "EAS服务不能为空", "apigw.components.AiModel.SelectEasService": "请选择EAS服务", "apigw.components.AiModel.ConnectionType": "连接类型", "apigw.components.AiModel.TheConnectionTypeCannotBe": "连接类型不能为空", "apigw.components.AiModel.PublicNetwork": "公网", "apigw.components.AiModel.PrivateNetwork": "私网（推荐）", "apigw.components.AiModel.SelectAConnectionType": "请选择连接类型", "apigw.components.AiModel.AutomaticallyObtained": "已自动获取", "apigw.policy.retry-content.RetryIntervalSeconds": "重试间隔（秒）", "apigw.route.rule.validate.placeholder": "请配置路由匹配规则", "apigw.llm.api.default.route": "默认路由 (只读)", "apigw.ai.llm.api.protocol.openai": "OpenAI 兼容", "apigw.ai.llm.api.protocol.openai.desc": "OpenAI 兼容协议下，LLM API 默认包含以下三条路由：", "apigw.consumer.jwt.remote.uri.placeholder": "请输入请求 URL，例如: www.xxx.com/xx", "apigw.consumer.jwt.remote.jwtType.local": "本地配置", "apigw.consumer.jwt.remote.jwtType.origin": "远程获取", "apigw.consumer.jwt.remote.jwtType.label": "创建方式", "apigw.consumer.jwt.remote.timeout.title": "超时时间 (ms)", "apigw.consumer.jwt.remote.ttl.title": "缓存时间 (ms)", "apigw.components.shared.JwtForm.YouMustStartWithHttp": "需要以http://或https://协议开头", "apigw.components.ObserveSide.Opening": "开启中", "apigw.components.ObserveSide.FailedToEnable": "开启失败", "apigw.components.ObserveSide.MetricsDeliveryConfiguration": "Metrics投递配置", "apigw.components.ObserveSide.MetricsDelivery": "Metrics投递", "apigw.components.ObserveSide.EnableCloudlensForApigFor": "为您开通Cloudlens for APIG并开启指标投递，监控指标将投递到您账户下的云监控Prometheus实例中，已Prometheus按量付费方式进行统一计费。", "apigw.components.ObserveSide.TermsOfService": "服务条款", "apigw.components.ObserveSide.FeeDetails": "费用详情", "apigw.components.ObserveSide.PrometheusInstances": "Prometheus实例", "apigw.mcp-server.mcp-server-details.BasicInfo.CustomHttpHeaderTest": "自定义 HTTP Header: test", "apigw.mcp-server.mcp-server-details.BasicInfo.CustomHttpHeader": "自定义 HTTP Header", "apigw.policiesPlugins.plugin.Agent": "智能体Agent", "apigw.components.plugin-manage.UploadCustomPlugin.PleaseFillInTheVersion": "请填写版本号", "apigw.components.plugin-manage.UploadCustomPlugin.TheVersionNumberOfThe": "新上传插件版本号需高于旧版本 {_maxVersion}", "apigw.plugin-manage.plugin-actions.ChangePluginVersion.SelectVersion": "选择版本", "apigw.plugin-manage.plugin-actions.ChangePluginVersion.VersionChange": "版本变更", "apigw.plugin-manage.plugin-actions.ChangePluginVersion.VersionChangeTip": "确认将版本从{originVersion}变更至{newVersion}", "apigw.shared.PluginCardList.PluginCard.SelectThePlugInVersion": "选择要删除的插件版本", "apigw.shared.PluginCardList.PluginCard.SelectVersion": "选择版本", "apigw.shared.PluginCardList.PluginCard.ConfirmToDeleteTheSpecified": "确认删除插件指定版本", "apigw.shared.PluginCardList.PluginCard.ConfirmToDeleteThePlug.1": "确认删除插件", "apigw.shared.PluginCardList.PluginCard.TheCurrentOperationIsDeleting": "当前操作正在删除该插件所有版本，是否确认删除该插件？", "apigw.plugin-manage.$id.Version": "版本", "apigw.plugin-manage.plugin-actions.ChangePluginVersion.ThisPlugInIsA": "该插件为自定义插件，如需平滑升级需要确保变更版本与原版本规则配置兼容。", "apigw.plugin-manage.plugin-actions.ChangePluginVersion.ConfirmTheVersionChange": "确认变更版本", "apigw.components.plugin-manage.UploadCustomPlugin.SupportsNumbersAndPoints": "支持数字和点", "apigw.plugin-manage.plugin-actions.ChangePluginVersion.PleaseSelectAVersionNumber": "请选择版本号", "apigw.shared.PluginCardList.PluginCard.PleaseSelectTheVersionNumber": "请选择要删除的版本号", "apigw.shared.PluginCardList.configHook.LlmApiLevelPlugIn": "LLM API级插件规则", "apigw.shared.PluginCardList.configHook.OnTheLlmApiThe": "作用在LLM API上；当请求匹配到指定LLM API的任意接口时，规则生效。", "apigw.shared.PluginCardList.configHook.RoutingPlugInRules": "路由级插件规则", "apigw.shared.PluginCardList.configHook.TheRuleTakesEffectWhen.1": "作用在MCP上；当请求匹配到指定MCP的任意路由时，规则生效。", "apigw.policiesPlugins.plugin.AllRoutes": "全部路由", "apigw.plugin-manage.plugin-actions.ChangePluginVersion.ThisPlugInIsAn": "该插件为官方插件，如需平滑升级需要确保变更版本与原版本规则配置兼容。", "apigw.shared.PluginCardList.PluginInstall.PlugInInstalled": "已安装插件{bindVersion}版本，平滑升级需确保变更版本与原版本规则配置兼容", "apigw.plugin-manage.plugin-transfer.InstanceTransfer.TheCurrentPlugInIs": "已安装当前插件", "apigw.api-manage.headBtn.DeleteVersion": "删除版本", "apigw.create-actions.CreateModelApiSidePanel.SceneDialog.InThisScenarioYouNeed": "该场景需要网关引擎升级到 2.1.6 及以上版本"}