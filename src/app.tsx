import React, { useState } from 'react';
import './virtual:windi.css';
// import './utils/ydIntlInitiator';
// import './utils/emitter';
import './utils/globalLib';
import { Route, Switch } from 'react-router-dom';
import GatewayDetail from './pages/$regionId/gateway/$id';
import SaveToken from './components/shared/DevToken';
import './styles/index.less';
import PreEnvSelector from './components/shared/PreEnvSelector';
import { IS_PRE, IS_PRE_OR_LOCAL } from './constants';
import {
  useAuth,
  useMaintaining,
  useMockUser,
  useRegionGray,
  useWhiteList,
} from './components/auth/useAuth';
import Auth from './pages/auth';
import { Loading } from '@ali/cnd';
import NotAllowed from './pages/notAllowed';
import { OpAnnouncement } from './components/shared/OpAnnouncement';
import Maintaining from './pages/maintaining';
import { MockUser } from './components/shared/MockUser';
import { MultiVersionSelector } from './components/shared/MultiVersionSelector';
import { MessengerRegionBar } from './components/shared/MessengerRegionBar';

import MessengerResourceGroupBar from './components/shared/MessengerResourceGroupBar';
import { CommonErrorBoundary } from './components/shared/ErrorFallback';
import AppProvider from './components/app-provider';
import AiGatewayDetail from './pages/$regionId/ai-gateway/$id';
import CachedData from './utils/cacheData';
import DocQuickStartWidget from './components/DocQuickStartWidget';
import { SAMPLE_GUIDE_CONFIG } from './guide';

export default (app, App, history) => {
  window.hashHistory = history;

  return (props) => {
    useRegionGray();
    const { isMockUser } = useMockUser();
    const { authed, loading } = useAuth({ disableErrorDialog: true });
    const { isWhite } = useWhiteList();
    const { isMaintainingWhite } = useMaintaining();
    const [visible, setVisible] = useState(true);

    if (loading) return <Loading fullScreen />;
    if (!isMaintainingWhite) return <Maintaining />;
    if (!isWhite) return <NotAllowed />;
    const onDocVisibilityChange = (visible) => {
      setVisible(visible);
    }
    return (
      <CommonErrorBoundary>
        <AppProvider>
          <MessengerRegionBar />
          <MessengerResourceGroupBar />
          {!CachedData.isEaChannel() && <OpAnnouncement />}
          {process.env.NODE_ENV === 'development' && <SaveToken />}
          {isMockUser && <MockUser />}
          {!authed && <Auth />}
          {authed && (
            <div style={{ display: 'flex', width: '100%' }}>
              <div className="main-page-container div1" style={{ flex: 1, minWidth: 0 }}>
                <Switch>
                  <Route path="/:regionId/gateway/:id" component={GatewayDetail} />
                  <Route path="/:regionId/ai-gateway/:id" component={AiGatewayDetail} />

                  <Route>
                    <App {...props} />
                    {IS_PRE_OR_LOCAL && location.pathname && <PreEnvSelector />}
                    {IS_PRE && <MultiVersionSelector />}
                  </Route>
                </Switch>
              </div>
              <div className="doc-quick-start-panel div2" style={{ width: visible ? 300 : 0 }}>
                <DocQuickStartWidget
                  appName='guidePanel'
                  currentStepIndex={0}
                  guide={SAMPLE_GUIDE_CONFIG.guides[0]}
                  onVisibilityChange={onDocVisibilityChange}
                />
              </div>
            </div>
          )}
          {/* {process.env.NODE_ENV === 'development' && <Medusa />} */}
        </AppProvider>
      </CommonErrorBoundary>
    );
  };
};
