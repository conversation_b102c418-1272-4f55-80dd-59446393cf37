import { Icon, intl, Message } from '@ali/cnd';
import React, { useEffect, useState, useContext } from 'react';
import { ProcessType } from '../DebugResponsePanel';
import AIDialogs from './AIDialogs';
import ProcessMonitor from './ProcessMonitor';
import moment from 'moment';
import services from '~/utils/services';
import { APIContext } from '~/config/context/APIContext';
import { useIsResourceStatus } from '../../../../../useActions';

export default (props) => {
    const { isShowProcess, setIsShowProcess, diagnoseType, response, httpApiInfo, selectedEnvId, gatewayRequestId, resourceData, isRest, gatewayId } = props;
    const [isAIDataReady, setIsAIDataReady] = useState(false);
    const [currentSlsInfo, setCurrentSlsInfo] = useState({})
    const { currentGatewayInfo } =
        useContext(APIContext);
    const { isResourceProcessing } = useIsResourceStatus({
        resourceData,
        currentGatewayInfo,
    });
    const msToSec = (ms) => {
        return Math.floor(ms / 1000);
    };
    const getErrorLogsList = async () => {
        if (!gatewayId || !gatewayRequestId) {
            return
        }
        const params = {
            startTime: msToSec(moment().startOf('day').valueOf()),
            endTime: msToSec(moment().endOf('day').valueOf()),
            gatewayId,
            gatewayRequestId
        };
        const data = await services.ListGatewayErrorAccessLogs({
            customErrorHandle: (err, data, callback) => {
                callback();
            },
            params,
        });
        return data
    };


    const longPollErrorLogs = async (options: {
        maxAttempts?: number;
        timeout?: number;
        onSuccess: (data: any) => void;
        onFailure?: () => void;
    }) => {
        const { maxAttempts = 15, timeout = 1500, onSuccess, onFailure } = options;
        let attempts = 0;
        const poll = async (): Promise<any> => {
            try {
                attempts++;
                const data = await getErrorLogsList();

                // 情况1：成功获取数据
                if (data && data.length > 0) {
                    return data;
                }
                // 情况2：未达到最大尝试次数
                if (attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, timeout));
                    return poll(); // 继续轮询
                }
                // 情况3：达到最大尝试次数仍无数据
                return null;
            } catch (error) {
                console.error('轮询请求失败:', error);
                return null;
            }
        };
        const result = await poll();
        if (result) {
            setCurrentSlsInfo(result[0]);
            setIsAIDataReady(true);
            onSuccess(result);
        } else {
            setIsShowProcess(false);
            onFailure?.();
            Message.error(intl('apigw.api-debugging.RequestParamsTab.PleaseTryAgain'));
        }
    };
    useEffect(() => {
        setCurrentSlsInfo({});
        if (diagnoseType === ProcessType.aidiagnose) {
            longPollErrorLogs({
                onSuccess: (data) => {
                    console.log('成功获取日志:', data);
                    // 其他成功处理逻辑
                },
                onFailure: () => {
                    console.log('获取日志失败');
                    // 其他失败处理逻辑
                }
            });
        }
    }, [isShowProcess]);
    

    return (
        <>
            {(diagnoseType == ProcessType.aidiagnose || diagnoseType == ProcessType.process) && isShowProcess&&
                <div className='divider'
                    style={{ background: "#FFFFFF", height: '100%', width: '11px', boxSizing: 'border-box', border: '1px solid #E5E5E5' }}
                >
                    <div className='divider-line' style={{ width: '3px', height: '36px', background: '#EEEEEE', borderRadius: 10, }}></div>
                </div>
            }
            {
                isShowProcess &&
                <div style={{ width: 'calc(50% - 11px)' }}>
                    {
                        diagnoseType === ProcessType.aidiagnose && (
                            isAIDataReady ? (<AIDialogs
                                visible={true}
                                onClose={() => {
                                    setIsShowProcess(false);
                                    setCurrentSlsInfo({})
                                }}
                                currentSlsInfo={currentSlsInfo}
                            />) : (<div className='flex justify-center align-center' style={{ height: '100%' }}>
                                <Icon type="loading" />{intl('apigw.shared.ExtendSelect.Loading')}.....
                            </div>))
                    }
                    {
                        diagnoseType === ProcessType.process &&
                        <ProcessMonitor
                            isAll={false}
                            response={response}
                            currentEnvId={selectedEnvId}
                            httpApiInfo={httpApiInfo}
                            onClose={() => { setIsShowProcess(false) }}
                            isResourceProcessing={isResourceProcessing}
                            resourceInfo={{
                                resourceId: isRest ? resourceData?.operationId : resourceData?.routeId,
                                resourceType: isRest ? 'Operation' : 'GatewayRoute',
                                ...resourceData,
                            }}

                        />

                    }
                </div>
            }
        </>
    );
};