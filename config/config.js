/**
 * 整个应用构建, 编译配置
 *
 * 详细工程化相关配置 详情参见：
 * https://xconsole.aliyun-inc.com/nexconsole/develop/vcpzm0
 */
import WindiCSSWebpackPlugin from 'windicss-webpack-plugin';
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

const IS_IN_DEF_IDE = process.env.IDE_NAME === 'ide-framework-server';
const querystring = require('qs');
const merge = require('webpack-merge');
const isEnvDevelopment = process.env.NODE_ENV === 'development';
// const isEnvDevelopment = true;

const onProxyReq = (proxyReq, req) => {
  const writeBody = (bodyData) => {
    proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
    proxyReq.write(bodyData);
  };
  const contentType = proxyReq.getHeader('Content-Type');
  if (contentType === 'application/json') {
    return writeBody(JSON.stringify(req.body));
  }

  if (contentType?.includes?.('application/x-www-form-urlencoded')) {
    return writeBody(querystring.stringify(req.body));
  }
};

module.exports = {
  // 产品名称, 如果是控制台项目一定要配置此选项
  // 保持和你 viper code 相关
  product: 'xconsole',
  // 路由配置
  routes: {
    index: 'overview',
    mode: 'hash',
  },

  // 编译构建 typescript
  useTypescript: true,

  // 开启 topbar, sidebar
  consoleBase: {
    enableErrorPromptProxy: true,
  },

  // 加入监控脚本
  armsId: '',

  // 开启 oneConsole 的 meta 标签
  oneConsole: true,
  // 国际化配置
  intl: {
    locale: 'zh',
    products: [
      {
        group: 'aliyun',
        name: 'wind',
        identifier: 'ALIYUN_WIND_MESSAGE',
      },
    ],
    messages: 'locales/messages.js',
  },

  // mocks 配置
  mocks: {
    oneapi: false,
    // oneapi: true,
    // product: 'wind-pro',
    proxy: {
      '/data/api.json': {
        target: 'https://pre-apigw.console.aliyun.com',
        changeOrigin: true,
        secure: false,
        ws: true,
        withCredentials: true,
        onProxyReq,
      },
      '/tool/sse/get.json': {
        target: 'https://pre-apigw.console.aliyun.com',
        changeOrigin: true,
        secure: false,
        ws: true,
        withCredentials: true,
        onProxyReq,
      },
      '/data/custom.json': {
        target: 'https://pre-apigw.console.aliyun.com',
        changeOrigin: true,
        secure: false,
        ws: true,
        withCredentials: true,
        onProxyReq,
      },
    },
  },

  // 开启低版本浏览器提示
  browserCompatibility: true,

  useTerserPlugin: true,

  // 在 DEF IDE 中启动项目的时候，不自动打开浏览器
  noOpen: IS_IN_DEF_IDE ? true : false,

  // 自定义 webpack 配置
  webpack: (config) => {
    /* 请自行用 webpack-merge 和传入的 config 做合并 */
    return merge(config, {
      plugins: [
        new WindiCSSWebpackPlugin({
          virtualModulePath: '../src',
        }),
        // isEnvDevelopment &&
        //   new BundleAnalyzerPlugin({
        //     analyzerMode: 'static',
        //     openAnalyzer: false,
        //     reportFilename: 'report.html',
        //   }),
      ],
      module: {
        rules: [
          {
            test: /\.(js|mjs|jsx|ts|tsx)$/,
            loader: require.resolve('babel-loader'),
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
              plugins: [
                'jsx-control-statements',
                // 配置仅在开发环境生效的 babel 插件
              ].filter(Boolean),
            },
          },
        ],
      },
      devServer: {
        // proxy: {
        //   '/data/api.json': {
        //     target: 'https://pre-mse.console.aliyun.com',
        //     changeOrigin: true,
        //     secure: false,
        //     ws: true,
        //     withCredentials: true,
        //     // onProxyReq,
        //   },
        // },
        // allowedHosts: ['localhost', '127.0.0.1'],
        // host: '0.0.0.0',
      },
      externals: [
        {
          'prop-types': 'PropTypes',
          react: 'React',
          'react-dom': 'ReactDOM',
        },
      ],
    });
  },

  // add more configs
};
