import React, { useEffect, useState } from 'react';
// import ApilotRequest from 'utils/apilot_service';
import {
  Table,
  Grid,
  Button,
  Icon,
  Message,
  Select,
  Input,
  Form,
  Field,
  Truncate,
  Balloon,
  Loading,
  Copy,
  intl,
  LinkButton,
} from '@ali/cnd';
// import RouteMonitor from './components/RouteMonitor';
import QuickTimeSelect from './components/QuickTimeSelect';
import services from '~/utils/services';
import { map, toNumber, get } from 'lodash';
import './index.less';
import ExtendBalloon from '../shared/ExtendBalloon';
// import RouteMonitor from '../api-manage/router/components/router/components/RouteMonitor';
// import ExternalLink from '../shared/ExternalLink';
// import CachedData from '~/utils/cacheData';
import AIDialogs from './components/AIDialogs';
import { formatDate } from '~/utils';
import gateway from '~/utils/services/gateway';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const errorCodeList = ['404', '503', '502', '403', '4xx', '5xx'];
const SelfTroubleshooting = (props) => {
  const { gatewayId, gatewayType } = props;
  const [instanceList, setInstancelist] = useState([]);
  const [authorityList, setAuthorityList] = useState([]);
  const [currentSlsInfo, setCurrentSlsInfo] = useState({});
  const [loading, setLoading] = useState(false);
  const [queryLoading, setQueryLoading] = useState(false);
  const [showAiDialogs, setShowAiDialogs] = useState(false);
  const [isSearch, setIsSearch] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [currentQuickTime, setCurrentQuickTime] = useState('last_12_hours');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [state, setState] = useState(undefined);
  const field = Field.useField();
  const { getValue, setValue, validate, getValues, setValues } = field;

  useEffect(() => {
    if (gatewayId) {
      getInstanceList(gatewayId);
    } else {
      getInstanceList();
    }
  }, []);

  const columns = [
    {
      key: 'request_id',
      title: intl('mse.selfTroubleshooting.requestId'),
      dataIndex: 'request_id',
      width: 220,
      lock: 'left',
      cell: (value) => {
        return (
          <Truncate showTooltip type="width" threshold={180}>
            {value}
          </Truncate>
        );
      },
    },
    {
      key: 'start_time',
      title: intl('mse.selfTroubleshooting.requestTime'),
      dataIndex: 'start_time',
      width: 200,
      cell: (value) => {
        return formatDate(toNumber(value));
      },
    },
    {
      key: 'authority',
      title: intl('mse.selfTroubleshooting.domain'),
      dataIndex: 'authority',
      width: 180,
    },
    {
      key: 'path',
      title: intl('mse.selfTroubleshooting.path'),
      dataIndex: 'path',
      width: 180,
      cell: (value) => {
        return (
          <Truncate showTooltip type="width" threshold={140}>
            {value}
          </Truncate>
        );
      },
    },
    {
      key: 'route_name',
      title: intl('@ali/widget-edas-microgw::widget.defense.route'),
      dataIndex: 'route_name',
      width: 150,
      cell: (value) => {
        return (
          <Truncate showTooltip type="width" threshold={140}>
            {value}
          </Truncate>
        );
      },
      // cell: (value, index, record) => {
      //   return (
      //     <>
      //       {value}
      //       {value && value !== '-' && (
      //         <div
      //           style={{ display: 'inline-block', cursor: 'pointer' }}
      //           onClick={() => handleOpenMonitor(value, record)}
      //         >
      //           <svg
      //             xmlns="http://www.w3.org/2000/svg"
      //             style={{ display: 'inline-block', marginLeft: '8px' }}
      //             fill="none"
      //             version="1.1"
      //             width="12"
      //             height="12"
      //             viewBox="0 0 12 12"
      //           >
      //             <g>
      //               <g>
      //                 <path
      //                   d="M12,11.0002L1.002,11.0002L1.002,0L0.00225,0L0.00225,11.0002L0,11.0002L0,12L12,12L12,11.0002ZM1.5885,8.43975L2.41125,9.00825L4.689,5.71575L8.2065,7.51875L11.8253,2.80425L11.0318,2.19525L7.92075,6.249L4.365,4.4265L1.5885,8.43975Z"
      //                   fill-rule="evenodd"
      //                   fill="#0064C8"
      //                   fill-opacity="1"
      //                 />
      //               </g>
      //             </g>
      //           </svg>
      //         </div>
      //       )}
      //     </>
      //   );
      // },
    },
    {
      key: 'upstream_host',
      title: intl('mse.selfTroubleshooting.upstreamHost'),
      dataIndex: 'upstream_host',
      width: 150,
    },
    {
      key: 'duration',
      title: intl('mse.selfTroubleshooting.duration'),
      dataIndex: 'duration',
      width: 150,
    },
    {
      key: 'upstream_service_time',
      title: intl('mse.selfTroubleshooting.upstreamServiceTime'),
      dataIndex: 'upstream_service_time',
      width: 150,
    },
    {
      key: 'trace_id',
      title: intl('mse.selfTroubleshooting.traceId'),
      dataIndex: 'trace_id',
      width: 200,
      cell: (value, index, record) => {
        return (
          <>
            <div>{value}</div>
            {value && value !== '-' && (
              <Copy text={value}>
                <a onClick={goToTrace}>
                  <Truncate threshold={140} type="width">
                    {value}
                  </Truncate>
                </a>
              </Copy>
            )}
          </>
        );
      },
    },
    {
      key: 'response_code',
      title: intl('apigw.components.ai-diagnose.ErrorCode'),
      dataIndex: 'response_code',
      lock: 'right',
      width: 160,
    },
    {
      key: 'solution',
      title: intl('mse.selfTroubleshooting.solution'),
      dataIndex: 'solution',
      width: 120,
      lock: 'right',
      cell: (value, index, record) => (
        <Button
          type="primary"
          text
          onClick={() => {
            setCurrentSlsInfo(record);
            setShowAiDialogs(true);
          }}
        >
          {intl('apigw.components.ai-diagnose.AskAi')}
        </Button>
      ),
    },
  ];

  const goToTrace = () => {
    window.open(
      `/#/${window.regionId}/gateway/${getValue('gatewayId')}/observe/trace?region=${
        window.regionId
      }`,
    );
  };

  // const handleOpenMonitor = (value, record) => {
  //   setCurrentRouterName(value);
  //   setShowMonitor(true);
  // };

  const msToSec = (ms) => {
    return Math.floor(ms / 1000);
  };

  const getInstanceList = async (id = '') => {
    setState('loading');
    const { items } = await services.getGatewayList({
      params: {
        pageSize: 500,
        pageNumber: 1,
        gatewayType,
      },
    });

    const _items = map(items, (item) => {
      return {
        ...item,
        value: item.gatewayId,
        label: item.name,
      };
    });
    setState(undefined);
    setInstancelist(_items);
    if (id) {
      setValue('gatewayId', id);
      await getGatewayDomain(id);
    }
  };

  const chooseErrorCode = (code) => {
    setValue('responseCode', code);
  };

  const renderInstanceItem = (item) => {
    return `${item.label} / ${item.value}`;
  };

  const onInstanceChange = async (value) => {
    setValues({
      gatewayId: value,
      routeName: '',
      authority: '',
    });
    if (value) {
      await getGatewayDomain(value);
    }
  };

  const getGatewayDomain = async (gatewayId) => {
    if (gatewayId) {
      const { items = [] } = await services.ListDomains({
        params: {
          pageNumber: 1,
          pageSize: 500,
          gatewayId,
          gatewayType,
        },
      });
      const _items = map(items, (item) => ({
        ...item,
        value: item.name,
        label: item.name,
      }));
      setAuthorityList(_items);
    }
  };

  const onSearch = async () => {
    validate(async (errors) => {
      if (errors) {
        return;
      }
      setQueryLoading(true);
      setIsSearch(true);
      await getErrorLogsList();
    });
  };

  const getErrorLogsList = async () => {
    const values = getValues();
    const { errorCodeList, ...rest } = values as any;
    const params = {
      startTime: msToSec(startTime),
      endTime: msToSec(endTime),
      ...rest,
    };
    const data = await services.ListGatewayErrorAccessLogs({
      customErrorHandle: (err, data, callback) => {
        setLoading(false);
        callback();
      },
      params,
    });
    setDataSource(data || []);
    setQueryLoading(false);
  };

  const changeTimeRange = (value) => {
    const { startTime, endTime, currentQuickTime } = value;
    startTime && setStartTime(startTime);
    endTime && setEndTime(endTime);
    setCurrentQuickTime(currentQuickTime);
  };

  return (
    <div className="mse-self-troubleshooting">
      <Message type="notice" style={{ marginBottom: 8 }}>
        <div>{intl('mse.selfTroubleshooting.tip')}</div>
      </Message>

      <Form field={field} {...formItemLayout} labelTextAlign="left">
        <div className="form-item-group">
          <div className="grid-container" id="self-trouble-shooting">
            <Grid.Row gutter="10">
              <Grid.Col span={8}>
                <Form.Item
                  name="gatewayId"
                  label={intl('mse.selfTroubleshooting.instance.label')}
                  required={true}
                  labelAlign="top"
                  labelTextAlign="left"
                  labelCol={{ span: 4 }}
                >
                  <Select
                    state={state}
                    placeholder={intl('widget.common.p_select')}
                    dataSource={instanceList}
                    itemRender={renderInstanceItem}
                    onChange={onInstanceChange}
                    showSearch
                    filterLocal
                    hasClear
                  ></Select>
                </Form.Item>
              </Grid.Col>

              <Grid.Col span={8}>
                <Form.Item
                  name="responseCode"
                  label={intl('mse.selfTroubleshooting.errorCode.label')}
                  labelAlign="top"
                  labelTextAlign="left"
                  labelCol={{ span: 4 }}
                >
                  <Select.AutoComplete
                    dataSource={errorCodeList}
                    className="full-width"
                    placeholder={intl('mse.microgw.selfTroubleshooting.errorCode.placeholder')}
                    filterLocal={false}
                    hasClear
                  />
                </Form.Item>
              </Grid.Col>

              <Grid.Col span={8}>
                <Form.Item
                  name="authority"
                  label={intl('mse.selfTroubleshooting.domain.label')}
                  labelAlign="top"
                  labelTextAlign="left"
                  labelCol={{ span: 4 }}
                >
                  <Select
                    placeholder={intl('widget.common.p_select')}
                    dataSource={authorityList}
                    hasClear
                  ></Select>
                </Form.Item>
              </Grid.Col>
            </Grid.Row>

            <Grid.Row gutter="10">
              <Grid.Col span={8}>
                <Form.Item
                  name="path"
                  label={intl('mse.selfTroubleshooting.path.label')}
                  labelAlign="top"
                  labelTextAlign="left"
                  labelCol={{ span: 4 }}
                >
                  <Input placeholder={intl('widget.common.p_input')} hasClear trim />
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={8}>
                <Form.Item
                  name="routeName"
                  label={intl('widget.route.name')}
                  labelAlign="top"
                  labelTextAlign="left"
                  labelCol={{ span: 4 }}
                >
                  <Input
                    placeholder={intl('apigw.components.ai-diagnose.EnterARouteName')}
                    hasClear
                    trim
                  />
                </Form.Item>
              </Grid.Col>
              <Grid.Col span={8}>
                <Form.Item
                  name="gatewayRequestId"
                  label={intl('mse.selfTroubleshooting.requestId')}
                  labelAlign="top"
                  labelTextAlign="left"
                  labelCol={{ span: 4 }}
                >
                  <Input placeholder={intl('widget.common.p_input')} hasClear trim />
                </Form.Item>
              </Grid.Col>
            </Grid.Row>
            <Grid.Row gutter="10">
              <Grid.Col>
                <ExtendBalloon
                  trigger={
                    <Button
                      type="primary"
                      disabled={!getValue('gatewayId')}
                      onClick={onSearch}
                      loading={queryLoading}
                      className="mb-16"
                    >
                      <Icon type="search" />
                      {intl('mse.selfTroubleshooting.submit')}
                    </Button>
                  }
                  isShow={!getValue('gatewayId')}
                >
                  {intl('apigw.components.ai-diagnose.SelectTheInstanceToDiagnose')}
                </ExtendBalloon>
              </Grid.Col>
            </Grid.Row>

            <Grid.Row gutter="10">
              <Grid.Col span={17}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                  <QuickTimeSelect
                    startTime={startTime}
                    endTime={endTime}
                    currentQuickTime={currentQuickTime}
                    changeTimeRange={changeTimeRange}
                  />
                </div>
              </Grid.Col>
            </Grid.Row>
          </div>
        </div>
      </Form>

      <div>
        <Loading visible={loading} style={{ width: '100%', height: '100%' }}>
          <Table
            columns={columns as any}
            dataSource={dataSource}
            loading={queryLoading}
            hasBorder={false}
            emptyContent={
              isSearch
                ? intl('apigw.components.ai-diagnose.ThereIsNoDataIn')
                : intl('apigw.components.ai-diagnose.NoDataAvailable')
            }
          />
        </Loading>
      </div>

      {/* {showMonitor && (
         <div className="router-table">
           <div className="top-title">
             <div>{`${intl('@ali/widget-edas-microgw::widget.gateway.action.monitor')} (${
               currentRouterName
             }) `}</div>
             <div className="flex">
               <LinkButton
                 className="mr-20 text-xs"
                 // onClick={() => {
                 //   const { Id: RouteId, environmentInfo } = currentRouteInfo;
                 //   const gatewayId = get(environmentInfo, 'gatewayInfo.gatewayId');
                 //   const url = `${window.location.origin}/#/${window.regionId}/api-manage/api-http/${apiId}/router/detail?region=${window.regionId}&gatewayId=${gatewayId}&routerId=${RouteId}&apiName=${apiName}&tabKey=monitor`;
                 //   window.open(url, '_blank');
                 // }}
               >
                 <span>{intl('apigw.router.components.GoToTheMonitoringDashboard')}</span>
                 <Icon type="external-link" size="xs" />
               </LinkButton>
               <Balloon
                 closable={false}
                 triggerType="hover"
                 trigger={
                   <span className="text-xs mr-16 flex items-center ">
                     {intl('apigw.router.components.ForMoreMonitoringDataGo')}
                      <ExternalLink
                       label={intl('apigw.router.components.CloudMonitoring')}
                       url={CachedData.confLink('cms:console:dashboard')}
                     />
                     {intl('apigw.router.components.View')}
                      <Icon type="help" size="xs" className="ml-4" />
                   </span>
                 }
               >
                 <span>{intl('apigw.router.components.DueToTheAdjustmentOf')}</span>
               </Balloon>
                <Button
                 text
                 onClick={() => {
                   setShowMonitor(false);
                 }}
               >
                 <Icon type="close" />
               </Button>
             </div>
           </div>
           <div className="dialog-content">
             <RouteMonitor
               GatewayUniqueId={getValue('gatewayId')}
               // RouteId={currentRouteInfo?.Id}
               routeName={currentRouterName}
               // apiName={apiName}
             />
           </div>
         </div>
        )} */}

      {showAiDialogs && (
        <AIDialogs
          visible={showAiDialogs}
          onClose={() => {
            setShowAiDialogs(false);
            setCurrentSlsInfo({});
          }}
          currentSlsInfo={currentSlsInfo}
        />
      )}
    </div>
  );
};
export default SelfTroubleshooting;
