import { intl } from '@ali/cnd';
import React, { useContext, useState } from 'react';
import AddPolicyPlugin from './AddPolicyPlugin';
import { Button, Icon, Tag, Truncate } from '@ali/cnd';
import PolicyPanel from './PolicyPanel';
import { find, get, isEmpty, map, filter, includes, concat } from 'lodash';
import DeletePolicy from './DeletePolicy';
import DeletePlugin from './DeletePlugin';
import APIPluginConfigPanel from '~/components/plugin-manage/APIPluginConfigPanel';
import { APIContext } from '~/config/context/APIContext';
import { formatPluginInfo } from '~/components/plugin-manage/plugin-flow/usePluginFlow';

const EnterOrOutContent = ({
  type = 'InBound',
  isAll,
  environmentId,
  policyList,
  onRefresh,
  isResourceProcessing,
  httpApiInfo,
  resourceInfo,
  concatDomains,
}) => {
  const [currentPlugin, setCurrentPlugin] = useState({});
  const { currentGatewayInfo } = useContext(APIContext);

  return (
    <div className={type === 'InBound' ? 'enter-content' : 'out-content'}>
      {map(policyList, (policy) => {
        const {
          formType,
          attachResourceType,
          classAlias,
          alias,
          policyAttachmentId,
          policyId,
          pluginAttachmentId,
          className
        } = policy || {};
        const isApi = attachResourceType === 'HttpApi';
        const isDisabled = !isAll && isApi;
        const isApiWafPolicy = className === 'WafApi';

        const { enable, httpRewriteInitData } = formatPluginInfo({
          pluginData: policy,
          httpApiInfo,
          resourceInfo,
          domains: concatDomains,
          isAll,
        });

        return (
          <>
            <div className="content-direction"></div>
            <div className="space-between pt-8 pb-8" style={{ borderBottom: '1px solid #DBDBDB' }}>
              <div className="align-center mr-16">
                <Tag
                  type="normal"
                  size="small"
                  className="mr-8"
                  style={{ borderRadius: 2, border: '1px solid #BCC0C5', background: '#EFF2F8' }}
                >
                  {formType === 'Policy'
                    ? intl('apigw.components.api-operations-policy.EnterOrOutContent.Strategy')
                    : intl('apigw.components.api-operations-policy.EnterOrOutContent.PlugIn')}
                </Tag>
                {/* {isApi && (
                  <Tag type="normal" size="small" className="mr-8">
                    {intl('apigw.components.api-operations-policy.EnterOrOutContent.ApiLevel')}
                  </Tag>
                )} */}

                {formType === 'Policy' ? (
                  <PolicyPanel
                    isAll={isAll}
                    onRefresh={onRefresh}
                    trigger={
                      <Button type="primary" text disabled={isResourceProcessing}>
                        <Truncate type="width" threshold={isApi ? 145 : 190} align="t">
                          {alias || classAlias}
                        </Truncate>
                      </Button>
                    }
                    curData={{
                      ...policy,
                      initData: httpRewriteInitData,
                      routeStatus: get(resourceInfo, 'status'),
                      attachResourceName: get(resourceInfo, 'name'),
                    }}
                    type="edit"
                  />
                ) : (
                  <Button
                    type="primary"
                    text
                    onClick={() => setCurrentPlugin(policy)}
                    disabled={isResourceProcessing}
                  >
                    <Truncate type="width" threshold={isApi ? 145 : 190} align="t">
                      {alias || classAlias}
                    </Truncate>
                  </Button>
                )}
              </div>
              <div className="align-center">
                {enable ? (
                  <div className="align-center">
                    <Icon className="color-success mr-5 l-h-14" type="check_fill" size={'small'} />
                    <span>
                      {intl('apigw.components.api-operations-policy.EnterOrOutContent.Enabled')}
                    </span>
                  </div>
                ) : (
                  <div className="align-center">
                    <Icon className="mr-5 l-h-14" type="minus_fill" size={'small'} />
                    <span>
                      {intl('apigw.components.api-operations-policy.EnterOrOutContent.NotEnabled')}
                    </span>
                  </div>
                )}

                <div className="ml-8 color-gray">|</div>
                {formType === 'Policy' ? (
                  <DeletePolicy
                    disabled={isDisabled || isResourceProcessing}
                    policyAttachmentId={policyAttachmentId}
                    policyId={policyId}
                    onRefresh={onRefresh}
                    triggerType="icon"
                    isApiWafPolicy={isApiWafPolicy}
                    apiType={get(httpApiInfo, 'type')}
                  />
                ) : (
                  <DeletePlugin
                    apiType={get(httpApiInfo, 'type')}
                    disabled={isDisabled || isResourceProcessing}
                    pluginAttachmentId={pluginAttachmentId}
                    onRefresh={onRefresh}
                    triggerType="icon"
                  />
                )}
              </div>
              {!isEmpty(currentPlugin) &&
                get(currentPlugin, 'pluginAttachmentId') === get(policy, 'pluginAttachmentId') && (
                  <APIPluginConfigPanel
                    type="edit"
                    visible={
                      !isEmpty(currentPlugin) &&
                      get(currentPlugin, 'pluginAttachmentId') === get(policy, 'pluginAttachmentId')
                    }
                    isDisabled={!isAll && get(policy, 'attachResourceType') === 'HttpApi'}
                    onClose={() => setCurrentPlugin({})}
                    sourceProps={{
                      attachResourceId: get(policy, 'attachResourceId'),
                      attachResourceType: get(policy, 'attachResourceType'),
                      attachResourceNames: get(policy, 'attachResourceNames'),
                      environmentId,
                      gatewayId:
                        get(currentGatewayInfo, 'gatewayId') ||
                        get(resourceInfo, 'environmentInfo.gatewayInfo.gatewayId'),
                    }}
                    plugin={policy}
                    curData={{
                      enable: get(policy, 'enable', false),
                      pluginConfig: get(
                        policy,
                        'pluginConfig',
                        get(policy, 'pluginInfo.pluginConfig'),
                      ),
                    }}
                    onRefresh={onRefresh}
                  />
                )}
            </div>
          </>
        );
      })}

      <AddPolicyPlugin
        direction={type}
        isAll={isAll}
        resourceInfo={resourceInfo}
        environmentId={environmentId}
        disabled={!environmentId || isResourceProcessing}
        onRefresh={onRefresh}
        httpApiInfo={httpApiInfo}
        concatDomains={concatDomains}
      // currentEnvStatus={currentEnvStatus}
      />
    </div>
  );
};
export default EnterOrOutContent;
