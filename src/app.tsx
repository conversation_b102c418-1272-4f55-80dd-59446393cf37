import React, { useEffect, useRef, useState } from 'react';
import './virtual:windi.css';
// import './utils/ydIntlInitiator';
// import './utils/emitter';
import './utils/globalLib';
import { Route, Switch } from 'react-router-dom';
import GatewayDetail from './pages/$regionId/gateway/$id';
import SaveToken from './components/shared/DevToken';
import './styles/index.less';
import PreEnvSelector from './components/shared/PreEnvSelector';
import { IS_PRE, IS_PRE_OR_LOCAL } from './constants';
import {
  useAuth,
  useMaintaining,
  useMockUser,
  useRegionGray,
  useWhiteList,
} from './components/auth/useAuth';
import Auth from './pages/auth';
import { Loading } from '@ali/cnd';
import NotAllowed from './pages/notAllowed';
import { OpAnnouncement } from './components/shared/OpAnnouncement';
import Maintaining from './pages/maintaining';
import { MockUser } from './components/shared/MockUser';
import { MultiVersionSelector } from './components/shared/MultiVersionSelector';
import { MessengerRegionBar } from './components/shared/MessengerRegionBar';

import MessengerResourceGroupBar from './components/shared/MessengerResourceGroupBar';
import { CommonErrorBoundary } from './components/shared/ErrorFallback';
import AppProvider from './components/app-provider';
import AiGatewayDetail from './pages/$regionId/ai-gateway/$id';
import CachedData from './utils/cacheData';
import DocQuickStartWidget from './components/DocQuickStartWidget';
import { SAMPLE_GUIDE_CONFIG } from './guide';
import './index.less'

export default (app, App, history) => {
  window.hashHistory = history;

  return (props) => {
    useRegionGray();
    const { isMockUser } = useMockUser();
    const { authed, loading } = useAuth({ disableErrorDialog: true });
    const { isWhite } = useWhiteList();
    const { isMaintainingWhite } = useMaintaining();

    // 用于控制布局的状态
    const [widgetHasContent, setWidgetHasContent] = useState(true);
    const div2Ref = useRef(null);
    const observerRef = useRef(null);

    if (loading) return <Loading fullScreen />;
    if (!isMaintainingWhite) return <Maintaining />;
    if (!isWhite) return <NotAllowed />;

    // 监控widget内容变化的effect
    useEffect(() => {
      if (!div2Ref.current) return;

      const checkWidgetContent = () => {
        const widget = div2Ref.current?.querySelector('.widget-document-quickstart');
        if (!widget) {
          // setWidgetHasContent(false);
          return;
        }

        const widgetRect = widget.getBoundingClientRect();
        const hasContent = widgetRect.width > 0 && widgetRect.height > 0;
        // setWidgetHasContent(hasContent);
      };

      // 初始检查
      checkWidgetContent();

      // 创建ResizeObserver监听widget大小变化
      if (window.ResizeObserver) {
        observerRef.current = new ResizeObserver(() => {
          checkWidgetContent();
        });

        const widget = div2Ref.current.querySelector('.widget-document-quickstart');
        if (widget) {
          observerRef.current.observe(widget);
        }
        observerRef.current.observe(div2Ref.current);
      }

      // 清理函数
      return () => {
        if (observerRef.current) {
          observerRef.current.disconnect();
        }
      };
    }, [authed]); // 当authed状态改变时重新初始化


    return (
      <CommonErrorBoundary>
        <AppProvider>
          <MessengerRegionBar />
          <MessengerResourceGroupBar />
          {!CachedData.isEaChannel() && <OpAnnouncement />}
          {process.env.NODE_ENV === 'development' && <SaveToken />}
          {isMockUser && <MockUser />}
          {!authed && <Auth />}
          {authed && (
            <div style={{ display: 'flex', width:'100%' }}>
              <div
                className="main-page-container div1"
                style={{
                  flex: widgetHasContent ? 1 : 'none',
                  width: widgetHasContent ? 'auto' : '100%',
                  minWidth: 0
                }}
              >
                <Switch>
                  <Route path="/:regionId/gateway/:id" component={GatewayDetail} />
                  <Route path="/:regionId/ai-gateway/:id" component={AiGatewayDetail} />

                  <Route>
                    <App {...props} />
                    {IS_PRE_OR_LOCAL && location.pathname && <PreEnvSelector />}
                    {IS_PRE && <MultiVersionSelector />}
                  </Route>
                </Switch>
              </div>
              <div
                ref={div2Ref}
                className="doc-quick-start-panel div2"
                style={{
                  width: widgetHasContent ? 'auto' : 0,
                  minWidth: widgetHasContent ? 300 : 0,
                  maxWidth: widgetHasContent ? 400 : 0,
                  overflow: 'hidden',
                  transition: 'width 0.3s ease, min-width 0.3s ease, max-width 0.3s ease'
                }}
              >
                <DocQuickStartWidget
                  className="widget-document-quickstart"
                  appName='guidePanel'
                  currentStepIndex={0}
                  guide={SAMPLE_GUIDE_CONFIG.guides[0]}
                />
              </div>
            </div>
          )}
          {/* {process.env.NODE_ENV === 'development' && <Medusa />} */}
        </AppProvider>
      </CommonErrorBoundary>
    );
  };
};
