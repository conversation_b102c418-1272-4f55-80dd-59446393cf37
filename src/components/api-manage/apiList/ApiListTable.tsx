import React, { useEffect, useState, useRef } from 'react';
import { aiGatewaySearch, columns, search } from './ApiListTableProps';
import ActionContainer from '../createApi/create-actions/ActionContainer';
import CreateApiTypesDialog from '../createApi/CreateApiTypesDialog';
import { Button, intl, Select, Icon, Truncate, CndTable } from '@ali/cnd';
import { get, first, map, isEmpty, includes, forEach, filter, find } from 'lodash';
import CreateOrUpdateApiSidePanel from '../createApi/create-actions/CreateOrUpdateApiSidePanel';
import services from '~/utils/services';
import ListMonitoring from './ListMonitoring';
import CachedData from '~/utils/cacheData';
import { API_CREATE_TYPE } from '~/track/api';
import useApiRetry from './useApiRetry';
import { AIModelDebug } from '../apiAi/AIModelDebug';
import { useResourceGroupId } from '~/components/app-provider';
import { CREATE_API_TYPE } from '../createApi';

const CreateApi = ActionContainer(CreateApiTypesDialog, {
  trigger: (
    <Button type="primary">{intl('apigw.api-manage.apiList.ApiListTable.CreateAnApi')}</Button>
  ),
});

const NoDatereateApi = ActionContainer(CreateApiTypesDialog, {
  trigger: (
    <span className="color-primary-btn cursor-pointer ml-4">
      {intl('apigw.api-manage.apiList.ApiListTable.CreateAnApi')}
    </span>
  ),
});

const ApiListTable = ({
  history,
  hasGateway,
  tabKey,
  gatewayId = '',
  // aiScene = CREATE_API_TYPE.AI,
  apiScene = CREATE_API_TYPE.AI,
  gatewayInfo = {},
  envId = '',
  inGatewayId,
  hasAiApi,
}: any) => {
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [showMonitor, setShowMonitor] = useState(false);
  const [showEdit, setShowEdit] = useState(false);
  const [showAIDebug, setShowAIDebug] = useState(false);
  const [currentApi, setCurrentApi] = useState<any>({});
  const [httpApiInfo, setHttpApiInfo] = useState({});
  const [actionType, setActionType] = useState('create');
  const [loopEnable, setLoopEnable] = useState(false);
  const retryCountRef = useRef(0);
  const resourceGroupId = useResourceGroupId();
  const saveSearchvalue = useRef<any>({});
  const [isSearch, setIsSearch] = useState(false);

  useEffect(() => {
    setRefreshIndex(refreshIndex + 1);
  }, [resourceGroupId]);

  const fetchData = async (params) => {
    setIsSearch(saveSearchvalue.current?.keyword || saveSearchvalue.current?.types);
    // const { tabKey, current, pageSize, ...reset } = params;
    const { tabKey, current, pageSize, ...reset } = saveSearchvalue.current;
    const data = {
      pageNumber: current,
      pageSize,
      gatewayId: gatewayId ? gatewayId : undefined,
      withEnvironmentInfo: true,
      types: tabKey !== 'all' ? apiScene : 'Http,AI,Rest,Websocket,HttpIngress',
      ...reset,
    };

    if (includes([CREATE_API_TYPE.LLM, CREATE_API_TYPE.Agent], apiScene)) {
      data.gatewayId = get(gatewayInfo, 'gatewayId');
    }

    try {
      const { items = [], totalSize } = await services.ListHttpApis({
        customErrorHandle: (err, data, callback) => {
          if (!(get(err, 'code') === 'Forbidden.AccessDenied' && CachedData.isSubAccount())) {
            callback();
          }
          return { items: [], totalSize: 0 };
        },
        params: data,
      });
      const aiApiList = filter(items, { type: apiScene });
      const rollPolingApiFind = find(aiApiList, (item) => {
        let firstVersions: any = first(item.versionedHttpApis);
        const firstDeployConfig: any = first(get(firstVersions, 'deployConfigs') || []) || {};
        const _environment =
          find(get(firstVersions, 'environments', []), {
            environmentId: firstDeployConfig.environmentId,
          }) || {};
        return retryValidate(_environment);
      });
      if (!!rollPolingApiFind && retryCountRef.current < 6) {
        setLoopEnable(true);
        retryCountRef.current += 1;
      } else {
        setLoopEnable(false);
        retryCountRef.current = 0;
      }
      return {
        data: items,
        total: totalSize,
      };
    } catch (error) {
      return { data: [], total: 0 };
    }
  };

  const { retryValidate } = useApiRetry({
    refreshIndex,
    fetchData: () => { },
  });

  const handleOpenAction = (record, actionType) => {
    setCurrentApi(record);
    switch (actionType) {
      case 'monitor':
        setShowMonitor(true);
        getCurrentApi(record);
        break;
      case 'edit':
        setActionType('edit');
        setShowEdit(true);
        break;
      default:
        break;
    }
  };

  const getCurrentApi = async (record) => {
    let versions = record.versionedHttpApis;
    if (!record.versionEnabled) {
      // setVersions([]);
    } else {
      versions = map(versions, (item) => {
        return {
          ...item,
          label: get(item, 'versionInfo.version') || intl('apigw..InitialVersion'),
          value: item.httpApiId,
        };
      });
      // setVersions(versions);
    }
    let httpApiId = get(first(versions), 'httpApiId') || '';
    return await GetHttpApi(httpApiId);
  };

  const GetHttpApi = async (httpApiId) => {
    let data = {
      httpApiId,
    };
    try {
      const result = await services.GetHttpApi({
        params: { ...data },
      });
      setHttpApiInfo(result);
      return result;
    } catch (error) {
      // setEnvDataSource([]);
    }
  };

  const handleOpenAIDebug = async (record) => {
    const api = await getCurrentApi(record);
    setCurrentApi(api);
    setShowAIDebug(true);
  };

  const CREATE_API_TITLE = {
    [CREATE_API_TYPE.AI]: intl('apigw.api-manage.apiList.ApiListTable.CreateAiApi'),
    [CREATE_API_TYPE.LLM]: intl('apigw.api.createApiDialog.CreateModelApi'),
    [CREATE_API_TYPE.Agent]: intl('apigw.api-manage.apiList.ApiListTable.CreateAgentApi'),
  };

  return (
    <>
      <CndTable
        fetchData={
          (async (value) => {
            if (value?.keyword !== saveSearchvalue.current?.keyword) {
              setLoopEnable(false);
            }
            saveSearchvalue.current = { ...saveSearchvalue.current, ...value, tabKey };
            return await fetchData(saveSearchvalue.current);
          }) as any
        }
        columns={
          columns({
            setRefreshIndex,
            handleOpenAction,
            handleOpenAIDebug,
            tabKey,
            gatewayInfo,
            gatewayId,
            inGatewayId,
            apiScene,
          }) as any
        }
        refreshIndex={refreshIndex}
        operation={
          tabKey === 'all' ? (
            <CreateApi
              history={history}
              hasGateway={hasGateway}
              setRefreshIndex={setRefreshIndex}
              hasAiApi={hasAiApi}
              engineVersion={gatewayInfo.engineVersion}
            />
          ) : gatewayId ? null : (
            <Button
              type="primary"
              onClick={() => {
                setActionType('create');
                setCurrentApi({
                  type: apiScene,
                });
                setShowEdit(true);
              }}
            >
              {CREATE_API_TITLE[apiScene]}
            </Button>
          )
        }
        search={
          apiScene === CREATE_API_TYPE.AI ? (search(tabKey) as any) : (aiGatewaySearch() as any)
        }
        showRefreshButton
        pagination={{
          pageSizeList: [10, 20, 50, 100],
          hideOnlyOnePage: false,
        }}
        emptyContent={
          isSearch || includes([CREATE_API_TYPE.LLM, CREATE_API_TYPE.Agent], apiScene) ? (
            intl('widget.common.none_data')
          ) : (
            <div className="color-light-black justify-center">
              {intl('apigw.api-manage.apiList.ApiListTable.NoApiIsAvailable')}

              <NoDatereateApi
                history={history}
                hasGateway={hasGateway}
                setRefreshIndex={setRefreshIndex}
                hasAiApi={hasAiApi}
              />
            </div>
          )
        }
        loop={{ enable: loopEnable, time: 10000, showLoading: false }}
      />

      {showMonitor && (
        <ListMonitoring
          httpApiInfo={httpApiInfo}
          currentApi={currentApi}
          apitype={get(currentApi, 'type')}
          onClose={() => {
            setShowMonitor(false);
            setCurrentApi({});
          }}
        />
      )}

      {showEdit && get(currentApi, 'type') && (
        <CreateOrUpdateApiSidePanel
          {...{
            apiType: includes([CREATE_API_TYPE.AI], get(currentApi, 'type'))
              ? API_CREATE_TYPE['aiAPI']
              : get(currentApi, 'type') === 'Http'
                ? API_CREATE_TYPE['router']
                : get(currentApi, 'type') || apiScene,
            type: actionType,
            apiScene,
            setRefreshIndex,
            environmentInfo: {
              gatewayInfo,
              environmentId: envId,
            },
            apiId: get(currentApi, 'versionedHttpApis[0].httpApiId', ''),
            setVisible: () => {
              setShowEdit(false);
              setCurrentApi({});
            },
          }}
        />
      )}

      {showAIDebug && (
        <AIModelDebug
          visible={showAIDebug}
          onClose={() => setShowAIDebug(false)}
          httpApiInfo={httpApiInfo}
          apiScene={apiScene}
          gatewayId={gatewayInfo?.gatewayId}
        />
      )}
    </>
  );
};

export default ApiListTable;
