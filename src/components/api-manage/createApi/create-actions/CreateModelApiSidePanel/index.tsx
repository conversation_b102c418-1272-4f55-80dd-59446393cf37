import React, { useState, useEffect } from 'react';
import { Field, SlidePanel, intl } from '@ali/cnd';
import BasicInfoForm from './BasicInfoForm';
import SceneDialog from './SceneDialog';
import ModelServiceForm from './ModelServiceForm';
import './index.less';
import { useCreateOrUpdateHook, useFormEcho } from '../hook/createOrUpdateHook';
import { CREATE_API_TYPE } from '../..';
import { get } from 'lodash';
import { CREATE_API_MODEL_CATEGORY } from '../components/ProtocolSelect/options';

const CreateModelApiSidePanel = (props): any => {
  const { type = 'create', environmentInfo } = props;
  const [isVisible, setIsVisible] = useState(true);
  const field = Field.useField();
  const [sceneType, setSceneType] = useState('');
  const { setValue } = field;

  useEffect(() => {
    setValue('environmentInfo', environmentInfo);
  }, [environmentInfo]);

  useFormEcho({
    field,
    httpApiInfo: props.httpApiInfo,
    echoType: CREATE_API_TYPE.LLM,
  });

  const { isProcessing, handleSubmit, handleClose } = useCreateOrUpdateHook({
    ...props,
    field,
    createType: CREATE_API_TYPE.LLM,
  });

  useEffect(() => {
    if (type === 'edit') {
      setSceneType(get(props.httpApiInfo, 'modelCategory', CREATE_API_MODEL_CATEGORY.Text));
    }
  }, [type]);

  return type == 'create' && isVisible ? (
    <SceneDialog
      isVisible={isVisible}
      handleClose={handleClose}
      engineVersion={get(environmentInfo, 'gatewayInfo.engineVersion')}
      handleCreate={(sceneType) => {
        setSceneType(sceneType);
        setIsVisible(false);
      }}
    />
  ) : (
    <SlidePanel
      title={
        type == 'create'
          ? intl('apigw.api.createApiDialog.CreateModelApi')
          : intl('apigw.api.createApiDialog.EditModelApi')
      }
      isShowing={true}
      width={960}
      onClose={handleClose}
      onCancel={handleClose}
      onOk={() => handleSubmit()}
      isProcessing={isProcessing}
    >
      <div className="CreateAiApiSidePanel">
        <BasicInfoForm {...props} field={field} sceneType={sceneType} />
        <ModelServiceForm {...props} field={field} sceneType={sceneType} />
      </div>
    </SlidePanel>
  );
};

export default CreateModelApiSidePanel;
