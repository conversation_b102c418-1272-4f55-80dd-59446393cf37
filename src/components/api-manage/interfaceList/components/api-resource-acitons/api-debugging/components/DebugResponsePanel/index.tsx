
import CodeMirrorEditor from '~/components/shared/CodeMirrorEditor';
import copy from 'copy-to-clipboard';
import React, { useState, useEffect } from 'react';
import { intl, Tab, Copy as CopyContent, Icon, Tag, Button, Message, Select, Balloon } from '@ali/cnd';
import './index.less';
import { includes } from 'lodash';
import { useHistoryDebugContext } from '../HistoryContext';

// 直接在 enum 里定义样式对象
export enum CodeStyle {
  Green = 'Green',
  Red = 'Red',
  Orange = 'Orange',
  Gray = 'Gray',
  Default = 'Default'
}

const codeStyleMap: Record<CodeStyle, React.CSSProperties> = {
  [CodeStyle.Green]: {
    backgroundColor: '#EAFAEC',
    color: '#038E4A'
  },
  [CodeStyle.Red]: {
    backgroundColor: '#FFEDEC',
    color: '#D50B16'
  },
  [CodeStyle.Orange]: {
    backgroundColor: '#FEEEE8',
    color: '#D95900'
  },
  [CodeStyle.Gray]: {
    backgroundColor: '#F6F6F6',
    color: '#555555'
  },
  [CodeStyle.Default]: {
    background: 'transparent',
    color: '#555555'
  }
};

function getCodeStyle(code: string | number): React.CSSProperties {
  if (typeof code === 'number') code = String(code);
  if (!code) return codeStyleMap[CodeStyle.Default];
  if (code.startsWith('2')) return codeStyleMap[CodeStyle.Green];
  if (code.startsWith('3')) return codeStyleMap[CodeStyle.Gray];
  if (code.startsWith('4')) return codeStyleMap[CodeStyle.Red];
  if (code.startsWith('5')) return codeStyleMap[CodeStyle.Orange];
  return codeStyleMap[CodeStyle.Default];
}

function extractContentType(contentType: string) {
  if (!contentType) return '';
  const idx = contentType.indexOf(';');
  if (idx === -1) return contentType.trim();
  return contentType.slice(0, idx).trim();
}

enum RequestParamsKey {
  body = 'Body',
  cookies = 'Cookies',
  header = 'Header',
}

export enum ProcessType {
  process = 'process',
  history = 'history',
  aidiagnose = 'AIDiagnose',
}
const HistoryList = (props) => {
  const { isShowHistory, setIsShowHistory } = props;
  const { groupedHistory, changeCurEntry, currentEntry, clearCurrentEntry, deleteHistoryItemByTimestamp, deleteHistoryByHttpRestId } = useHistoryDebugContext();
  const [historyGroups, setHistoryGroups] = useState([]);

  useEffect(() => {
    setHistoryGroups(groupedHistory);
  }, [isShowHistory, groupedHistory])

  const handleItemClick = (item) => {
    clearCurrentEntry()
    changeCurEntry(item.details)
  };

  return (
    isShowHistory &&
    <div className='w-1/4 flex flex-col' style={{ height: '100%', borderLeft: '1px solid #E5E5E5' }} >
      <div
        className='flex space-between pl-16 pr-16'>
        <span className="font-medium" style={{ fontSize: '12px' }}>
          {intl('apigw.api-debugging.DebugResponsePanel.HistoricalRecords')}
        </span>
        <div>
          <Button onClick={() => setIsShowHistory(false)} size="small" style={{ border: 'none' }}><Icon type="close1" /></Button>
        </div>
      </div>
      <div className="flex-1 overflow-y-auto min-h-0">
        {!historyGroups || historyGroups.length === 0 ? (
          <div className='mt-10 ml-16'>
            {intl('apigw.api-debugging.DebugResponsePanel.NoDebuggingRecord')}
          </div>
        ) : (
          historyGroups.map((group, groupIndex) => (
            <div key={groupIndex} >
              <div className='pl-16 pr-16 pt-8 pb-8 font-medium'>{group.date}</div>
              <ul style={{ listStyle: 'none', paddingLeft: 0 }} >
                {group.list.map((item: any, index: number) => (
                  <li
                    className={`history-item${item?.details?.timestamp === currentEntry?.timestamp ? ' active' : ''}`}
                    key={index}
                    style={{
                      position: 'relative',
                      display: 'flex',
                      justifyContent: 'space-between',
                      padding: '4px 16px',
                      alignItems: 'center'
                    }}
                    onClick={() => handleItemClick(item)}
                  >
                    <div className='mr-8'>
                      <Tag
                        style={{
                          border: 'none',
                          borderRadius: 0,
                          fontSize: '12px',
                          ...getCodeStyle(item?.status)
                        }}>{item.status || '-'}</Tag>
                    </div>
                    <div className='flex-1 time-ellipsis w-full'>{item.time}</div>
                    <div className='flex items-center'>
                      {/* <div> */}
                      <Balloon
                        align="t"
                        closable={false}
                        triggerType="hover"
                        trigger={
                          <span className='replay-btn ml-8'>{intl('apigw.api-debugging.DebugResponsePanel.PlayBack')}</span>
                        }
                      >
                        <span>{intl('apigw.api-debugging.DebugResponsePanel.OnlyBackfill')}</span>
                      </Balloon>
                      <div className='operation-divider ml-8 mr-4'></div>
                      <button
                        className="history-item-delete"
                        onClick={e => {
                          e.stopPropagation();
                          deleteHistoryItemByTimestamp(item?.details?.timestamp);
                        }}
                      >
                        <Icon type="ashbin" />
                      </button>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ))
        )}
      </div>
      <div className='p-12 w-full'>
        <Button onClick={() => { deleteHistoryByHttpRestId() }} className='w-full'>
          {intl('apigw.api-debugging.DebugResponsePanel.ClearRecord')}
        </Button>
      </div>
    </div>
  );
};
const ResponseTab = (props) => {
  const { request, response, debugLoading, isShowProcess, onIsShowProcessChange, selectedEnvId, cancelReqesut } = props;
  const [debugRequest, setDebugRequest] = useState<any>({});
  const [debugResponse, setDebugResponse] = useState<any>({});
  const [isHold, setIsHold] = useState(true);
  const [isNormal, setIsNormal] = useState(false);
  const [activeKey, setActiveKey] = useState(RequestParamsKey.body);
  const [isShowHistory, setIsShowHistory] = useState(true);
  const [contentType, setContentType] = useState('');

  // 新增：AI诊断按钮延迟显示,这里是因为发送请求后ai诊断的相关数据并没有立马ready,需要先等一会
  const [showAIDiagnose, setShowAIDiagnose] = useState(false);
  const [isExceed, setIsExceed] = useState(false);

  const ResponseTitle = () => {
    return (
      <>
        <div className="flex space-between">
          <span text-xs className='font-medium'>
            {intl('apigw.api-debugging.DebugResponsePanel.ResponseResults')}
          </span>
          <div>
            {
              debugLoading && (
                <Button size='small'
                  onClick={() => {
                    cancelReqesut()
                  }}>
                  <Icon type="close" />
                  <span>
                    {intl('apigw.api-debugging.DebugResponsePanel.CancelRequest')}
                  </span>
                </Button>
              )
            }
            <Button size='small'
              onClick={() => {
                setIsShowHistory(true)
                onIsShowProcessChange(false, ProcessType.history);
              }}
              className='ml-8'>
              <Icon type="clock" />
              <span> {intl('apigw.api-debugging.DebugResponsePanel.HistoricalRecords')}</span>
            </Button>
          </div>
        </div>
      </>
    )
  }
  const ActionBar = () => {
    return (
      <div className="action-bar flex items-center h-full">
        <Tag
          size="small"
          style={{
            border: 'none', borderRadius: 0, height: '24px', lineHeight: '24px', marginRight: '8px',
            ...getCodeStyle(debugResponse?.Code)
          }}
        >
          {debugResponse?.Code || '-'} {debugResponse.Msg}
        </Tag>
        {isShowProcess ? (
          <div><Icon type="ellipsis" onClick={()=>{ setIsShowHistory(true);onIsShowProcessChange(false, ProcessType.history);}}/></div>
        ) : (
          <>
            <Tag
              size="small"
              style={{ borderRadius: 0, height: '24px', lineHeight: '22px' }}>{intl('apigw.api-debugging.DebugResponsePanel.ResponseTime')}:{debugResponse?.Cost || '-'}ms</Tag>
          </>
        )}
        {
          debugResponse.Code !== 200 && !isExceed && <>
            {showAIDiagnose ? <Icon type="warning" style={{ color: '#D50B16' }} /> : <Icon type="loading" />}
            <span>{intl('apigw.api-debugging.RequestParamsTab.CurrentFailedPlease')}
              <span
                style={{ color: showAIDiagnose ? '#0064C8' : '#AAAAAA', cursor: showAIDiagnose ? 'pointer' : 'not-allowed', display: 'inline-flex', alignItems: 'center' }}
                onClick={() => {
                  if (showAIDiagnose) {
                    setIsShowHistory(false);
                    onIsShowProcessChange(true, ProcessType.aidiagnose);
                  }
                }}
              >
                {intl('apigw.api-debugging.RequestParamsTab.Diagnose')}
              </span>
            </span>
          </>
        }
        <div className='h-5 w-px bg-gray-300 mx-2 '></div>
        {/* {selectedEnvId && (<Button className={`${isShowProcess ? 'isOnlyIcon ' : ''}mr-8`} size='small' onClick={() => {
          setIsShowHistory(false);
          onIsShowProcessChange(true, ProcessType.process);
        }}
        >
          <Icon type="playcircle-fill" />
          {
            isShowProcess ? '' : <span className='ml-8'>{intl('apigw.api-debugging.DebugResponsePanel.ProcessObservation')}</span>
          }
        </Button>)
        } */}
        <Button className={`${isShowProcess ? 'isOnlyIcon ' : ''}mr-8`} size='small'
          onClick={() => {
            setIsShowHistory(true)
            onIsShowProcessChange(false, ProcessType.history);
          }}>
          <Icon type="clock" />
          {
            isShowProcess ? '' : <span className='ml-8'>{intl('apigw.api-debugging.DebugResponsePanel.HistoricalRecords')}</span>
          }
        </Button>

      </div>
    )
  }
  useEffect(() => {
    const _debugResponse = JSON.parse(JSON.stringify(response || {}));
    if (Reflect.has(_debugResponse, 'headers')) {
      const { headers = {} } = _debugResponse;
      Object.assign(_debugResponse, {
        Header: headers,
        Keys: Object.keys(headers) || [],
      });
    }
    const _debugRequest = JSON.parse(JSON.stringify(request));
    if (Reflect.has(_debugRequest, 'Header')) {
      const { Header = '{}' } = _debugRequest;
      Object.assign(_debugRequest, {
        Header: JSON.parse(Header),
        HeaderKeys: Object.keys(JSON.parse(Header)) || [],
      });
    }
    if (Reflect.has(_debugRequest, 'Query')) {
      const { Query = '{}' } = _debugRequest;
      Object.assign(_debugRequest, {
        Query: JSON.parse(Query),
        QueryKeys: Object.keys(JSON.parse(Query)) || [],
      });
    }
    if (Reflect.has(_debugRequest, 'BodyType')) {
      const { BodyType } = _debugRequest;
      if (BodyType === 'FORM' || BodyType === 'MULTIPART') {
        const { BodyForm = '{}' } = _debugRequest;
        Object.assign(_debugRequest, {
          BodyForm: JSON.parse(BodyForm),
          BodyFormKeys: Object.keys(JSON.parse(BodyForm)) || [],
        });
      }
    }
    const keys = Object.keys(_debugResponse);
    setIsHold(!keys.length);
    let _isNormal = false;
    if (includes(keys, 'headers') || includes(keys, 'body')) {
      _isNormal = true;
    }
    setIsNormal(_isNormal);
    setDebugRequest(_debugRequest);
    setDebugResponse(_debugResponse);
    setIsExceed(true);
    setShowAIDiagnose(false);
    let _contentType = _debugResponse?.Header?.['Content-Type']
    setContentType(_contentType)
    let isRrightCode = checkCode(response?.Code)
    if (response && Object.keys(response).length > 0 && isRrightCode) {
      setIsExceed(false);
      const timer = setTimeout(() => {
        setShowAIDiagnose(true);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [response]);

  const checkCode = (code) => {
    const isClientError = String(code).startsWith('4') || String(code).startsWith('5');
    return isClientError ? true : false
  }

  const copyBody = () => {
    copy(JSON.stringify(debugResponse.Body));
    try {
      Message.success(intl('copy.success'));
    } catch (error) {
      Message.error(intl('copy.fail'));
    }
  }
  return (
    <React.Fragment>
      <div className='router-debug-response'
        style={{ height: '100%' }}
      >
        {(() => {
          const stateMap = {
            loading: debugLoading,// 请求中
            empty: isHold,
            error: !isHold && !isNormal,// 请求失败
            normal: !isHold && isNormal
          };
          if (stateMap.error) {
            return (
              <div className="no-data">
                <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="64" height="64">
                  <path
                    d="M234.27218 32h58.36780969v179.99999344h-58.36874719V32zM583.99997844 32h58.27218562v179.99999344H583.99997844V32zM175.99999437 331.99998875h524.59216688v59.99999719H175.99999437v-59.99999719z m0 179.9999925h291.40780125v59.99999812H175.99999437v-59.99999812z m352.55998594 383.999985H32V79.99999812h767.99997v381.6477975C911.16871531 492.99216969 991.9999625 597.1043525 991.9999625 721.99997281c0 149.99999437-116.59218281 269.99998969-262.27217719 269.99998969a258.38436469 258.38436469 0 0 1-201.1199925-95.99999625z m212.35217906-443.75998312V138.31999625H91.08781062v699.35997188H492.36873219A277.72780125 277.72780125 0 0 1 467.40779562 721.99997281c0-149.99999437 116.59218281-269.99998969 262.31998969-269.99998875 3.744375 0 7.4878125 0.095625 11.18437407 0.23999906zM175.99999437 691.99997469h233.13561563v59.99999719H175.99999437v-59.99999719z m553.72779094-179.99999344c-110.73562031 0-203.9999925 95.99999625-203.9999925 209.99999156s93.26437125 209.99999156 203.9999925 209.9999925 203.9999925-95.99999625 203.99999156-209.9999925-93.26343375-209.99999156-203.99999156-209.99999156zM703.99997375 559.99997938h59.75999812v203.99999249H703.99997375V559.99997938z m59.75999812 239.99999062v59.75999812H703.99997375V799.99997h59.75999812z"
                    fill="#bfbfbf"
                  />
                </svg>
                <span>{debugResponse.Msg || intl('mse.request.error.name')}</span>
              </div>
            );
          }
          return (
            <div className='flex w-full' style={{ height: '100%' }}>
              <div className='flex-1 pr-16' style={{ overflowY: 'scroll' }}>
                {stateMap.loading && (
                  <div>
                    <ResponseTitle />
                    <div className="no-data" style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                      <Icon type="loading" /><div>
                        {intl('apigw.api-debugging.DebugResponsePanel.RequestProcessing')}
                      </div>
                    </div>
                  </div>)
                }
                {!stateMap.loading && stateMap.empty && (
                  <div>
                    <ResponseTitle />
                    <div className="no-data">
                      <svg xmlns="http://www.w3.org/2000/svg"
                        fill="none" version="1.1" width="161.4476318359375" height="118.6943359375" viewBox="0 0 161.4476318359375 118.6943359375"><g><g><g><ellipse cx="140.53929996490479" cy="40.373284339904785" rx="9.969231605529785" ry="9.969231605529785" fill-opacity="0" stroke-opacity="1" stroke="#EBECEC" fill="none" stroke-width="1.944000005722046" /></g><g><ellipse cx="140.53929996490479" cy="60.311760902404785" rx="9.969231605529785" ry="9.969231605529785" fill-opacity="0" stroke-opacity="1" stroke="#EBECEC" fill="none" stroke-width="1.944000005722046" /></g><g><ellipse cx="140.53929996490479" cy="80.25023746490479" rx="9.969231605529785" ry="9.969231605529785" fill-opacity="0" stroke-opacity="1" stroke="#EBECEC" fill="none" stroke-width="1.944000005722046" /></g><g><path d="M139.569020921875,-0.000033296874999977355L139.569025921875,0.969970703125L139.567025921875,0.969970703125L139.567025921875,110.631970703125L139.569025921875,110.631970703125L139.569020921875,111.601970703125L141.509034921875,111.601970703125L141.509024921875,110.631970703125L141.511024921875,110.631970703125L141.511024921875,0.969970703125L141.509024921875,0.969970703125L141.509034921875,-0.000033296874999977355L139.569020921875,-0.000033296874999977355Z" fill-rule="evenodd" fill="#F7F7F7" fill-opacity="1" /></g><g><path d="M119.630544359375,-0.000033296874999977355L119.630549359375,0.969970703125L119.628549359375,0.969970703125L119.628549359375,110.631970703125L119.630549359375,110.631970703125L119.630544359375,111.601970703125L121.570558359375,111.601970703125L121.570548359375,110.631970703125L121.572548359375,110.631970703125L121.572548359375,0.969970703125L121.570548359375,0.969970703125L121.570558359375,-0.000033296874999977355L119.630544359375,-0.000033296874999977355Z" fill-rule="evenodd" fill="#F7F7F7" fill-opacity="1" /></g><g><path d="M0.969970703125,61.28373L160.477970703125,61.28373L160.477970703125,61.28173L161.447970703125,61.28174L161.447970703125,59.341726L160.477970703125,59.341731L160.477970703125,59.339731L0.969970703125,59.339731L0.969970703125,59.341731L-0.000033296874999977355,59.341726L-0.000033296874999977355,61.28174L0.969970703125,61.28173L0.969970703125,61.28373Z" fill-rule="evenodd" fill="#F7F7F7" fill-opacity="1" /></g><g><path d="M0.969970703125,81.2222065625L160.477970703125,81.2222065625L160.477970703125,81.2202065625L161.447970703125,81.2202165625L161.447970703125,79.2802025625L160.477970703125,79.2802075625L160.477970703125,79.2782075625L0.969970703125,79.2782075625L0.969970703125,79.2802075625L-0.000033296874999977355,79.2802025625L-0.000033296874999977355,81.2202165625L0.969970703125,81.2202065625L0.969970703125,81.2222065625Z" fill-rule="evenodd" fill="#F7F7F7" fill-opacity="1" /></g><g><path d="M0.969970703125,41.3452534375L160.477970703125,41.3452534375L160.477970703125,41.3432534375L161.447970703125,41.3432634375L161.447970703125,39.4032494375L160.477970703125,39.4032544375L160.477970703125,39.4012544375L0.969970703125,39.4012544375L0.969970703125,39.4032544375L-0.000033296874999977355,39.4032494375L-0.000033296874999977355,41.3432634375L0.969970703125,41.3432534375L0.969970703125,41.3452534375Z" fill-rule="evenodd" fill="#F7F7F7" fill-opacity="1" /></g><g><ellipse cx="140.58513021469116" cy="60.29948568344116" rx="2.692307949066162" ry="2.692307949066162" fill="#FFFFFF" fill-opacity="1" /></g><g><ellipse cx="140.58513021469116" cy="80.23796224594116" rx="2.692307949066162" ry="2.692307949066162" fill="#FFFFFF" fill-opacity="1" /></g><g><ellipse cx="140.58513021469116" cy="40.36100912094116" rx="2.692307949066162" ry="2.692307949066162" fill="#FFFFFF" fill-opacity="1" /></g></g><g><path d="M30.877685546875,90.4687203125L130.569985546875,90.4687203125L130.569985546875,30.6533203125L30.877685546875,30.6533203125L30.877685546875,90.4687203125Z" fill-rule="evenodd" fill="#F7F7F7" fill-opacity="1" /></g><g><path d="M40.846923828125,80.49945859375L120.600823828125,80.49945859375L120.600823828125,40.62255859375L40.846923828125,40.62255859375L40.846923828125,80.49945859375Z" fill-rule="evenodd" fill="#EBECEC" fill-opacity="1" /></g><g transform="matrix(-0.7071067690849304,-0.7071067690849304,0.7071067690849304,-0.7071067690849304,53.99520655501692,259.41339582002547)"><path d="M80.723876953125,138.46242578125L100.662376953125,118.52392578125L100.662376953125,138.46242578125L80.723876953125,138.46242578125ZM92.45697695312501,133.60242578125L95.802376953125,130.25702578125L95.802376953125,133.60242578125L92.45697695312501,133.60242578125Z" fill-rule="evenodd" fill="#C4C5C7" fill-opacity="1" /></g><g><path d="M60.785400390625 100.43798828125C60.785400390625 100.43798828125 60.785400390625 100.43798828125 60.785400390625 100.43798828125L100.66232681274414 100.43798828125C100.66232681274414 100.43798828125 100.66232681274414 100.43798828125 100.66232681274414 100.43798828125L100.66232681274414 105.42260408401489C100.66232681274414 105.42260408401489 100.66232681274414 105.42260408401489 100.66232681274414 105.42260408401489L60.785400390625 105.42260408401489C60.785400390625 105.42260408401489 60.785400390625 105.42260408401489 60.785400390625 105.42260408401489Z" fill="#EBECEC" fill-opacity="1" /></g><g><path d="M40.846923828125 75.514892578125C40.846923828125 75.514892578125 40.846923828125 75.514892578125 40.846923828125 75.514892578125L120.60077667236328 75.514892578125C120.60077667236328 75.514892578125 120.60077667236328 75.514892578125 120.60077667236328 75.514892578125L120.60077667236328 80.49950838088989C120.60077667236328 80.49950838088989 120.60077667236328 80.49950838088989 120.60077667236328 80.49950838088989L40.846923828125 80.49950838088989C40.846923828125 80.49950838088989 40.846923828125 80.49950838088989 40.846923828125 80.49950838088989Z" fill="#C4C5C7" fill-opacity="1" /></g><g><ellipse cx="80.72387027740479" cy="59.065667152404785" rx="9.969231605529785" ry="9.969231605529785" fill-opacity="0" stroke-opacity="1" stroke="#FFFFFF" fill="none" stroke-width="4" /></g><g transform="matrix(-1,0,0,1,163.53994750976562,0)"><path d="M81.76997375488281,55.74560546875L83.76997375488281,55.74560546875L83.76997375488281,57.34042546875L83.3837637548828,60.54437546875L82.1469857548828,60.54437546875L81.76997375488281,57.34042546875L81.76997375488281,55.74560546875ZM81.83893935488281,61.03141546875L83.70560375488282,61.03141546875L83.70560375488282,62.74560546875L81.83893935488281,62.74560546875L81.83893935488281,61.03141546875Z" fill="#FFFFFF" fill-opacity="1" /></g></g></svg>
                      <span>{intl('@ali/widget-edas-microgw::widget.route.test.send_tip')}</span>
                    </div>
                  </div>
                )}
                {!stateMap.loading && stateMap.normal && (
                  <Tab
                    shape="capsule"
                    size="small"
                    activeKey={activeKey}
                    lazyLoad={false}
                    onChange={(key: RequestParamsKey) => setActiveKey(key)}
                    extra={<ActionBar />}
                  >
                    <Tab.Item
                      title={RequestParamsKey.body} key={RequestParamsKey.body}>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', height: '100%' }}>
                        {!debugResponse.Body ? (
                          <div
                            className="content-no-data">
                            This response does not have a body
                          </div>
                        ) : (<>
                          <div style={{ borderRadius: '2px', backgroundColor: '#EFF3F8', height: '32px' }} className='flex justify-between items-center pl-16 pr-16 mt-12 pt-6 pb-6'>
                            <div style={{ color: '#555555' }}>{extractContentType(contentType)}</div>
                            <button
                              onClick={copyBody}>
                              <Icon type="copy" /><span>{intl('apigw.apiAi.AIModelDebug.Output.Copy')}</span>
                            </button>
                          </div>
                          <CodeMirrorEditor
                            value={debugResponse.Body}
                            language={'json'}
                            lineWrapping
                            isLint={false}
                            height="100%"
                            theme={'light'}
                          />
                        </>)
                        }
                      </div>
                    </Tab.Item>
                    <Tab.Item title={RequestParamsKey.header} key={RequestParamsKey.header}>
                      {!debugResponse.Header ? (
                        <div
                          className="content-no-data">
                          This response does not have a header
                        </div>
                      ) : (
                        <div className="mt-12" style={{ overflow: 'auto', height: '100%' }}>
                          {debugResponse.Keys &&
                            debugResponse.Keys.map((key) => (
                              <div className="header" key={key}>
                                <span className="key">{key[0].toUpperCase() + key.substr(1)}:</span>
                                <span className="value">{debugResponse.Header[key]}</span>
                              </div>
                            ))}
                        </div>
                      )}
                    </Tab.Item>
                  </Tab>
                )}
              </div>
              <HistoryList
                isShowHistory={isShowHistory}
                setIsShowHistory={setIsShowHistory}
              ></HistoryList>
            </div>
          )
        })()}
      </div>
    </React.Fragment>
  );
}

export default ResponseTab;
