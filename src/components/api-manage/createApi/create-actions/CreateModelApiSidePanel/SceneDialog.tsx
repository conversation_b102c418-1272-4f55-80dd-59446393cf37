import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog, Icon, intl } from '@ali/cnd';
import { map } from 'lodash';
import { CREATE_API_MODEL_CATEGORY } from '../components/ProtocolSelect/options';
import { getVersionCompare } from '~/utils';

export const SCENE_LIST = [
  {
    type: CREATE_API_MODEL_CATEGORY.Text,
    title: intl('apigw.components.SceneDialog.TextGeneration'),
    decs: intl('apigw.components.SceneDialog.TextGenerationDesc'),
    icon: 'https://img.alicdn.com/imgextra/i4/O1CN01MlrQ3x1nonf53Axug_!!6000000005137-2-tps-96-96.png',
  },
  {
    type: CREATE_API_MODEL_CATEGORY.Image,
    title: intl('apigw.components.SceneDialog.ImageGeneration'),
    decs: intl('apigw.components.SceneDialog.ImageGenerationDesc'),
    icon: 'https://img.alicdn.com/imgextra/i4/O1CN01JhUQUi1upRWgowRiZ_!!6000000006086-55-tps-24-24.svg',
  },
  {
    type: CREATE_API_MODEL_CATEGORY.Video,
    title: intl('apigw.components.SceneDialog.VideoGeneration'),
    decs: intl('apigw.components.SceneDialog.VideoGenerationDesc'),
    icon: 'https://img.alicdn.com/imgextra/i2/O1CN01skQ4OB1QJRNdZmwYm_!!6000000001955-2-tps-96-96.png',
  },
  {
    type: CREATE_API_MODEL_CATEGORY.Audio,
    title: intl('apigw.components.SceneDialog.SpeechSynthesis'),
    decs: intl('apigw.components.SceneDialog.SpeechSynthesisDesc'),
    icon: 'https://img.alicdn.com/imgextra/i2/O1CN01cm3JmK1h2M4bzd3yO_!!6000000004219-2-tps-96-96.png',
  },
  {
    type: CREATE_API_MODEL_CATEGORY.Rerank,
    title: intl('apigw.components.SceneDialog.TextRerank'),
    decs: intl('apigw.components.SceneDialog.TextRerankDesc'),
    icon: 'https://img.alicdn.com/imgextra/i2/O1CN01Rvu7Nu27hHTUEH84j_!!6000000007828-55-tps-24-24.svg',
  },
  {
    type: CREATE_API_MODEL_CATEGORY.Embedding,
    title: intl('apigw.components.SceneDialog.Embedding'),
    decs: intl('apigw.components.SceneDialog.EmbeddingDesc'),
    icon: 'https://img.alicdn.com/imgextra/i1/O1CN01J8mUDX1pus8zMH9Dv_!!6000000005421-2-tps-96-96.png',
  },
  {
    type: CREATE_API_MODEL_CATEGORY.Others,
    title: intl('apigw.components.SceneDialog.Others'),
    decs: intl('apigw.components.SceneDialog.OthersDesc'),
    icon: 'https://img.alicdn.com/imgextra/i1/O1CN01J8mUDX1pus8zMH9Dv_!!6000000005421-2-tps-96-96.png',
  },
];

const SceneDialog = ({ isVisible, handleClose, handleCreate, engineVersion }) => {
  const isDisabled = !getVersionCompare(engineVersion, '2.1.6');
  return (
    <Dialog
      title={intl('apigw.components.SceneDialog.SelectUseCase')}
      visible={isVisible}
      onClose={handleClose}
      shouldUpdatePosition
      style={{ width: '960px' }}
      footerActions={[]}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          alignContent: 'flex-start',
          marginTop: 24,
        }}
      >
        {map(SCENE_LIST, (item, i) => (
          <div key={item.type} className="scene-item">
            <img src={item.icon} height={32} width={32} />
            <span className="title">{item.title}</span>
            <div className="des">{item.decs}</div>
            <div className="flex space-between">
              {item.type !== CREATE_API_MODEL_CATEGORY.Text && isDisabled ? (
                <div style={{ color: '#808080', lineHeight: '32px' }}>
                  {intl(
                    'apigw.create-actions.CreateModelApiSidePanel.SceneDialog.InThisScenarioYouNeed',
                  )}
                </div>)
                : <span></span>
              }
              <Button
                type="primary"
                onClick={() => {
                  handleCreate(item.type);
                }}
                disabled={item.type !== CREATE_API_MODEL_CATEGORY.Text && isDisabled}
              >
                <Icon type="plus" />
                {intl('apigw.components.SceneDialog.Create')}
              </Button>
            </div>
          </div>
        ))}
      </div>
    </Dialog>
  );
};

export default SceneDialog;
