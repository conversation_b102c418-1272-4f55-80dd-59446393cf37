import { useEffect, useState, useRef } from 'react';
import { get, uniqBy, concat, find, isEmpty, split, forEach, uniq, size, map } from 'lodash';
import services, { request } from '~/utils/services';
import { useHistoryDebugContext } from '../components/HistoryContext';
import { ContentTypeKey } from '~/components/api-manage/interfaceList/components/api-operations-debug/RequestParamsTab';
import { getAuthInfo } from '~/components/api-manage/interfaceList/components/api-resource-acitons/api-debugging/components/DebugResponsePanel/debugCommon';
import { Message } from '@ali/cnd';

const CLOSE_MOCK_TIP = 'apigw.components.api-operations-debug.OperationDebug.TheMockConfigurationIsNot';
const NO_RESPONSE_TIP = 'this response has no body';
const ProtocolEnum = {
  All: 'ALL',
  Http: 'HTTP',
  Https: 'HTTPS',
};
enum ApiVersionSchema {
  Query = 'Query',
  Header = 'Header',
  Path = 'Path',
}

export function useRestDebug(props) {
  const { resourceData, selectedEnvId, apiId, httpApiInfo, isRest, gatewayId } = props;
  const [response, updateResponse] = useState<any>({});
  const [requestData, updateRequest] = useState({});
  const [isShowProcess, setIsShowProcess] = useState(false);
  const [requestParams, setRequest] = useState<any>({});
  const [inputUrl, setInputUrl] = useState('');
  const [initUrl, setInitUrl] = useState('');
  const [inputValidate, setInputValidate] = useState(true);
  const [initValues, setInitValues] = useState<any>({});
  const [apiDetail, setApiDetail] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const requestParamsRef = useRef<any>(null);
  const [diagnoseType, setDiagnoseType] = useState('');
  const { updateHttpRestId, addEntry } = useHistoryDebugContext();
  const [gatewayRequestId, setGatewayRequestId] = useState('');
  const containerRef = useRef<any>(null);
  const hasAbortRef = useRef(false);
  const [debugLoading, setDebugLoading] = useState(false);

  useEffect(() => {
    services
      .GetHttpApi({
        params: {
          httpApiId: apiId,
        },
      })
      .then((api) => setApiDetail(api));
  }, []);

  useEffect(() => {
    getRestApiInfo(selectedEnvId);
  }, [inputUrl, selectedEnvId]);

  useEffect(() => {
    setRequest(resourceData?.request || {});
    updateHttpRestId(resourceData?.operationId || '');
  }, [resourceData]);

  useEffect(() => {
    if (!apiDetail) return;
    fillVersionInfo();
    getPath()
  }, [apiDetail, resourceData]);

  // 构建path，如果是path或者query的话就把相关信息放上
  const getPath = () => {
    let basepath = get(apiDetail, 'basePath', '') === '/' ? '' : get(apiDetail, 'basePath', '');
    let path = resourceData?.path || '';
    let url = '';
    let {
      enable,
      scheme,
      version = '',
      queryName = '',
      headerName = '',
    } = get(apiDetail, 'versionInfo') || {};
    if (enable) {
      scheme === 'Path' && (url = `${basepath}${version ? '/' + version : ''}${path}`);
      scheme === 'Header' && (url = `${basepath}${path}`);
      scheme === 'Query' && (url = `${basepath}${path}?${queryName}=${version}`);
    } else {
      url = `${basepath}${path}`;
    }
    const filledUrl = fillPathInfo(url);
    setInputUrl(filledUrl);
    setInitUrl(url);
    validatePath(filledUrl, url);
  }

  const fillPathInfo = (url: string) => {
    let pathParameters = resourceData?.request?.pathParameters || [];
    const oldPathSplit = split(url || '', '?');
    let pathSplit = split(oldPathSplit[0], '/');
    forEach(pathSplit, (item, index) => {
      let match = extractDynamicPath(item);
      if (size(match) === 0) {
      } else {
        const curPathParam = find(pathParameters, (item) => item.name === match[0]) || {};
        pathSplit[index] = curPathParam.defaultValue || curPathParam.exampleValue || pathSplit[index];
      }
    });
    const path = oldPathSplit[1] ? `${pathSplit.join('/')}?${oldPathSplit[1]}` : pathSplit.join('/');
    return path || '';
  };

  // 把版本为header的信息填充
  const fillVersionInfo = () => {
    if (!apiDetail?.versionInfo) return;
    const {
      scheme: versioningScheme,
      queryName: versionQueryName,
      version,
      headerName: versionHeaderName,
    } = apiDetail.versionInfo;
    const isQueryVersion = versioningScheme === ApiVersionSchema.Query;
    const isHeaderVersion = versioningScheme === ApiVersionSchema.Header;
    const versionPosition = isHeaderVersion ? 'headerParameters' : null;
    const versionKey = isHeaderVersion ? versionHeaderName : '';
    if (!versionPosition) return;
    if (
      find(
        requestParams[versionPosition],
        (item) => item.name === versionKey && item.value === version,
      )
    ) {
      setRequest({ ...requestParams });
    } else if (
      find(
        requestParams[versionPosition],
        (item) => item.name === versionKey && item.value !== version,
      )
    ) {
      setRequest({
        ...requestParams,
        [versionPosition]: map(requestParams[versionPosition], (item) => {
          if (item.name === versionKey) {
            item.value = version;
          }
          return item;
        }),
      });
    } else {
      setRequest({
        ...requestParams,
        [versionPosition]: [
          ...(requestParams[versionPosition] || []),
          {
            name: versionKey,
            value: version,
            exampleValue: version,
            required: true,
          },
        ],
      });
    }
  };

  const getRestApiInfo = async (environmentId: string) => {
    setIsLoading(true);
    let _initValues: any = {};
    let domainRef = new Map();
    const selectEnvInfo = (apiDetail?.environments || []).find(
      (env: any) => env.environmentId === environmentId
    );
    if (!selectEnvInfo) return;
    const domains = uniqBy(
      concat(selectEnvInfo.subDomains, selectEnvInfo.customDomains),
      'domainId'
    ).filter(Boolean);
    const protocols = Array.from(
      new Set(['HTTP', ...(selectEnvInfo.customDomains || []).map((item: any) => item.protocol)]),
    );
    domainRef = await getDomainData(domains);
    let _authInfo: any = {}
    if (resourceData?.enableAuth && resourceData?.authConfig?.authType == "Apikey") {
      _authInfo = await getAuthInfo({ selectedEnvId, routeId: resourceData?.operationId, isRest });
    }
    let _headerParameters = (requestParams?.headerParameters || resourceData?.request?.headerParameters||[]).map(item => ({
      ...item, value: item?.value || item.defaultValue
    }));
    let _queryParameters = (resourceData?.request?.queryParameters || []).map(item => ({
      ...item, value: item.defaultValue
    }));
    if (_authInfo?.Header) {
      _headerParameters = [_authInfo?.Header, ..._headerParameters]
    }
    if (_authInfo?.Query) {
      _queryParameters = [_authInfo?.Query, ..._queryParameters]
    }
    _initValues = {
      Protocol: protocols[0],
      domains,
      Path: inputUrl,
      initUrl,
      Method: resourceData.method,
      domainRef: domainRef,
      environmentId: selectedEnvId,
      Header: _headerParameters,
      Query: _queryParameters,
      PathParams: resourceData?.request?.pathParameters,
      Body: resourceData?.request?.body || {},
    };
    setInitValues(_initValues);
    setIsLoading(false);
  };

  const getDomainData = async (domains: any[]) => {
    const _http: any[] = [];
    const _https: any[] = [];
    domains.forEach((domain) => {
      const { protocol, name, domainId } = domain;
      if (protocol === ProtocolEnum.Http) {
        _http.push({ label: name, value: domainId, ...domain });
      }
      if (protocol === ProtocolEnum.Https) {
        _https.push({ label: name, value: domainId, ...domain });
      }
    });
    const domainRef = new Map();
    domainRef
      .set(ProtocolEnum.Http, _http)
      .set(ProtocolEnum.Https, _https)
      .set(ProtocolEnum.All, domains);
    return Promise.resolve(domainRef);
  };

  const validatePath = (input?: string, init?: string) => {
    let bool = true;
    let keys: any = {};
    const userInput = input || inputUrl;
    const originUrl = init || initUrl;
    let pathParameters = resourceData?.request?.pathParameters || [];
    if (isEmpty(pathParameters) || !userInput) {
    } else {
      let inputUrlSplit = split(split(userInput, '?')[0], '/');
      let initUrlSplit = split(split(originUrl, '?')[0], '/');
      if (inputUrlSplit.length !== initUrlSplit.length) {
        setInputValidate(false);
        bool = false;
        return false;
      }
      forEach(initUrlSplit, (item, index) => {
        let match = extractDynamicPath(item);
        if (size(match) === 0) {
          if (item !== inputUrlSplit[index]) {
            setInputValidate(false);
            bool = false;
            return false;
          }
        } else {
          if (item !== inputUrlSplit[index]) {
            keys[match[0]] = inputUrlSplit[index];
          }
        }
      });
    }
    return bool ? keys : false;
  };

  const extractDynamicPath = (path = '') => {
    const patterns = [/\[([^\[\]]+)\]/g, /\{([^\{\}]+)\}/g, /:([^\/]+)/g];
    let dynamicFields: string[] = [];
    patterns.forEach((pattern) => {
      const matches = path.matchAll(pattern);
      for (const match of matches) {
        dynamicFields.push(match[1]);
      }
    });
    return uniq(dynamicFields);
  };


  const getRequestParams = () => {
    return requestParamsRef?.current?.getData() || {};
  };

  const sendDebugRequest = async (values: any) => {
    setIsShowProcess(false)
    let pathParameters = validatePath(values?.Path);
    if (pathParameters === false) {
      Message.error('Validate Error')
      return;
    }
    setDebugLoading(true);
    hasAbortRef.current = false;
    const requestTabParams: any = getRequestParams();
    const { Protocol, environmentId, Domain: fieldDomainId, Path, Method } = values;
    const Domain = fieldDomainId || undefined;
    const params: any = {};
    const { Header = [], Query = [] } = requestTabParams;
    if (Header && Header.length) {
      const _Header: any = {};
      for (let i = 0; i < Header.length; i++) {
        const kv = Header[i];
        const { name, value } = kv;
        if (!name && !value) continue;
        Reflect.set(_Header, name, value);
      }
      const keys = Object.keys(_Header);
      if (keys.length) {
        Object.assign(params, { Header: JSON.stringify(_Header) });
      }
    }
    if (Query && Query.length) {
      const _Query: any = {};
      for (let i = 0; i < Query.length; i++) {
        const kv = Query[i];
        const { name, value } = kv;
        if (!name && !value) continue;
        Reflect.set(_Query, name, value);
      }
      const keys = Object.keys(_Query);
      if (keys.length) {
        Object.assign(params, { Query: JSON.stringify(_Query) });
      }
    }
    const { Body = { BodyType: 'NONE', BodyForm: [], BodyJson: {}, BodyMultipart: [] } } = requestTabParams;
    const { BodyType, BodyForm, BodyJson, BodyMultipart } = Body;
    if (BodyType === 'FORM' && BodyForm && BodyForm.length) {
      const _BodyForm: any = {};
      for (let i = 0; i < BodyForm.length; i++) {
        const kv = BodyForm[i];
        const { name, value } = kv;
        if (!name && !value) continue;
        Reflect.set(_BodyForm, name, value);
      }
      const { BodyType } = Body;
      const keys = Object.keys(_BodyForm);
      if (keys.length) {
        Object.assign(params, { BodyType, BodyForm: JSON.stringify(_BodyForm) });
      }
    }
    if (BodyType === 'MULTIPART' && BodyMultipart && BodyMultipart.length) {
      const _BodyMultipart: any = {};
      for (let i = 0; i < BodyMultipart.length; i++) {
        const kv = BodyMultipart[i];
        const { name, value } = kv;
        if (!name && !value) continue;
        Reflect.set(_BodyMultipart, name, value);
      }
      const { BodyType } = Body;
      const keys = Object.keys(_BodyMultipart);
      if (keys.length) {
        Object.assign(params, { BodyType, BodyForm: JSON.stringify(_BodyMultipart) });
      }
    }
    if (BodyType === 'JSON' && BodyJson && BodyJson.length) {
      const { BodyType } = Body;
      Object.assign(params, { BodyType, BodyJson });
    }
    const contentTypeMapper = {
      FORM: ContentTypeKey.form,
      JSON: ContentTypeKey.json,
      MULTIPART: ContentTypeKey.multipart,
    };
    let requestParamsObj: any = { httpApiId: apiId };
    let requestBody: any = {
      queryParams: params.Query ? JSON.parse(params.Query) : undefined,
      headers: params.Header ? JSON.parse(params.Header) : undefined,
      contentType: contentTypeMapper[params.BodyType],
      bodyJson: params.BodyJson,
      bodyForm: params.BodyForm ? JSON.parse(params.BodyForm) : undefined,
    };
    const { scheme: versioningScheme, queryName, version } = apiDetail.versionInfo || {};
    const isQueryVersion = versioningScheme === ApiVersionSchema.Query;
    if (isQueryVersion) {
      requestBody.queryParams = {
        ...requestBody.queryParams,
        [queryName]: version,
      };
    }
    requestBody.pathParams = pathParameters
    requestBody.restApiConfig = {
      operationId: resourceData?.operationId,
      domainId: Domain,
      protocol: Protocol,
      environmentId,
    };
    const currentEnvInfo = find(
      get(apiDetail, 'environments'),
      (item: any) => item.environmentId === environmentId,
    );
    const isMockBackendScene = get(currentEnvInfo, 'backendScene') === 'Mock';
    let res: any = await request({
      action: 'InvokeHttpApi',
      product: 'APIG',
      rawResponseData: true,
    })({
      params: requestParamsObj,
      content: requestBody,
      customErrorHandle: (err: any, data: any, callback: any) => {
        setDebugLoading(false);
        callback();
      },
    });
    if (hasAbortRef.current) return;
    const requestId = res?.data?.headers?.['X-Request-Id'];
    res = res.data || {};
    res = {
      ...res,
      Msg: res?.httpCode == 200 ? 'OK' : null,
      Header: JSON.stringify(res?.headers || {}),
      Method: res?.method || '',
      Protocol: res?.protocol || '',
      Code: res?.httpCode,
      Body: res?.body,
      Cost: res?.cost || '',
      Url: res?.url || '',
    };
    if (!get(res, 'Body')) {
      const isCloseMock =
        isMockBackendScene &&
        !get(resourceData, 'mock.enable') &&
        !get(resourceData, 'mock.responseContent');
      res = {
        ...res,
        Body: isCloseMock ? CLOSE_MOCK_TIP : NO_RESPONSE_TIP,
      };
    }
    setGatewayRequestId(requestId);
    updateResponse(res);
    updateRequest(params);
    setDebugLoading(false);
    const entry = {
      timestamp: Date.now(),
      requestTabParams: requestTabParams,
      responseResult: res?.Code,
      domainPath: { Method, Path, Domain, Protocol },
    };
    addEntry(resourceData?.operationId, entry, apiId, gatewayId);
  };
  const cancelReqesut = () => {
    hasAbortRef.current = true;
    setDebugLoading(false)
  }
  return {
    response,
    requestData,
    isShowProcess,
    setIsShowProcess,
    requestParams,
    setRequest,
    inputUrl,
    setInputUrl,
    initUrl,
    setInitUrl,
    inputValidate,
    setInputValidate,
    initValues,
    setInitValues,
    apiDetail,
    setApiDetail,
    isLoading,
    setIsLoading,
    requestParamsRef,
    diagnoseType,
    setDiagnoseType,
    gatewayRequestId,
    setGatewayRequestId,
    containerRef,
    debugLoading,
    cancelReqesut,
    setDebugLoading,
    fillPathInfo,
    fillVersionInfo,
    getRestApiInfo,
    getDomainData,
    validatePath,
    extractDynamicPath,
    getRequestParams,
    sendDebugRequest,
    selectedEnvId,
    apiId,
    httpApiInfo,
    resourceData,
    gatewayId,
  };
} 